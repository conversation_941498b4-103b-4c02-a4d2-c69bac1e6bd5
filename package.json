{"name": "@nger/weibo", "version": "1.0.0", "description": "", "main": "index.js", "bin": {"weibo-master": "dist/master.js", "weibo-node": "dist/node.js"}, "scripts": {"start": "ts-node -r tsconfig-paths/register src/consumer.ts", "install": "ts-node -r tsconfig-paths/register src/install.ts", "dev:master": "ts-node -r tsconfig-paths/register src/master.ts", "dev:dead": "ts-node -r tsconfig-paths/register src/dead.ts", "test": "ts-node -r tsconfig-paths/register src/framework/rabbitmq/rabbitmq.util.ts", "search": "ts-node -r tsconfig-paths/register src/framework/spider/spider.ts", "tsc": "tsc", "prod": "pnpm run tsc && pm2-runtime run config/processes.json", "master": "pm2 start -n=master -i 1 dist/master.js", "consumer": "pm2 start -n=consumer -i 2 dist/consumer.js", "dead": "pm2 start -n=master -i 1 dist/dead.js", "restart": "pm2 restart main", "kill": "pm2 kill", "demo": "ts-node -r tsconfig-paths/register src/v2/index.ts", "test:redis": "ts-node -r tsconfig-paths/register src/framework/redis/redis.test.ts", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "frontend:preview": "cd frontend && npm run preview"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@nger/core": "1.10.38", "@nger/entities": "1.1.14", "@nger/http": "4.0.76", "@nger/install": "1.0.55", "@nger/oauth2": "1.0.3", "@nger/puppeteer": "1.0.12", "@nger/rabbitmq": "1.0.52", "@nger/redis": "1.0.6", "@nger/rest": "1.0.188", "@nger/sirv": "^1.0.44", "@nger/template": "1.0.2", "@nger/typeorm": "1.0.47", "@nger/utils": "1.0.3", "@nger/weibo": "file:", "@nger/zookeeper": "1.0.52", "@redis/bloom": "^1.2.0", "@redis/client": "^1.5.6", "@types/progress": "^2.0.5", "@types/proxy-from-env": "^1.0.1", "amqplib": "^0.10.3", "axios": "^1.3.5", "cheerio": "1.0.0-rc.12", "cosmiconfig": "^8.1.3", "dotenv": "^16.0.3", "encodeurl": "^1.0.2", "fs-extra": "^11.1.1", "https-proxy-agent": "^5.0.1", "koa": "^2.14.2", "logstash": "^5.0.2", "multihashing-async": "^2.1.4", "node-cmd": "^5.0.0", "node-os-utils": "^1.3.7", "node-schedule": "^2.1.1", "node-xlsx": "^0.21.2", "os-utils": "^0.0.14", "pg": "^8.10.0", "pm2": "^5.3.0", "progress": "^2.0.3", "proxy-from-env": "^1.1.0", "puppeteer": "^19.9.1", "puppeteer-core": "^19.9.1", "redis": "^4.6.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "typeorm": "^0.3.15", "ws": "^8.13.0"}, "devDependencies": {"@types/amqplib": "^0.10.1", "@types/fs-extra": "^11.0.1", "@types/node": "^18.15.11", "@types/node-os-utils": "^1.3.1", "@types/node-schedule": "^2.1.0", "@types/os-utils": "^0.0.1", "@types/puppeteer": "^7.0.4", "@types/ws": "^8.5.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.4"}}