# 舆情监控大屏幕系统

基于 React + TypeScript + Vite + TailwindCSS 构建的现代化舆情监控大屏幕展示系统。

## 🚀 特性

- **现代化技术栈**: React 18 + TypeScript + Vite + TailwindCSS
- **实时数据展示**: WebSocket 实时数据更新
- **丰富的图表组件**: 基于 ECharts 的多种数据可视化图表
- **响应式设计**: 适配不同分辨率的大屏幕显示
- **优雅的动画效果**: Framer Motion 提供流畅的交互动画
- **状态管理**: Zustand 轻量级状态管理
- **模块化架构**: 清晰的项目结构和组件设计

## 📊 功能模块

### 数据可视化
- **时间序列图**: 情感趋势分析
- **饼图**: 情感分布统计
- **词云图**: 关键词分析
- **柱状图**: 热点话题排行
- **地图**: 地理位置分布热力图

### 实时监控
- **实时数据更新**: WebSocket 连接实时推送数据
- **系统状态监控**: CPU、内存、网络状态监控
- **数据源状态**: 微博、知乎、新闻等数据源连接状态
- **自动刷新**: 可配置的自动数据刷新机制

### 交互功能
- **时间范围选择**: 1小时、6小时、24小时、7天、30天
- **数据筛选**: 多维度数据筛选功能
- **图表交互**: 图表缩放、工具箱等交互功能
- **响应式布局**: 适配不同屏幕尺寸

## 🛠️ 技术栈

- **前端框架**: React 18
- **开发语言**: TypeScript
- **构建工具**: Vite
- **样式框架**: TailwindCSS
- **图表库**: ECharts + echarts-for-react
- **动画库**: Framer Motion
- **状态管理**: Zustand
- **HTTP 客户端**: Axios
- **WebSocket**: Socket.IO Client
- **路由**: React Router
- **图标**: Lucide React

## 📦 安装和运行

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 组件
│   │   ├── charts/        # 图表组件
│   │   ├── ui/            # UI 组件
│   │   ├── Header.tsx     # 头部组件
│   │   ├── Sidebar.tsx    # 侧边栏组件
│   │   ├── Footer.tsx     # 底部组件
│   │   └── Layout.tsx     # 布局组件
│   ├── hooks/             # 自定义 Hooks
│   ├── pages/             # 页面组件
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── types/             # 类型定义
│   ├── utils/             # 工具函数
│   ├── App.tsx            # 主应用组件
│   └── main.tsx           # 入口文件
├── .env                   # 环境变量
├── package.json           # 项目配置
├── tailwind.config.js     # TailwindCSS 配置
├── tsconfig.json          # TypeScript 配置
└── vite.config.ts         # Vite 配置
```

## 🎨 主要组件

### 图表组件
- `TimeSeriesChart`: 时间序列图表
- `SentimentPieChart`: 情感分析饼图
- `WordCloudChart`: 词云图
- `HotTopicsChart`: 热点话题柱状图
- `LocationHeatMap`: 地理位置热力图

### UI 组件
- `MetricCard`: 指标卡片
- `AlertPanel`: 警告面板
- `LoadingSpinner`: 加载动画

### 布局组件
- `Layout`: 主布局
- `Header`: 头部导航
- `Sidebar`: 侧边栏
- `Footer`: 底部状态栏

## 🔧 配置

### 环境变量
```bash
VITE_API_BASE_URL=http://localhost:8080/api
VITE_WS_URL=ws://localhost:8080
VITE_APP_TITLE=舆情监控大屏幕系统
```

### 主题配置
系统支持深色主题，可在 `tailwind.config.js` 中自定义颜色配置。

## 📱 响应式设计

系统针对大屏幕显示进行了优化，支持以下分辨率：
- 1920x1080 (Full HD)
- 2560x1440 (2K)
- 3840x2160 (4K)

## 🚀 部署

### 构建
```bash
npm run build
```

### 部署到服务器
将 `dist` 目录下的文件部署到 Web 服务器即可。

### Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

MIT License
