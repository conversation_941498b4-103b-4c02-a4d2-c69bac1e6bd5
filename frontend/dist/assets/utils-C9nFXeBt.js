import{c as En,g as Sn}from"./vendor-CJBOjUNh.js";var Pt={exports:{}};(function(n,e){(function(t,r){n.exports=r()})(En,function(){var t=1e3,r=6e4,s=36e5,i="millisecond",o="second",a="minute",h="hour",u="day",l="week",f="month",O="quarter",A="year",y="date",_="Invalid Date",g=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,v=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,N={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(w){var m=["th","st","nd","rd"],d=w%100;return"["+w+(m[(d-20)%10]||m[d]||m[0])+"]"}},x=function(w,m,d){var b=String(w);return!b||b.length>=m?w:""+Array(m+1-b.length).join(d)+w},W={s:x,z:function(w){var m=-w.utcOffset(),d=Math.abs(m),b=Math.floor(d/60),p=d%60;return(m<=0?"+":"-")+x(b,2,"0")+":"+x(p,2,"0")},m:function w(m,d){if(m.date()<d.date())return-w(d,m);var b=12*(d.year()-m.year())+(d.month()-m.month()),p=m.clone().add(b,f),S=d-p<0,R=m.clone().add(b+(S?-1:1),f);return+(-(b+(d-p)/(S?p-R:R-p))||0)},a:function(w){return w<0?Math.ceil(w)||0:Math.floor(w)},p:function(w){return{M:f,y:A,w:l,d:u,D:y,h,m:a,s:o,ms:i,Q:O}[w]||String(w||"").toLowerCase().replace(/s$/,"")},u:function(w){return w===void 0}},L="en",U={};U[L]=N;var Y="$isDayjsObject",G=function(w){return w instanceof Se||!(!w||!w[Y])},te=function w(m,d,b){var p;if(!m)return L;if(typeof m=="string"){var S=m.toLowerCase();U[S]&&(p=S),d&&(U[S]=d,p=S);var R=m.split("-");if(!p&&R.length>1)return w(R[0])}else{var C=m.name;U[C]=m,p=C}return!b&&p&&(L=p),p||!b&&L},$=function(w,m){if(G(w))return w.clone();var d=typeof m=="object"?m:{};return d.date=w,d.args=arguments,new Se(d)},k=W;k.l=te,k.i=G,k.w=function(w,m){return $(w,{locale:m.$L,utc:m.$u,x:m.$x,$offset:m.$offset})};var Se=function(){function w(d){this.$L=te(d.locale,null,!0),this.parse(d),this.$x=this.$x||d.x||{},this[Y]=!0}var m=w.prototype;return m.parse=function(d){this.$d=function(b){var p=b.date,S=b.utc;if(p===null)return new Date(NaN);if(k.u(p))return new Date;if(p instanceof Date)return new Date(p);if(typeof p=="string"&&!/Z$/i.test(p)){var R=p.match(g);if(R){var C=R[2]-1||0,B=(R[7]||"0").substring(0,3);return S?new Date(Date.UTC(R[1],C,R[3]||1,R[4]||0,R[5]||0,R[6]||0,B)):new Date(R[1],C,R[3]||1,R[4]||0,R[5]||0,R[6]||0,B)}}return new Date(p)}(d),this.init()},m.init=function(){var d=this.$d;this.$y=d.getFullYear(),this.$M=d.getMonth(),this.$D=d.getDate(),this.$W=d.getDay(),this.$H=d.getHours(),this.$m=d.getMinutes(),this.$s=d.getSeconds(),this.$ms=d.getMilliseconds()},m.$utils=function(){return k},m.isValid=function(){return this.$d.toString()!==_},m.isSame=function(d,b){var p=$(d);return this.startOf(b)<=p&&p<=this.endOf(b)},m.isAfter=function(d,b){return $(d)<this.startOf(b)},m.isBefore=function(d,b){return this.endOf(b)<$(d)},m.$g=function(d,b,p){return k.u(d)?this[b]:this.set(p,d)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(d,b){var p=this,S=!!k.u(b)||b,R=k.p(d),C=function(re,I){var Z=k.w(p.$u?Date.UTC(p.$y,I,re):new Date(p.$y,I,re),p);return S?Z:Z.endOf(u)},B=function(re,I){return k.w(p.toDate()[re].apply(p.toDate("s"),(S?[0,0,0,0]:[23,59,59,999]).slice(I)),p)},F=this.$W,q=this.$M,V=this.$D,ae="set"+(this.$u?"UTC":"");switch(R){case A:return S?C(1,0):C(31,11);case f:return S?C(1,q):C(0,q+1);case l:var ne=this.$locale().weekStart||0,fe=(F<ne?F+7:F)-ne;return C(S?V-fe:V+(6-fe),q);case u:case y:return B(ae+"Hours",0);case h:return B(ae+"Minutes",1);case a:return B(ae+"Seconds",2);case o:return B(ae+"Milliseconds",3);default:return this.clone()}},m.endOf=function(d){return this.startOf(d,!1)},m.$set=function(d,b){var p,S=k.p(d),R="set"+(this.$u?"UTC":""),C=(p={},p[u]=R+"Date",p[y]=R+"Date",p[f]=R+"Month",p[A]=R+"FullYear",p[h]=R+"Hours",p[a]=R+"Minutes",p[o]=R+"Seconds",p[i]=R+"Milliseconds",p)[S],B=S===u?this.$D+(b-this.$W):b;if(S===f||S===A){var F=this.clone().set(y,1);F.$d[C](B),F.init(),this.$d=F.set(y,Math.min(this.$D,F.daysInMonth())).$d}else C&&this.$d[C](B);return this.init(),this},m.set=function(d,b){return this.clone().$set(d,b)},m.get=function(d){return this[k.p(d)]()},m.add=function(d,b){var p,S=this;d=Number(d);var R=k.p(b),C=function(q){var V=$(S);return k.w(V.date(V.date()+Math.round(q*d)),S)};if(R===f)return this.set(f,this.$M+d);if(R===A)return this.set(A,this.$y+d);if(R===u)return C(1);if(R===l)return C(7);var B=(p={},p[a]=r,p[h]=s,p[o]=t,p)[R]||1,F=this.$d.getTime()+d*B;return k.w(F,this)},m.subtract=function(d,b){return this.add(-1*d,b)},m.format=function(d){var b=this,p=this.$locale();if(!this.isValid())return p.invalidDate||_;var S=d||"YYYY-MM-DDTHH:mm:ssZ",R=k.z(this),C=this.$H,B=this.$m,F=this.$M,q=p.weekdays,V=p.months,ae=p.meridiem,ne=function(I,Z,de,Re){return I&&(I[Z]||I(b,S))||de[Z].slice(0,Re)},fe=function(I){return k.s(C%12||12,I,"0")},re=ae||function(I,Z,de){var Re=I<12?"AM":"PM";return de?Re.toLowerCase():Re};return S.replace(v,function(I,Z){return Z||function(de){switch(de){case"YY":return String(b.$y).slice(-2);case"YYYY":return k.s(b.$y,4,"0");case"M":return F+1;case"MM":return k.s(F+1,2,"0");case"MMM":return ne(p.monthsShort,F,V,3);case"MMMM":return ne(V,F);case"D":return b.$D;case"DD":return k.s(b.$D,2,"0");case"d":return String(b.$W);case"dd":return ne(p.weekdaysMin,b.$W,q,2);case"ddd":return ne(p.weekdaysShort,b.$W,q,3);case"dddd":return q[b.$W];case"H":return String(C);case"HH":return k.s(C,2,"0");case"h":return fe(1);case"hh":return fe(2);case"a":return re(C,B,!0);case"A":return re(C,B,!1);case"m":return String(B);case"mm":return k.s(B,2,"0");case"s":return String(b.$s);case"ss":return k.s(b.$s,2,"0");case"SSS":return k.s(b.$ms,3,"0");case"Z":return R}return null}(I)||R.replace(":","")})},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(d,b,p){var S,R=this,C=k.p(b),B=$(d),F=(B.utcOffset()-this.utcOffset())*r,q=this-B,V=function(){return k.m(R,B)};switch(C){case A:S=V()/12;break;case f:S=V();break;case O:S=V()/3;break;case l:S=(q-F)/6048e5;break;case u:S=(q-F)/864e5;break;case h:S=q/s;break;case a:S=q/r;break;case o:S=q/t;break;default:S=q}return p?S:k.a(S)},m.daysInMonth=function(){return this.endOf(f).$D},m.$locale=function(){return U[this.$L]},m.locale=function(d,b){if(!d)return this.$L;var p=this.clone(),S=te(d,b,!0);return S&&(p.$L=S),p},m.clone=function(){return k.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},w}(),yt=Se.prototype;return $.prototype=yt,[["$ms",i],["$s",o],["$m",a],["$H",h],["$W",u],["$M",f],["$y",A],["$D",y]].forEach(function(w){yt[w[1]]=function(m){return this.$g(m,w[0],w[1])}}),$.extend=function(w,m){return w.$i||(w(m,Se,$),w.$i=!0),$},$.locale=te,$.isDayjs=G,$.unix=function(w){return $(1e3*w)},$.en=U[L],$.Ls=U,$.p={},$})})(Pt);var Rn=Pt.exports;const Ks=Sn(Rn),Q=Object.create(null);Q.open="0";Q.close="1";Q.ping="2";Q.pong="3";Q.message="4";Q.upgrade="5";Q.noop="6";const Ae=Object.create(null);Object.keys(Q).forEach(n=>{Ae[Q[n]]=n});const Xe={type:"error",data:"parser error"},$t=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Ft=typeof ArrayBuffer=="function",Ut=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer,ct=({type:n,data:e},t,r)=>$t&&e instanceof Blob?t?r(e):gt(e,r):Ft&&(e instanceof ArrayBuffer||Ut(e))?t?r(e):gt(new Blob([e]),r):r(Q[n]+(e||"")),gt=(n,e)=>{const t=new FileReader;return t.onload=function(){const r=t.result.split(",")[1];e("b"+(r||""))},t.readAsDataURL(n)};function bt(n){return n instanceof Uint8Array?n:n instanceof ArrayBuffer?new Uint8Array(n):new Uint8Array(n.buffer,n.byteOffset,n.byteLength)}let je;function On(n,e){if($t&&n.data instanceof Blob)return n.data.arrayBuffer().then(bt).then(e);if(Ft&&(n.data instanceof ArrayBuffer||Ut(n.data)))return e(bt(n.data));ct(n,!1,t=>{je||(je=new TextEncoder),e(je.encode(t))})}const wt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ye=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let n=0;n<wt.length;n++)ye[wt.charCodeAt(n)]=n;const Tn=n=>{let e=n.length*.75,t=n.length,r,s=0,i,o,a,h;n[n.length-1]==="="&&(e--,n[n.length-2]==="="&&e--);const u=new ArrayBuffer(e),l=new Uint8Array(u);for(r=0;r<t;r+=4)i=ye[n.charCodeAt(r)],o=ye[n.charCodeAt(r+1)],a=ye[n.charCodeAt(r+2)],h=ye[n.charCodeAt(r+3)],l[s++]=i<<2|o>>4,l[s++]=(o&15)<<4|a>>2,l[s++]=(a&3)<<6|h&63;return u},An=typeof ArrayBuffer=="function",ut=(n,e)=>{if(typeof n!="string")return{type:"message",data:qt(n,e)};const t=n.charAt(0);return t==="b"?{type:"message",data:kn(n.substring(1),e)}:Ae[t]?n.length>1?{type:Ae[t],data:n.substring(1)}:{type:Ae[t]}:Xe},kn=(n,e)=>{if(An){const t=Tn(n);return qt(t,e)}else return{base64:!0,data:n}},qt=(n,e)=>{switch(e){case"blob":return n instanceof Blob?n:new Blob([n]);case"arraybuffer":default:return n instanceof ArrayBuffer?n:n.buffer}},Mt="",Cn=(n,e)=>{const t=n.length,r=new Array(t);let s=0;n.forEach((i,o)=>{ct(i,!1,a=>{r[o]=a,++s===t&&e(r.join(Mt))})})},vn=(n,e)=>{const t=n.split(Mt),r=[];for(let s=0;s<t.length;s++){const i=ut(t[s],e);if(r.push(i),i.type==="error")break}return r};function xn(){return new TransformStream({transform(n,e){On(n,t=>{const r=t.length;let s;if(r<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,r);else if(r<65536){s=new Uint8Array(3);const i=new DataView(s.buffer);i.setUint8(0,126),i.setUint16(1,r)}else{s=new Uint8Array(9);const i=new DataView(s.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(r))}n.data&&typeof n.data!="string"&&(s[0]|=128),e.enqueue(s),e.enqueue(t)})}})}let Ve;function Oe(n){return n.reduce((e,t)=>e+t.length,0)}function Te(n,e){if(n[0].length===e)return n.shift();const t=new Uint8Array(e);let r=0;for(let s=0;s<e;s++)t[s]=n[0][r++],r===n[0].length&&(n.shift(),r=0);return n.length&&r<n[0].length&&(n[0]=n[0].slice(r)),t}function Nn(n,e){Ve||(Ve=new TextDecoder);const t=[];let r=0,s=-1,i=!1;return new TransformStream({transform(o,a){for(t.push(o);;){if(r===0){if(Oe(t)<1)break;const h=Te(t,1);i=(h[0]&128)===128,s=h[0]&127,s<126?r=3:s===126?r=1:r=2}else if(r===1){if(Oe(t)<2)break;const h=Te(t,2);s=new DataView(h.buffer,h.byteOffset,h.length).getUint16(0),r=3}else if(r===2){if(Oe(t)<8)break;const h=Te(t,8),u=new DataView(h.buffer,h.byteOffset,h.length),l=u.getUint32(0);if(l>Math.pow(2,21)-1){a.enqueue(Xe);break}s=l*Math.pow(2,32)+u.getUint32(4),r=3}else{if(Oe(t)<s)break;const h=Te(t,s);a.enqueue(ut(i?h:Ve.decode(h),e)),r=0}if(s===0||s>n){a.enqueue(Xe);break}}}})}const It=4;function D(n){if(n)return Bn(n)}function Bn(n){for(var e in D.prototype)n[e]=D.prototype[e];return n}D.prototype.on=D.prototype.addEventListener=function(n,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(e),this};D.prototype.once=function(n,e){function t(){this.off(n,t),e.apply(this,arguments)}return t.fn=e,this.on(n,t),this};D.prototype.off=D.prototype.removeListener=D.prototype.removeAllListeners=D.prototype.removeEventListener=function(n,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var t=this._callbacks["$"+n];if(!t)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var r,s=0;s<t.length;s++)if(r=t[s],r===e||r.fn===e){t.splice(s,1);break}return t.length===0&&delete this._callbacks["$"+n],this};D.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),t=this._callbacks["$"+n],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(t){t=t.slice(0);for(var r=0,s=t.length;r<s;++r)t[r].apply(this,e)}return this};D.prototype.emitReserved=D.prototype.emit;D.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]};D.prototype.hasListeners=function(n){return!!this.listeners(n).length};const Pe=typeof Promise=="function"&&typeof Promise.resolve=="function"?e=>Promise.resolve().then(e):(e,t)=>t(e,0),z=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Ln="arraybuffer";function Ht(n,...e){return e.reduce((t,r)=>(n.hasOwnProperty(r)&&(t[r]=n[r]),t),{})}const Dn=z.setTimeout,Pn=z.clearTimeout;function $e(n,e){e.useNativeTimers?(n.setTimeoutFn=Dn.bind(z),n.clearTimeoutFn=Pn.bind(z)):(n.setTimeoutFn=z.setTimeout.bind(z),n.clearTimeoutFn=z.clearTimeout.bind(z))}const $n=1.33;function Fn(n){return typeof n=="string"?Un(n):Math.ceil((n.byteLength||n.size)*$n)}function Un(n){let e=0,t=0;for(let r=0,s=n.length;r<s;r++)e=n.charCodeAt(r),e<128?t+=1:e<2048?t+=2:e<55296||e>=57344?t+=3:(r++,t+=4);return t}function jt(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function qn(n){let e="";for(let t in n)n.hasOwnProperty(t)&&(e.length&&(e+="&"),e+=encodeURIComponent(t)+"="+encodeURIComponent(n[t]));return e}function Mn(n){let e={},t=n.split("&");for(let r=0,s=t.length;r<s;r++){let i=t[r].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}class In extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}}class lt extends D{constructor(e){super(),this.writable=!1,$e(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,r){return super.emitReserved("error",new In(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(e){this.readyState==="open"&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=ut(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return e.indexOf(":")===-1?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(e){const t=qn(e);return t.length?"?"+t:""}}class Hn extends lt{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||t()})),this.writable||(r++,this.once("drain",function(){--r||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){const t=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};vn(e,this.socket.binaryType).forEach(t),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};this.readyState==="open"?e():this.once("open",e)}write(e){this.writable=!1,Cn(e,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return this.opts.timestampRequests!==!1&&(t[this.opts.timestampParam]=jt()),!this.supportsBinary&&!t.sid&&(t.b64=1),this.createUri(e,t)}}let Vt=!1;try{Vt=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const jn=Vt;function Vn(){}class zn extends Hn{constructor(e){if(super(e),typeof location<"u"){const t=location.protocol==="https:";let r=location.port;r||(r=t?"443":"80"),this.xd=typeof location<"u"&&e.hostname!==location.hostname||r!==e.port}}doWrite(e,t){const r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(s,i)=>{this.onError("xhr post error",s,i)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(t,r)=>{this.onError("xhr poll error",t,r)}),this.pollXhr=e}}let ce=class ke extends D{constructor(e,t,r){super(),this.createRequest=e,$e(this,r),this._opts=r,this._method=r.method||"GET",this._uri=t,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var e;const t=Ht(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(t);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let s in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(s)&&r.setRequestHeader(s,this._opts.extraHeaders[s])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(e=this._opts.cookieJar)===null||e===void 0||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var s;r.readyState===3&&((s=this._opts.cookieJar)===null||s===void 0||s.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(s){this.setTimeoutFn(()=>{this._onError(s)},0);return}typeof document<"u"&&(this._index=ke.requestsCount++,ke.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Vn,e)try{this._xhr.abort()}catch{}typeof document<"u"&&delete ke.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;e!==null&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};ce.requestsCount=0;ce.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",_t);else if(typeof addEventListener=="function"){const n="onpagehide"in z?"pagehide":"unload";addEventListener(n,_t,!1)}}function _t(){for(let n in ce.requests)ce.requests.hasOwnProperty(n)&&ce.requests[n].abort()}const Wn=function(){const n=zt({xdomain:!1});return n&&n.responseType!==null}();class Jn extends zn{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=Wn&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new ce(zt,this.uri(),e)}}function zt(n){const e=n.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!e||jn))return new XMLHttpRequest}catch{}if(!e)try{return new z[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Wt=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Kn extends lt{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,r=Wt?{}:Ht(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,r)}catch(s){return this.emitReserved("error",s)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const r=e[t],s=t===e.length-1;ct(r,this.supportsBinary,i=>{try{this.doWrite(r,i)}catch{}s&&Pe(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=jt()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const ze=z.WebSocket||z.MozWebSocket;class Yn extends Kn{createSocket(e,t,r){return Wt?new ze(e,t,r):t?new ze(e,t):new ze(e)}doWrite(e,t){this.ws.send(t)}}class Xn extends lt{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=Nn(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),s=xn();s.readable.pipeTo(e.writable),this._writer=s.writable.getWriter();const i=()=>{r.read().then(({done:a,value:h})=>{a||(this.onPacket(h),i())}).catch(a=>{})};i();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const r=e[t],s=t===e.length-1;this._writer.write(r).then(()=>{s&&Pe(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;(e=this._transport)===null||e===void 0||e.close()}}const Qn={websocket:Yn,webtransport:Xn,polling:Jn},Gn=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Zn=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Qe(n){if(n.length>8e3)throw"URI too long";const e=n,t=n.indexOf("["),r=n.indexOf("]");t!=-1&&r!=-1&&(n=n.substring(0,t)+n.substring(t,r).replace(/:/g,";")+n.substring(r,n.length));let s=Gn.exec(n||""),i={},o=14;for(;o--;)i[Zn[o]]=s[o]||"";return t!=-1&&r!=-1&&(i.source=e,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=er(i,i.path),i.queryKey=tr(i,i.query),i}function er(n,e){const t=/\/{2,9}/g,r=e.replace(t,"/").split("/");return(e.slice(0,1)=="/"||e.length===0)&&r.splice(0,1),e.slice(-1)=="/"&&r.splice(r.length-1,1),r}function tr(n,e){const t={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,s,i){s&&(t[s]=i)}),t}const Ge=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ce=[];Ge&&addEventListener("offline",()=>{Ce.forEach(n=>n())},!1);class ee extends D{constructor(e,t){if(super(),this.binaryType=Ln,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&typeof e=="object"&&(t=e,e=null),e){const r=Qe(e);t.hostname=r.host,t.secure=r.protocol==="https"||r.protocol==="wss",t.port=r.port,r.query&&(t.query=r.query)}else t.host&&(t.hostname=Qe(t.host).host);$e(this,t),this.secure=t.secure!=null?t.secure:typeof location<"u"&&location.protocol==="https:",t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=t.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(r=>{const s=r.prototype.name;this.transports.push(s),this._transportsByName[s]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Mn(this.opts.query)),Ge&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ce.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=It,t.transport=e,this.id&&(t.sid=this.id);const r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const e=this.opts.rememberUpgrade&&ee.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",ee.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data);break}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let r=0;r<this.writeBuffer.length;r++){const s=this.writeBuffer[r].data;if(s&&(t+=Fn(s)),r>0&&t>this._maxPayload)return this.writeBuffer.slice(0,r);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,Pe(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,r){return this._sendPacket("message",e,t,r),this}send(e,t,r){return this._sendPacket("message",e,t,r),this}_sendPacket(e,t,r,s){if(typeof t=="function"&&(s=t,t=void 0),typeof r=="function"&&(s=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const i={type:e,data:t,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),s&&this.once("flush",s),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}_onError(e){if(ee.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ge&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=Ce.indexOf(this._offlineEventListener);r!==-1&&Ce.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ee.protocol=It;class nr extends ee{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),r=!1;ee.priorWebsocketSuccess=!1;const s=()=>{r||(t.send([{type:"ping",data:"probe"}]),t.once("packet",f=>{if(!r)if(f.type==="pong"&&f.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;ee.priorWebsocketSuccess=t.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(l(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const O=new Error("probe error");O.transport=t.name,this.emitReserved("upgradeError",O)}}))};function i(){r||(r=!0,l(),t.close(),t=null)}const o=f=>{const O=new Error("probe error: "+f);O.transport=t.name,i(),this.emitReserved("upgradeError",O)};function a(){o("transport closed")}function h(){o("socket closed")}function u(f){t&&f.name!==t.name&&i()}const l=()=>{t.removeListener("open",s),t.removeListener("error",o),t.removeListener("close",a),this.off("close",h),this.off("upgrading",u)};t.once("open",s),t.once("error",o),t.once("close",a),this.once("close",h),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&e!=="webtransport"?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let r=0;r<e.length;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}let rr=class extends nr{constructor(e,t={}){const r=typeof e=="object"?e:t;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(s=>Qn[s]).filter(s=>!!s)),super(e,r)}};function sr(n,e="",t){let r=n;t=t||typeof location<"u"&&location,n==null&&(n=t.protocol+"//"+t.host),typeof n=="string"&&(n.charAt(0)==="/"&&(n.charAt(1)==="/"?n=t.protocol+n:n=t.host+n),/^(https?|wss?):\/\//.test(n)||(typeof t<"u"?n=t.protocol+"//"+n:n="https://"+n),r=Qe(n)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const i=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+e,r.href=r.protocol+"://"+i+(t&&t.port===r.port?"":":"+r.port),r}const ir=typeof ArrayBuffer=="function",or=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n.buffer instanceof ArrayBuffer,Jt=Object.prototype.toString,ar=typeof Blob=="function"||typeof Blob<"u"&&Jt.call(Blob)==="[object BlobConstructor]",cr=typeof File=="function"||typeof File<"u"&&Jt.call(File)==="[object FileConstructor]";function ht(n){return ir&&(n instanceof ArrayBuffer||or(n))||ar&&n instanceof Blob||cr&&n instanceof File}function ve(n,e){if(!n||typeof n!="object")return!1;if(Array.isArray(n)){for(let t=0,r=n.length;t<r;t++)if(ve(n[t]))return!0;return!1}if(ht(n))return!0;if(n.toJSON&&typeof n.toJSON=="function"&&arguments.length===1)return ve(n.toJSON(),!0);for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&ve(n[t]))return!0;return!1}function ur(n){const e=[],t=n.data,r=n;return r.data=Ze(t,e),r.attachments=e.length,{packet:r,buffers:e}}function Ze(n,e){if(!n)return n;if(ht(n)){const t={_placeholder:!0,num:e.length};return e.push(n),t}else if(Array.isArray(n)){const t=new Array(n.length);for(let r=0;r<n.length;r++)t[r]=Ze(n[r],e);return t}else if(typeof n=="object"&&!(n instanceof Date)){const t={};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=Ze(n[r],e));return t}return n}function lr(n,e){return n.data=et(n.data,e),delete n.attachments,n}function et(n,e){if(!n)return n;if(n&&n._placeholder===!0){if(typeof n.num=="number"&&n.num>=0&&n.num<e.length)return e[n.num];throw new Error("illegal attachments")}else if(Array.isArray(n))for(let t=0;t<n.length;t++)n[t]=et(n[t],e);else if(typeof n=="object")for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t]=et(n[t],e));return n}const hr=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],fr=5;var T;(function(n){n[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK"})(T||(T={}));class dr{constructor(e){this.replacer=e}encode(e){return(e.type===T.EVENT||e.type===T.ACK)&&ve(e)?this.encodeAsBinary({type:e.type===T.EVENT?T.BINARY_EVENT:T.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===T.BINARY_EVENT||e.type===T.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&e.nsp!=="/"&&(t+=e.nsp+","),e.id!=null&&(t+=e.id),e.data!=null&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=ur(e),r=this.encodeAsString(t.packet),s=t.buffers;return s.unshift(r),s}}function Et(n){return Object.prototype.toString.call(n)==="[object Object]"}class ft extends D{constructor(e){super(),this.reviver=e}add(e){let t;if(typeof e=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const r=t.type===T.BINARY_EVENT;r||t.type===T.BINARY_ACK?(t.type=r?T.EVENT:T.ACK,this.reconstructor=new pr(t),t.attachments===0&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ht(e)||e.base64)if(this.reconstructor)t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+e)}decodeString(e){let t=0;const r={type:Number(e.charAt(0))};if(T[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===T.BINARY_EVENT||r.type===T.BINARY_ACK){const i=t+1;for(;e.charAt(++t)!=="-"&&t!=e.length;);const o=e.substring(i,t);if(o!=Number(o)||e.charAt(t)!=="-")throw new Error("Illegal attachments");r.attachments=Number(o)}if(e.charAt(t+1)==="/"){const i=t+1;for(;++t&&!(e.charAt(t)===","||t===e.length););r.nsp=e.substring(i,t)}else r.nsp="/";const s=e.charAt(t+1);if(s!==""&&Number(s)==s){const i=t+1;for(;++t;){const o=e.charAt(t);if(o==null||Number(o)!=o){--t;break}if(t===e.length)break}r.id=Number(e.substring(i,t+1))}if(e.charAt(++t)){const i=this.tryParse(e.substr(t));if(ft.isPayloadValid(r.type,i))r.data=i;else throw new Error("invalid payload")}return r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch{return!1}}static isPayloadValid(e,t){switch(e){case T.CONNECT:return Et(t);case T.DISCONNECT:return t===void 0;case T.CONNECT_ERROR:return typeof t=="string"||Et(t);case T.EVENT:case T.BINARY_EVENT:return Array.isArray(t)&&(typeof t[0]=="number"||typeof t[0]=="string"&&hr.indexOf(t[0])===-1);case T.ACK:case T.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class pr{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const t=lr(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const mr=Object.freeze(Object.defineProperty({__proto__:null,Decoder:ft,Encoder:dr,get PacketType(){return T},protocol:fr},Symbol.toStringTag,{value:"Module"}));function J(n,e,t){return n.on(e,t),function(){n.off(e,t)}}const yr=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Kt extends D{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[J(e,"open",this.onopen.bind(this)),J(e,"packet",this.onpacket.bind(this)),J(e,"error",this.onerror.bind(this)),J(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var r,s,i;if(yr.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const o={type:T.EVENT,data:t};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof t[t.length-1]=="function"){const l=this.ids++,f=t.pop();this._registerAckCallback(l,f),o.id=l}const a=(s=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||s===void 0?void 0:s.writable,h=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!a||(h?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(e,t){var r;const s=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(s===void 0){this.acks[e]=t;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let a=0;a<this.sendBuffer.length;a++)this.sendBuffer[a].id===e&&this.sendBuffer.splice(a,1);t.call(this,new Error("operation has timed out"))},s),o=(...a)=>{this.io.clearTimeoutFn(i),t.apply(this,a)};o.withError=!0,this.acks[e]=o}emitWithAck(e,...t){return new Promise((r,s)=>{const i=(o,a)=>o?s(o):r(a);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;typeof e[e.length-1]=="function"&&(t=e.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((s,...i)=>r!==this._queue[0]?void 0:(s!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(s)):(this._queue.shift(),t&&t(null,...i)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||this._queue.length===0)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){typeof this.auth=="function"?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:T.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(r=>String(r.id)===e)){const r=this.acks[e];delete this.acks[e],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case T.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case T.EVENT:case T.BINARY_EVENT:this.onevent(e);break;case T.ACK:case T.BINARY_ACK:this.onack(e);break;case T.DISCONNECT:this.ondisconnect();break;case T.CONNECT_ERROR:this.destroy();const r=new Error(e.data.message);r.data=e.data.data,this.emitReserved("connect_error",r);break}}onevent(e){const t=e.data||[];e.id!=null&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const r of t)r.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=="string"&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let r=!1;return function(...s){r||(r=!0,t.packet({type:T.ACK,id:e,data:s}))}}onack(e){const t=this.acks[e.id];typeof t=="function"&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:T.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r])return t.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r])return t.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const r of t)r.apply(this,e.data)}}}function ue(n){n=n||{},this.ms=n.min||100,this.max=n.max||1e4,this.factor=n.factor||2,this.jitter=n.jitter>0&&n.jitter<=1?n.jitter:0,this.attempts=0}ue.prototype.duration=function(){var n=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),t=Math.floor(e*this.jitter*n);n=Math.floor(e*10)&1?n+t:n-t}return Math.min(n,this.max)|0};ue.prototype.reset=function(){this.attempts=0};ue.prototype.setMin=function(n){this.ms=n};ue.prototype.setMax=function(n){this.max=n};ue.prototype.setJitter=function(n){this.jitter=n};class tt extends D{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&typeof e=="object"&&(t=e,e=void 0),t=t||{},t.path=t.path||"/socket.io",this.opts=t,$e(this,t),this.reconnection(t.reconnection!==!1),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor((r=t.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new ue({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(t.timeout==null?2e4:t.timeout),this._readyState="closed",this.uri=e;const s=t.parser||mr;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=t.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(t=this.backoff)===null||t===void 0||t.setMin(e),this)}randomizationFactor(e){var t;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(t=this.backoff)===null||t===void 0||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(t=this.backoff)===null||t===void 0||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new rr(this.uri,this.opts);const t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const s=J(t,"open",function(){r.onopen(),e&&e()}),i=a=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",a),e?e(a):this.maybeReconnectOnOpen()},o=J(t,"error",i);if(this._timeout!==!1){const a=this._timeout,h=this.setTimeoutFn(()=>{s(),i(new Error("timeout")),t.close()},a);this.opts.autoUnref&&h.unref(),this.subs.push(()=>{this.clearTimeoutFn(h)})}return this.subs.push(s),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(J(e,"ping",this.onping.bind(this)),J(e,"data",this.ondata.bind(this)),J(e,"error",this.onerror.bind(this)),J(e,"close",this.onclose.bind(this)),J(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){Pe(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new Kt(this,e,t),this.nsps[e]=r),r}_destroy(e){const t=Object.keys(this.nsps);for(const r of t)if(this.nsps[r].active)return;this._close()}_packet(e){const t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),!e.skipReconnect&&e.open(s=>{s?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",s)):e.onreconnect()}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const pe={};function We(n,e){typeof n=="object"&&(e=n,n=void 0),e=e||{};const t=sr(n,e.path||"/socket.io"),r=t.source,s=t.id,i=t.path,o=pe[s]&&i in pe[s].nsps,a=e.forceNew||e["force new connection"]||e.multiplex===!1||o;let h;return a?h=new tt(r,e):(pe[s]||(pe[s]=new tt(r,e)),h=pe[s]),t.query&&!e.query&&(e.query=t.queryKey),h.socket(t.path,e)}Object.assign(We,{Manager:tt,Socket:Kt,io:We,connect:We});function Yt(n,e){return function(){return n.apply(e,arguments)}}const{toString:gr}=Object.prototype,{getPrototypeOf:dt}=Object,{iterator:Fe,toStringTag:Xt}=Symbol,Ue=(n=>e=>{const t=gr.call(e);return n[t]||(n[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),K=n=>(n=n.toLowerCase(),e=>Ue(e)===n),qe=n=>e=>typeof e===n,{isArray:le}=Array,ge=qe("undefined");function be(n){return n!==null&&!ge(n)&&n.constructor!==null&&!ge(n.constructor)&&H(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Qt=K("ArrayBuffer");function br(n){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(n):e=n&&n.buffer&&Qt(n.buffer),e}const wr=qe("string"),H=qe("function"),Gt=qe("number"),we=n=>n!==null&&typeof n=="object",_r=n=>n===!0||n===!1,xe=n=>{if(Ue(n)!=="object")return!1;const e=dt(n);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Xt in n)&&!(Fe in n)},Er=n=>{if(!we(n)||be(n))return!1;try{return Object.keys(n).length===0&&Object.getPrototypeOf(n)===Object.prototype}catch{return!1}},Sr=K("Date"),Rr=K("File"),Or=K("Blob"),Tr=K("FileList"),Ar=n=>we(n)&&H(n.pipe),kr=n=>{let e;return n&&(typeof FormData=="function"&&n instanceof FormData||H(n.append)&&((e=Ue(n))==="formdata"||e==="object"&&H(n.toString)&&n.toString()==="[object FormData]"))},Cr=K("URLSearchParams"),[vr,xr,Nr,Br]=["ReadableStream","Request","Response","Headers"].map(K),Lr=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function _e(n,e,{allOwnKeys:t=!1}={}){if(n===null||typeof n>"u")return;let r,s;if(typeof n!="object"&&(n=[n]),le(n))for(r=0,s=n.length;r<s;r++)e.call(null,n[r],r,n);else{if(be(n))return;const i=t?Object.getOwnPropertyNames(n):Object.keys(n),o=i.length;let a;for(r=0;r<o;r++)a=i[r],e.call(null,n[a],a,n)}}function Zt(n,e){if(be(n))return null;e=e.toLowerCase();const t=Object.keys(n);let r=t.length,s;for(;r-- >0;)if(s=t[r],e===s.toLowerCase())return s;return null}const se=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,en=n=>!ge(n)&&n!==se;function nt(){const{caseless:n}=en(this)&&this||{},e={},t=(r,s)=>{const i=n&&Zt(e,s)||s;xe(e[i])&&xe(r)?e[i]=nt(e[i],r):xe(r)?e[i]=nt({},r):le(r)?e[i]=r.slice():e[i]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&_e(arguments[r],t);return e}const Dr=(n,e,t,{allOwnKeys:r}={})=>(_e(e,(s,i)=>{t&&H(s)?n[i]=Yt(s,t):n[i]=s},{allOwnKeys:r}),n),Pr=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),$r=(n,e,t,r)=>{n.prototype=Object.create(e.prototype,r),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:e.prototype}),t&&Object.assign(n.prototype,t)},Fr=(n,e,t,r)=>{let s,i,o;const a={};if(e=e||{},n==null)return e;do{for(s=Object.getOwnPropertyNames(n),i=s.length;i-- >0;)o=s[i],(!r||r(o,n,e))&&!a[o]&&(e[o]=n[o],a[o]=!0);n=t!==!1&&dt(n)}while(n&&(!t||t(n,e))&&n!==Object.prototype);return e},Ur=(n,e,t)=>{n=String(n),(t===void 0||t>n.length)&&(t=n.length),t-=e.length;const r=n.indexOf(e,t);return r!==-1&&r===t},qr=n=>{if(!n)return null;if(le(n))return n;let e=n.length;if(!Gt(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=n[e];return t},Mr=(n=>e=>n&&e instanceof n)(typeof Uint8Array<"u"&&dt(Uint8Array)),Ir=(n,e)=>{const r=(n&&n[Fe]).call(n);let s;for(;(s=r.next())&&!s.done;){const i=s.value;e.call(n,i[0],i[1])}},Hr=(n,e)=>{let t;const r=[];for(;(t=n.exec(e))!==null;)r.push(t);return r},jr=K("HTMLFormElement"),Vr=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,r,s){return r.toUpperCase()+s}),St=(({hasOwnProperty:n})=>(e,t)=>n.call(e,t))(Object.prototype),zr=K("RegExp"),tn=(n,e)=>{const t=Object.getOwnPropertyDescriptors(n),r={};_e(t,(s,i)=>{let o;(o=e(s,i,n))!==!1&&(r[i]=o||s)}),Object.defineProperties(n,r)},Wr=n=>{tn(n,(e,t)=>{if(H(n)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const r=n[t];if(H(r)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},Jr=(n,e)=>{const t={},r=s=>{s.forEach(i=>{t[i]=!0})};return le(n)?r(n):r(String(n).split(e)),t},Kr=()=>{},Yr=(n,e)=>n!=null&&Number.isFinite(n=+n)?n:e;function Xr(n){return!!(n&&H(n.append)&&n[Xt]==="FormData"&&n[Fe])}const Qr=n=>{const e=new Array(10),t=(r,s)=>{if(we(r)){if(e.indexOf(r)>=0)return;if(be(r))return r;if(!("toJSON"in r)){e[s]=r;const i=le(r)?[]:{};return _e(r,(o,a)=>{const h=t(o,s+1);!ge(h)&&(i[a]=h)}),e[s]=void 0,i}}return r};return t(n,0)},Gr=K("AsyncFunction"),Zr=n=>n&&(we(n)||H(n))&&H(n.then)&&H(n.catch),nn=((n,e)=>n?setImmediate:e?((t,r)=>(se.addEventListener("message",({source:s,data:i})=>{s===se&&i===t&&r.length&&r.shift()()},!1),s=>{r.push(s),se.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",H(se.postMessage)),es=typeof queueMicrotask<"u"?queueMicrotask.bind(se):typeof process<"u"&&process.nextTick||nn,ts=n=>n!=null&&H(n[Fe]),c={isArray:le,isArrayBuffer:Qt,isBuffer:be,isFormData:kr,isArrayBufferView:br,isString:wr,isNumber:Gt,isBoolean:_r,isObject:we,isPlainObject:xe,isEmptyObject:Er,isReadableStream:vr,isRequest:xr,isResponse:Nr,isHeaders:Br,isUndefined:ge,isDate:Sr,isFile:Rr,isBlob:Or,isRegExp:zr,isFunction:H,isStream:Ar,isURLSearchParams:Cr,isTypedArray:Mr,isFileList:Tr,forEach:_e,merge:nt,extend:Dr,trim:Lr,stripBOM:Pr,inherits:$r,toFlatObject:Fr,kindOf:Ue,kindOfTest:K,endsWith:Ur,toArray:qr,forEachEntry:Ir,matchAll:Hr,isHTMLForm:jr,hasOwnProperty:St,hasOwnProp:St,reduceDescriptors:tn,freezeMethods:Wr,toObjectSet:Jr,toCamelCase:Vr,noop:Kr,toFiniteNumber:Yr,findKey:Zt,global:se,isContextDefined:en,isSpecCompliantForm:Xr,toJSONObject:Qr,isAsyncFn:Gr,isThenable:Zr,setImmediate:nn,asap:es,isIterable:ts};function E(n,e,t,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}c.inherits(E,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:c.toJSONObject(this.config),code:this.code,status:this.status}}});const rn=E.prototype,sn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{sn[n]={value:n}});Object.defineProperties(E,sn);Object.defineProperty(rn,"isAxiosError",{value:!0});E.from=(n,e,t,r,s,i)=>{const o=Object.create(rn);return c.toFlatObject(n,o,function(h){return h!==Error.prototype},a=>a!=="isAxiosError"),E.call(o,n.message,e,t,r,s),o.cause=n,o.name=n.name,i&&Object.assign(o,i),o};const ns=null;function rt(n){return c.isPlainObject(n)||c.isArray(n)}function on(n){return c.endsWith(n,"[]")?n.slice(0,-2):n}function Rt(n,e,t){return n?n.concat(e).map(function(s,i){return s=on(s),!t&&i?"["+s+"]":s}).join(t?".":""):e}function rs(n){return c.isArray(n)&&!n.some(rt)}const ss=c.toFlatObject(c,{},null,function(e){return/^is[A-Z]/.test(e)});function Me(n,e,t){if(!c.isObject(n))throw new TypeError("target must be an object");e=e||new FormData,t=c.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(_,g){return!c.isUndefined(g[_])});const r=t.metaTokens,s=t.visitor||l,i=t.dots,o=t.indexes,h=(t.Blob||typeof Blob<"u"&&Blob)&&c.isSpecCompliantForm(e);if(!c.isFunction(s))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(c.isDate(y))return y.toISOString();if(c.isBoolean(y))return y.toString();if(!h&&c.isBlob(y))throw new E("Blob is not supported. Use a Buffer instead.");return c.isArrayBuffer(y)||c.isTypedArray(y)?h&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function l(y,_,g){let v=y;if(y&&!g&&typeof y=="object"){if(c.endsWith(_,"{}"))_=r?_:_.slice(0,-2),y=JSON.stringify(y);else if(c.isArray(y)&&rs(y)||(c.isFileList(y)||c.endsWith(_,"[]"))&&(v=c.toArray(y)))return _=on(_),v.forEach(function(x,W){!(c.isUndefined(x)||x===null)&&e.append(o===!0?Rt([_],W,i):o===null?_:_+"[]",u(x))}),!1}return rt(y)?!0:(e.append(Rt(g,_,i),u(y)),!1)}const f=[],O=Object.assign(ss,{defaultVisitor:l,convertValue:u,isVisitable:rt});function A(y,_){if(!c.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+_.join("."));f.push(y),c.forEach(y,function(v,N){(!(c.isUndefined(v)||v===null)&&s.call(e,v,c.isString(N)?N.trim():N,_,O))===!0&&A(v,_?_.concat(N):[N])}),f.pop()}}if(!c.isObject(n))throw new TypeError("data must be an object");return A(n),e}function Ot(n){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(r){return e[r]})}function pt(n,e){this._pairs=[],n&&Me(n,this,e)}const an=pt.prototype;an.append=function(e,t){this._pairs.push([e,t])};an.toString=function(e){const t=e?function(r){return e.call(this,r,Ot)}:Ot;return this._pairs.map(function(s){return t(s[0])+"="+t(s[1])},"").join("&")};function is(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function cn(n,e,t){if(!e)return n;const r=t&&t.encode||is;c.isFunction(t)&&(t={serialize:t});const s=t&&t.serialize;let i;if(s?i=s(e,t):i=c.isURLSearchParams(e)?e.toString():new pt(e,t).toString(r),i){const o=n.indexOf("#");o!==-1&&(n=n.slice(0,o)),n+=(n.indexOf("?")===-1?"?":"&")+i}return n}class Tt{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){c.forEach(this.handlers,function(r){r!==null&&e(r)})}}const un={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},os=typeof URLSearchParams<"u"?URLSearchParams:pt,as=typeof FormData<"u"?FormData:null,cs=typeof Blob<"u"?Blob:null,us={isBrowser:!0,classes:{URLSearchParams:os,FormData:as,Blob:cs},protocols:["http","https","file","blob","url","data"]},mt=typeof window<"u"&&typeof document<"u",st=typeof navigator=="object"&&navigator||void 0,ls=mt&&(!st||["ReactNative","NativeScript","NS"].indexOf(st.product)<0),hs=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",fs=mt&&window.location.href||"http://localhost",ds=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:mt,hasStandardBrowserEnv:ls,hasStandardBrowserWebWorkerEnv:hs,navigator:st,origin:fs},Symbol.toStringTag,{value:"Module"})),M={...ds,...us};function ps(n,e){return Me(n,new M.classes.URLSearchParams,{visitor:function(t,r,s,i){return M.isNode&&c.isBuffer(t)?(this.append(r,t.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...e})}function ms(n){return c.matchAll(/\w+|\[(\w*)]/g,n).map(e=>e[0]==="[]"?"":e[1]||e[0])}function ys(n){const e={},t=Object.keys(n);let r;const s=t.length;let i;for(r=0;r<s;r++)i=t[r],e[i]=n[i];return e}function ln(n){function e(t,r,s,i){let o=t[i++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),h=i>=t.length;return o=!o&&c.isArray(s)?s.length:o,h?(c.hasOwnProp(s,o)?s[o]=[s[o],r]:s[o]=r,!a):((!s[o]||!c.isObject(s[o]))&&(s[o]=[]),e(t,r,s[o],i)&&c.isArray(s[o])&&(s[o]=ys(s[o])),!a)}if(c.isFormData(n)&&c.isFunction(n.entries)){const t={};return c.forEachEntry(n,(r,s)=>{e(ms(r),s,t,0)}),t}return null}function gs(n,e,t){if(c.isString(n))try{return(e||JSON.parse)(n),c.trim(n)}catch(r){if(r.name!=="SyntaxError")throw r}return(t||JSON.stringify)(n)}const Ee={transitional:un,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const r=t.getContentType()||"",s=r.indexOf("application/json")>-1,i=c.isObject(e);if(i&&c.isHTMLForm(e)&&(e=new FormData(e)),c.isFormData(e))return s?JSON.stringify(ln(e)):e;if(c.isArrayBuffer(e)||c.isBuffer(e)||c.isStream(e)||c.isFile(e)||c.isBlob(e)||c.isReadableStream(e))return e;if(c.isArrayBufferView(e))return e.buffer;if(c.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ps(e,this.formSerializer).toString();if((a=c.isFileList(e))||r.indexOf("multipart/form-data")>-1){const h=this.env&&this.env.FormData;return Me(a?{"files[]":e}:e,h&&new h,this.formSerializer)}}return i||s?(t.setContentType("application/json",!1),gs(e)):e}],transformResponse:[function(e){const t=this.transitional||Ee.transitional,r=t&&t.forcedJSONParsing,s=this.responseType==="json";if(c.isResponse(e)||c.isReadableStream(e))return e;if(e&&c.isString(e)&&(r&&!this.responseType||s)){const o=!(t&&t.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(a){if(o)throw a.name==="SyntaxError"?E.from(a,E.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:M.classes.FormData,Blob:M.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};c.forEach(["delete","get","head","post","put","patch"],n=>{Ee.headers[n]={}});const bs=c.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ws=n=>{const e={};let t,r,s;return n&&n.split(`
`).forEach(function(o){s=o.indexOf(":"),t=o.substring(0,s).trim().toLowerCase(),r=o.substring(s+1).trim(),!(!t||e[t]&&bs[t])&&(t==="set-cookie"?e[t]?e[t].push(r):e[t]=[r]:e[t]=e[t]?e[t]+", "+r:r)}),e},At=Symbol("internals");function me(n){return n&&String(n).trim().toLowerCase()}function Ne(n){return n===!1||n==null?n:c.isArray(n)?n.map(Ne):String(n)}function _s(n){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=t.exec(n);)e[r[1]]=r[2];return e}const Es=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Je(n,e,t,r,s){if(c.isFunction(r))return r.call(this,e,t);if(s&&(e=t),!!c.isString(e)){if(c.isString(r))return e.indexOf(r)!==-1;if(c.isRegExp(r))return r.test(e)}}function Ss(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r)}function Rs(n,e){const t=c.toCamelCase(" "+e);["get","set","has"].forEach(r=>{Object.defineProperty(n,r+t,{value:function(s,i,o){return this[r].call(this,e,s,i,o)},configurable:!0})})}let j=class{constructor(e){e&&this.set(e)}set(e,t,r){const s=this;function i(a,h,u){const l=me(h);if(!l)throw new Error("header name must be a non-empty string");const f=c.findKey(s,l);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||h]=Ne(a))}const o=(a,h)=>c.forEach(a,(u,l)=>i(u,l,h));if(c.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(c.isString(e)&&(e=e.trim())&&!Es(e))o(ws(e),t);else if(c.isObject(e)&&c.isIterable(e)){let a={},h,u;for(const l of e){if(!c.isArray(l))throw TypeError("Object iterator must return a key-value pair");a[u=l[0]]=(h=a[u])?c.isArray(h)?[...h,l[1]]:[h,l[1]]:l[1]}o(a,t)}else e!=null&&i(t,e,r);return this}get(e,t){if(e=me(e),e){const r=c.findKey(this,e);if(r){const s=this[r];if(!t)return s;if(t===!0)return _s(s);if(c.isFunction(t))return t.call(this,s,r);if(c.isRegExp(t))return t.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=me(e),e){const r=c.findKey(this,e);return!!(r&&this[r]!==void 0&&(!t||Je(this,this[r],r,t)))}return!1}delete(e,t){const r=this;let s=!1;function i(o){if(o=me(o),o){const a=c.findKey(r,o);a&&(!t||Je(r,r[a],a,t))&&(delete r[a],s=!0)}}return c.isArray(e)?e.forEach(i):i(e),s}clear(e){const t=Object.keys(this);let r=t.length,s=!1;for(;r--;){const i=t[r];(!e||Je(this,this[i],i,e,!0))&&(delete this[i],s=!0)}return s}normalize(e){const t=this,r={};return c.forEach(this,(s,i)=>{const o=c.findKey(r,i);if(o){t[o]=Ne(s),delete t[i];return}const a=e?Ss(i):String(i).trim();a!==i&&delete t[i],t[a]=Ne(s),r[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return c.forEach(this,(r,s)=>{r!=null&&r!==!1&&(t[s]=e&&c.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const r=new this(e);return t.forEach(s=>r.set(s)),r}static accessor(e){const r=(this[At]=this[At]={accessors:{}}).accessors,s=this.prototype;function i(o){const a=me(o);r[a]||(Rs(s,o),r[a]=!0)}return c.isArray(e)?e.forEach(i):i(e),this}};j.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);c.reduceDescriptors(j.prototype,({value:n},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>n,set(r){this[t]=r}}});c.freezeMethods(j);function Ke(n,e){const t=this||Ee,r=e||t,s=j.from(r.headers);let i=r.data;return c.forEach(n,function(a){i=a.call(t,i,s.normalize(),e?e.status:void 0)}),s.normalize(),i}function hn(n){return!!(n&&n.__CANCEL__)}function he(n,e,t){E.call(this,n??"canceled",E.ERR_CANCELED,e,t),this.name="CanceledError"}c.inherits(he,E,{__CANCEL__:!0});function fn(n,e,t){const r=t.config.validateStatus;!t.status||!r||r(t.status)?n(t):e(new E("Request failed with status code "+t.status,[E.ERR_BAD_REQUEST,E.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function Os(n){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return e&&e[1]||""}function Ts(n,e){n=n||10;const t=new Array(n),r=new Array(n);let s=0,i=0,o;return e=e!==void 0?e:1e3,function(h){const u=Date.now(),l=r[i];o||(o=u),t[s]=h,r[s]=u;let f=i,O=0;for(;f!==s;)O+=t[f++],f=f%n;if(s=(s+1)%n,s===i&&(i=(i+1)%n),u-o<e)return;const A=l&&u-l;return A?Math.round(O*1e3/A):void 0}}function As(n,e){let t=0,r=1e3/e,s,i;const o=(u,l=Date.now())=>{t=l,s=null,i&&(clearTimeout(i),i=null),n(...u)};return[(...u)=>{const l=Date.now(),f=l-t;f>=r?o(u,l):(s=u,i||(i=setTimeout(()=>{i=null,o(s)},r-f)))},()=>s&&o(s)]}const Le=(n,e,t=3)=>{let r=0;const s=Ts(50,250);return As(i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,h=o-r,u=s(h),l=o<=a;r=o;const f={loaded:o,total:a,progress:a?o/a:void 0,bytes:h,rate:u||void 0,estimated:u&&a&&l?(a-o)/u:void 0,event:i,lengthComputable:a!=null,[e?"download":"upload"]:!0};n(f)},t)},kt=(n,e)=>{const t=n!=null;return[r=>e[0]({lengthComputable:t,total:n,loaded:r}),e[1]]},Ct=n=>(...e)=>c.asap(()=>n(...e)),ks=M.hasStandardBrowserEnv?((n,e)=>t=>(t=new URL(t,M.origin),n.protocol===t.protocol&&n.host===t.host&&(e||n.port===t.port)))(new URL(M.origin),M.navigator&&/(msie|trident)/i.test(M.navigator.userAgent)):()=>!0,Cs=M.hasStandardBrowserEnv?{write(n,e,t,r,s,i){const o=[n+"="+encodeURIComponent(e)];c.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),c.isString(r)&&o.push("path="+r),c.isString(s)&&o.push("domain="+s),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(n){const e=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function vs(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function xs(n,e){return e?n.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):n}function dn(n,e,t){let r=!vs(e);return n&&(r||t==!1)?xs(n,e):e}const vt=n=>n instanceof j?{...n}:n;function oe(n,e){e=e||{};const t={};function r(u,l,f,O){return c.isPlainObject(u)&&c.isPlainObject(l)?c.merge.call({caseless:O},u,l):c.isPlainObject(l)?c.merge({},l):c.isArray(l)?l.slice():l}function s(u,l,f,O){if(c.isUndefined(l)){if(!c.isUndefined(u))return r(void 0,u,f,O)}else return r(u,l,f,O)}function i(u,l){if(!c.isUndefined(l))return r(void 0,l)}function o(u,l){if(c.isUndefined(l)){if(!c.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function a(u,l,f){if(f in e)return r(u,l);if(f in n)return r(void 0,u)}const h={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(u,l,f)=>s(vt(u),vt(l),f,!0)};return c.forEach(Object.keys({...n,...e}),function(l){const f=h[l]||s,O=f(n[l],e[l],l);c.isUndefined(O)&&f!==a||(t[l]=O)}),t}const pn=n=>{const e=oe({},n);let{data:t,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:i,headers:o,auth:a}=e;e.headers=o=j.from(o),e.url=cn(dn(e.baseURL,e.url,e.allowAbsoluteUrls),n.params,n.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let h;if(c.isFormData(t)){if(M.hasStandardBrowserEnv||M.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((h=o.getContentType())!==!1){const[u,...l]=h?h.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...l].join("; "))}}if(M.hasStandardBrowserEnv&&(r&&c.isFunction(r)&&(r=r(e)),r||r!==!1&&ks(e.url))){const u=s&&i&&Cs.read(i);u&&o.set(s,u)}return e},Ns=typeof XMLHttpRequest<"u",Bs=Ns&&function(n){return new Promise(function(t,r){const s=pn(n);let i=s.data;const o=j.from(s.headers).normalize();let{responseType:a,onUploadProgress:h,onDownloadProgress:u}=s,l,f,O,A,y;function _(){A&&A(),y&&y(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let g=new XMLHttpRequest;g.open(s.method.toUpperCase(),s.url,!0),g.timeout=s.timeout;function v(){if(!g)return;const x=j.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),L={data:!a||a==="text"||a==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:x,config:n,request:g};fn(function(Y){t(Y),_()},function(Y){r(Y),_()},L),g=null}"onloadend"in g?g.onloadend=v:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(v)},g.onabort=function(){g&&(r(new E("Request aborted",E.ECONNABORTED,n,g)),g=null)},g.onerror=function(){r(new E("Network Error",E.ERR_NETWORK,n,g)),g=null},g.ontimeout=function(){let W=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const L=s.transitional||un;s.timeoutErrorMessage&&(W=s.timeoutErrorMessage),r(new E(W,L.clarifyTimeoutError?E.ETIMEDOUT:E.ECONNABORTED,n,g)),g=null},i===void 0&&o.setContentType(null),"setRequestHeader"in g&&c.forEach(o.toJSON(),function(W,L){g.setRequestHeader(L,W)}),c.isUndefined(s.withCredentials)||(g.withCredentials=!!s.withCredentials),a&&a!=="json"&&(g.responseType=s.responseType),u&&([O,y]=Le(u,!0),g.addEventListener("progress",O)),h&&g.upload&&([f,A]=Le(h),g.upload.addEventListener("progress",f),g.upload.addEventListener("loadend",A)),(s.cancelToken||s.signal)&&(l=x=>{g&&(r(!x||x.type?new he(null,n,g):x),g.abort(),g=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const N=Os(s.url);if(N&&M.protocols.indexOf(N)===-1){r(new E("Unsupported protocol "+N+":",E.ERR_BAD_REQUEST,n));return}g.send(i||null)})},Ls=(n,e)=>{const{length:t}=n=n?n.filter(Boolean):[];if(e||t){let r=new AbortController,s;const i=function(u){if(!s){s=!0,a();const l=u instanceof Error?u:this.reason;r.abort(l instanceof E?l:new he(l instanceof Error?l.message:l))}};let o=e&&setTimeout(()=>{o=null,i(new E(`timeout ${e} of ms exceeded`,E.ETIMEDOUT))},e);const a=()=>{n&&(o&&clearTimeout(o),o=null,n.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),n=null)};n.forEach(u=>u.addEventListener("abort",i));const{signal:h}=r;return h.unsubscribe=()=>c.asap(a),h}},Ds=function*(n,e){let t=n.byteLength;if(t<e){yield n;return}let r=0,s;for(;r<t;)s=r+e,yield n.slice(r,s),r=s},Ps=async function*(n,e){for await(const t of $s(n))yield*Ds(t,e)},$s=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const e=n.getReader();try{for(;;){const{done:t,value:r}=await e.read();if(t)break;yield r}}finally{await e.cancel()}},xt=(n,e,t,r)=>{const s=Ps(n,e);let i=0,o,a=h=>{o||(o=!0,r&&r(h))};return new ReadableStream({async pull(h){try{const{done:u,value:l}=await s.next();if(u){a(),h.close();return}let f=l.byteLength;if(t){let O=i+=f;t(O)}h.enqueue(new Uint8Array(l))}catch(u){throw a(u),u}},cancel(h){return a(h),s.return()}},{highWaterMark:2})},Ie=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",mn=Ie&&typeof ReadableStream=="function",Fs=Ie&&(typeof TextEncoder=="function"?(n=>e=>n.encode(e))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),yn=(n,...e)=>{try{return!!n(...e)}catch{return!1}},Us=mn&&yn(()=>{let n=!1;const e=new Request(M.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!e}),Nt=64*1024,it=mn&&yn(()=>c.isReadableStream(new Response("").body)),De={stream:it&&(n=>n.body)};Ie&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!De[e]&&(De[e]=c.isFunction(n[e])?t=>t[e]():(t,r)=>{throw new E(`Response type '${e}' is not supported`,E.ERR_NOT_SUPPORT,r)})})})(new Response);const qs=async n=>{if(n==null)return 0;if(c.isBlob(n))return n.size;if(c.isSpecCompliantForm(n))return(await new Request(M.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(c.isArrayBufferView(n)||c.isArrayBuffer(n))return n.byteLength;if(c.isURLSearchParams(n)&&(n=n+""),c.isString(n))return(await Fs(n)).byteLength},Ms=async(n,e)=>{const t=c.toFiniteNumber(n.getContentLength());return t??qs(e)},Is=Ie&&(async n=>{let{url:e,method:t,data:r,signal:s,cancelToken:i,timeout:o,onDownloadProgress:a,onUploadProgress:h,responseType:u,headers:l,withCredentials:f="same-origin",fetchOptions:O}=pn(n);u=u?(u+"").toLowerCase():"text";let A=Ls([s,i&&i.toAbortSignal()],o),y;const _=A&&A.unsubscribe&&(()=>{A.unsubscribe()});let g;try{if(h&&Us&&t!=="get"&&t!=="head"&&(g=await Ms(l,r))!==0){let L=new Request(e,{method:"POST",body:r,duplex:"half"}),U;if(c.isFormData(r)&&(U=L.headers.get("content-type"))&&l.setContentType(U),L.body){const[Y,G]=kt(g,Le(Ct(h)));r=xt(L.body,Nt,Y,G)}}c.isString(f)||(f=f?"include":"omit");const v="credentials"in Request.prototype;y=new Request(e,{...O,signal:A,method:t.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:v?f:void 0});let N=await fetch(y,O);const x=it&&(u==="stream"||u==="response");if(it&&(a||x&&_)){const L={};["status","statusText","headers"].forEach(te=>{L[te]=N[te]});const U=c.toFiniteNumber(N.headers.get("content-length")),[Y,G]=a&&kt(U,Le(Ct(a),!0))||[];N=new Response(xt(N.body,Nt,Y,()=>{G&&G(),_&&_()}),L)}u=u||"text";let W=await De[c.findKey(De,u)||"text"](N,n);return!x&&_&&_(),await new Promise((L,U)=>{fn(L,U,{data:W,headers:j.from(N.headers),status:N.status,statusText:N.statusText,config:n,request:y})})}catch(v){throw _&&_(),v&&v.name==="TypeError"&&/Load failed|fetch/i.test(v.message)?Object.assign(new E("Network Error",E.ERR_NETWORK,n,y),{cause:v.cause||v}):E.from(v,v&&v.code,n,y)}}),ot={http:ns,xhr:Bs,fetch:Is};c.forEach(ot,(n,e)=>{if(n){try{Object.defineProperty(n,"name",{value:e})}catch{}Object.defineProperty(n,"adapterName",{value:e})}});const Bt=n=>`- ${n}`,Hs=n=>c.isFunction(n)||n===null||n===!1,gn={getAdapter:n=>{n=c.isArray(n)?n:[n];const{length:e}=n;let t,r;const s={};for(let i=0;i<e;i++){t=n[i];let o;if(r=t,!Hs(t)&&(r=ot[(o=String(t)).toLowerCase()],r===void 0))throw new E(`Unknown adapter '${o}'`);if(r)break;s[o||"#"+i]=r}if(!r){const i=Object.entries(s).map(([a,h])=>`adapter ${a} `+(h===!1?"is not supported by the environment":"is not available in the build"));let o=e?i.length>1?`since :
`+i.map(Bt).join(`
`):" "+Bt(i[0]):"as no adapter specified";throw new E("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:ot};function Ye(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new he(null,n)}function Lt(n){return Ye(n),n.headers=j.from(n.headers),n.data=Ke.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),gn.getAdapter(n.adapter||Ee.adapter)(n).then(function(r){return Ye(n),r.data=Ke.call(n,n.transformResponse,r),r.headers=j.from(r.headers),r},function(r){return hn(r)||(Ye(n),r&&r.response&&(r.response.data=Ke.call(n,n.transformResponse,r.response),r.response.headers=j.from(r.response.headers))),Promise.reject(r)})}const bn="1.11.0",He={};["object","boolean","number","function","string","symbol"].forEach((n,e)=>{He[n]=function(r){return typeof r===n||"a"+(e<1?"n ":" ")+n}});const Dt={};He.transitional=function(e,t,r){function s(i,o){return"[Axios v"+bn+"] Transitional option '"+i+"'"+o+(r?". "+r:"")}return(i,o,a)=>{if(e===!1)throw new E(s(o," has been removed"+(t?" in "+t:"")),E.ERR_DEPRECATED);return t&&!Dt[o]&&(Dt[o]=!0,console.warn(s(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(i,o,a):!0}};He.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};function js(n,e,t){if(typeof n!="object")throw new E("options must be an object",E.ERR_BAD_OPTION_VALUE);const r=Object.keys(n);let s=r.length;for(;s-- >0;){const i=r[s],o=e[i];if(o){const a=n[i],h=a===void 0||o(a,i,n);if(h!==!0)throw new E("option "+i+" must be "+h,E.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new E("Unknown option "+i,E.ERR_BAD_OPTION)}}const Be={assertOptions:js,validators:He},X=Be.validators;let ie=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Tt,response:new Tt}}async request(e,t){try{return await this._request(e,t)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const i=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=oe(this.defaults,t);const{transitional:r,paramsSerializer:s,headers:i}=t;r!==void 0&&Be.assertOptions(r,{silentJSONParsing:X.transitional(X.boolean),forcedJSONParsing:X.transitional(X.boolean),clarifyTimeoutError:X.transitional(X.boolean)},!1),s!=null&&(c.isFunction(s)?t.paramsSerializer={serialize:s}:Be.assertOptions(s,{encode:X.function,serialize:X.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Be.assertOptions(t,{baseUrl:X.spelling("baseURL"),withXsrfToken:X.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&c.merge(i.common,i[t.method]);i&&c.forEach(["delete","get","head","post","put","patch","common"],y=>{delete i[y]}),t.headers=j.concat(o,i);const a=[];let h=!0;this.interceptors.request.forEach(function(_){typeof _.runWhen=="function"&&_.runWhen(t)===!1||(h=h&&_.synchronous,a.unshift(_.fulfilled,_.rejected))});const u=[];this.interceptors.response.forEach(function(_){u.push(_.fulfilled,_.rejected)});let l,f=0,O;if(!h){const y=[Lt.bind(this),void 0];for(y.unshift(...a),y.push(...u),O=y.length,l=Promise.resolve(t);f<O;)l=l.then(y[f++],y[f++]);return l}O=a.length;let A=t;for(f=0;f<O;){const y=a[f++],_=a[f++];try{A=y(A)}catch(g){_.call(this,g);break}}try{l=Lt.call(this,A)}catch(y){return Promise.reject(y)}for(f=0,O=u.length;f<O;)l=l.then(u[f++],u[f++]);return l}getUri(e){e=oe(this.defaults,e);const t=dn(e.baseURL,e.url,e.allowAbsoluteUrls);return cn(t,e.params,e.paramsSerializer)}};c.forEach(["delete","get","head","options"],function(e){ie.prototype[e]=function(t,r){return this.request(oe(r||{},{method:e,url:t,data:(r||{}).data}))}});c.forEach(["post","put","patch"],function(e){function t(r){return function(i,o,a){return this.request(oe(a||{},{method:e,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}ie.prototype[e]=t(),ie.prototype[e+"Form"]=t(!0)});let Vs=class wn{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(i){t=i});const r=this;this.promise.then(s=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](s);r._listeners=null}),this.promise.then=s=>{let i;const o=new Promise(a=>{r.subscribe(a),i=a}).then(s);return o.cancel=function(){r.unsubscribe(i)},o},e(function(i,o,a){r.reason||(r.reason=new he(i,o,a),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=r=>{e.abort(r)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new wn(function(s){e=s}),cancel:e}}};function zs(n){return function(t){return n.apply(null,t)}}function Ws(n){return c.isObject(n)&&n.isAxiosError===!0}const at={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(at).forEach(([n,e])=>{at[e]=n});function _n(n){const e=new ie(n),t=Yt(ie.prototype.request,e);return c.extend(t,ie.prototype,e,{allOwnKeys:!0}),c.extend(t,e,null,{allOwnKeys:!0}),t.create=function(s){return _n(oe(n,s))},t}const P=_n(Ee);P.Axios=ie;P.CanceledError=he;P.CancelToken=Vs;P.isCancel=hn;P.VERSION=bn;P.toFormData=Me;P.AxiosError=E;P.Cancel=P.CanceledError;P.all=function(e){return Promise.all(e)};P.spread=zs;P.isAxiosError=Ws;P.mergeConfig=oe;P.AxiosHeaders=j;P.formToJSON=n=>ln(c.isHTMLForm(n)?new FormData(n):n);P.getAdapter=gn.getAdapter;P.HttpStatusCode=at;P.default=P;const{Axios:Gs,AxiosError:Zs,CanceledError:ei,isCancel:ti,CancelToken:ni,VERSION:ri,all:si,Cancel:ii,isAxiosError:oi,spread:ai,toFormData:ci,AxiosHeaders:ui,HttpStatusCode:li,formToJSON:hi,getAdapter:fi,mergeConfig:di}=P;export{Ks as a,P as b,Rn as d,We as l};
//# sourceMappingURL=utils-C9nFXeBt.js.map
