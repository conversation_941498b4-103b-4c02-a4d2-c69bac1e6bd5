var Yi=Object.defineProperty;var Ki=(e,t,n)=>t in e?Yi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var st=(e,t,n)=>Ki(e,typeof t!="symbol"?t+"":t,n);import{r as x,a as qi,b as Xi,R as ue,g as Ks,c as qs}from"./vendor-CJBOjUNh.js";import{c as Zi,E as Rt,u as Xs,i as Ji,a as Qi,b as ea,e as ta,d as na,S as ra,f as sa,Z as oa,g as xn,h as ia,r as aa,j as la,k as ca,l as kr,m as ua,n as da,o as fa}from"./charts-CUZW4G-H.js";import{d as ha,a as Qt,l as ma,b as pa}from"./utils-C9nFXeBt.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var Zs={exports:{}},en={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ga=x,ya=Symbol.for("react.element"),xa=Symbol.for("react.fragment"),va=Object.prototype.hasOwnProperty,ba=ga.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,wa={key:!0,ref:!0,__self:!0,__source:!0};function Js(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)va.call(t,r)&&!wa.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:ya,type:e,key:i,ref:o,props:s,_owner:ba.current}}en.Fragment=xa;en.jsx=Js;en.jsxs=Js;Zs.exports=en;var h=Zs.exports,Nn={},jr=qi;Nn.createRoot=jr.createRoot,Nn.hydrateRoot=jr.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $t(){return $t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$t.apply(this,arguments)}var ze;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ze||(ze={}));const Mr="popstate";function Sa(e){e===void 0&&(e={});function t(r,s){let{pathname:i,search:o,hash:a}=r.location;return Ln("",{pathname:i,search:o,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:eo(s)}return Ta(t,n,null,e)}function ge(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Qs(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ca(){return Math.random().toString(36).substr(2,8)}function Rr(e,t){return{usr:e.state,key:e.key,idx:t}}function Ln(e,t,n,r){return n===void 0&&(n=null),$t({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?tn(t):t,{state:n,key:t&&t.key||r||Ca()})}function eo(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function tn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Ta(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:i=!1}=r,o=s.history,a=ze.Pop,l=null,c=u();c==null&&(c=0,o.replaceState($t({},o.state,{idx:c}),""));function u(){return(o.state||{idx:null}).idx}function d(){a=ze.Pop;let y=u(),v=y==null?null:y-c;c=y,l&&l({action:a,location:p.location,delta:v})}function f(y,v){a=ze.Push;let b=Ln(p.location,y,v);c=u()+1;let w=Rr(b,c),C=p.createHref(b);try{o.pushState(w,"",C)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;s.location.assign(C)}i&&l&&l({action:a,location:p.location,delta:1})}function m(y,v){a=ze.Replace;let b=Ln(p.location,y,v);c=u();let w=Rr(b,c),C=p.createHref(b);o.replaceState(w,"",C),i&&l&&l({action:a,location:p.location,delta:0})}function g(y){let v=s.location.origin!=="null"?s.location.origin:s.location.href,b=typeof y=="string"?y:eo(y);return b=b.replace(/ $/,"%20"),ge(v,"No window.location.(origin|href) available to create URL for href: "+b),new URL(b,v)}let p={get action(){return a},get location(){return e(s,o)},listen(y){if(l)throw new Error("A history only accepts one active listener");return s.addEventListener(Mr,d),l=y,()=>{s.removeEventListener(Mr,d),l=null}},createHref(y){return t(s,y)},createURL:g,encodeLocation(y){let v=g(y);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:f,replace:m,go(y){return o.go(y)}};return p}var Ar;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ar||(Ar={}));function Pa(e,t,n){return n===void 0&&(n="/"),ka(e,t,n)}function ka(e,t,n,r){let s=typeof t=="string"?tn(t):t,i=ro(s.pathname||"/",n);if(i==null)return null;let o=to(e);ja(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let c=Ba(i);a=Ia(o[l],c)}return a}function to(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(i,o,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(ge(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let c=dt([r,l.relativePath]),u=n.concat(l);i.children&&i.children.length>0&&(ge(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),to(i.children,t,u,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Na(c,i.index),routesMeta:u})};return e.forEach((i,o)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))s(i,o);else for(let l of no(i.path))s(i,o,l)}),t}function no(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return s?[i,""]:[i];let o=no(r.join("/")),a=[];return a.push(...o.map(l=>l===""?i:[i,l].join("/"))),s&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function ja(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:La(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Ma=/^:[\w-]+$/,Ra=3,Aa=2,Ea=1,Va=10,Da=-2,Er=e=>e==="*";function Na(e,t){let n=e.split("/"),r=n.length;return n.some(Er)&&(r+=Da),t&&(r+=Aa),n.filter(s=>!Er(s)).reduce((s,i)=>s+(Ma.test(i)?Ra:i===""?Ea:Va),r)}function La(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function Ia(e,t,n){let{routesMeta:r}=e,s={},i="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],c=a===r.length-1,u=i==="/"?t:t.slice(i.length)||"/",d=Fa({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},u),f=l.route;if(!d)return null;Object.assign(s,d.params),o.push({params:s,pathname:dt([i,d.pathname]),pathnameBase:za(dt([i,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(i=dt([i,d.pathnameBase]))}return o}function Fa(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Oa(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let i=s[0],o=i.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:r.reduce((c,u,d)=>{let{paramName:f,isOptional:m}=u;if(f==="*"){let p=a[d]||"";o=i.slice(0,i.length-p.length).replace(/(.)\/+$/,"$1")}const g=a[d];return m&&!g?c[f]=void 0:c[f]=(g||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function Oa(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Qs(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function Ba(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Qs(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ro(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const dt=e=>e.join("/").replace(/\/\/+/g,"/"),za=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function _a(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const so=["post","put","patch","delete"];new Set(so);const Wa=["get",...so];new Set(Wa);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ut(){return Ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ut.apply(this,arguments)}const $a=x.createContext(null),Ua=x.createContext(null),oo=x.createContext(null),nn=x.createContext(null),rn=x.createContext({outlet:null,matches:[],isDataRoute:!1}),io=x.createContext(null);function Qn(){return x.useContext(nn)!=null}function Ga(){return Qn()||ge(!1),x.useContext(nn).location}function Ha(e,t){return Ya(e,t)}function Ya(e,t,n,r){Qn()||ge(!1);let{navigator:s}=x.useContext(oo),{matches:i}=x.useContext(rn),o=i[i.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let c=Ga(),u;if(t){var d;let y=typeof t=="string"?tn(t):t;l==="/"||(d=y.pathname)!=null&&d.startsWith(l)||ge(!1),u=y}else u=c;let f=u.pathname||"/",m=f;if(l!=="/"){let y=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(y.length).join("/")}let g=Pa(e,{pathname:m}),p=Ja(g&&g.map(y=>Object.assign({},y,{params:Object.assign({},a,y.params),pathname:dt([l,s.encodeLocation?s.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?l:dt([l,s.encodeLocation?s.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),i,n,r);return t&&p?x.createElement(nn.Provider,{value:{location:Ut({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:ze.Pop}},p):p}function Ka(){let e=nl(),t=_a(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:s},n):null,null)}const qa=x.createElement(Ka,null);class Xa extends x.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?x.createElement(rn.Provider,{value:this.props.routeContext},x.createElement(io.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Za(e){let{routeContext:t,match:n,children:r}=e,s=x.useContext($a);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),x.createElement(rn.Provider,{value:t},r)}function Ja(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,a=(s=n)==null?void 0:s.errors;if(a!=null){let u=o.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);u>=0||ge(!1),o=o.slice(0,Math.min(o.length,u+1))}let l=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let u=0;u<o.length;u++){let d=o[u];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(c=u),d.route.id){let{loaderData:f,errors:m}=n,g=d.route.loader&&f[d.route.id]===void 0&&(!m||m[d.route.id]===void 0);if(d.route.lazy||g){l=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((u,d,f)=>{let m,g=!1,p=null,y=null;n&&(m=a&&d.route.id?a[d.route.id]:void 0,p=d.route.errorElement||qa,l&&(c<0&&f===0?(rl("route-fallback"),g=!0,y=null):c===f&&(g=!0,y=d.route.hydrateFallbackElement||null)));let v=t.concat(o.slice(0,f+1)),b=()=>{let w;return m?w=p:g?w=y:d.route.Component?w=x.createElement(d.route.Component,null):d.route.element?w=d.route.element:w=u,x.createElement(Za,{match:d,routeContext:{outlet:u,matches:v,isDataRoute:n!=null},children:w})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?x.createElement(Xa,{location:n.location,revalidation:n.revalidation,component:p,error:m,children:b(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):b()},null)}var ao=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ao||{});function Qa(e){let t=x.useContext(Ua);return t||ge(!1),t}function el(e){let t=x.useContext(rn);return t||ge(!1),t}function tl(e){let t=el(),n=t.matches[t.matches.length-1];return n.route.id||ge(!1),n.route.id}function nl(){var e;let t=x.useContext(io),n=Qa(ao.UseRouteError),r=tl();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const Vr={};function rl(e,t,n){Vr[e]||(Vr[e]=!0)}function sl(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function In(e){ge(!1)}function ol(e){let{basename:t="/",children:n=null,location:r,navigationType:s=ze.Pop,navigator:i,static:o=!1,future:a}=e;Qn()&&ge(!1);let l=t.replace(/^\/*/,"/"),c=x.useMemo(()=>({basename:l,navigator:i,static:o,future:Ut({v7_relativeSplatPath:!1},a)}),[l,a,i,o]);typeof r=="string"&&(r=tn(r));let{pathname:u="/",search:d="",hash:f="",state:m=null,key:g="default"}=r,p=x.useMemo(()=>{let y=ro(u,l);return y==null?null:{location:{pathname:y,search:d,hash:f,state:m,key:g},navigationType:s}},[l,u,d,f,m,g,s]);return p==null?null:x.createElement(oo.Provider,{value:c},x.createElement(nn.Provider,{children:n,value:p}))}function il(e){let{children:t,location:n}=e;return Ha(Fn(t),n)}new Promise(()=>{});function Fn(e,t){t===void 0&&(t=[]);let n=[];return x.Children.forEach(e,(r,s)=>{if(!x.isValidElement(r))return;let i=[...t,s];if(r.type===x.Fragment){n.push.apply(n,Fn(r.props.children,i));return}r.type!==In&&ge(!1),!r.props.index||!r.props.children||ge(!1);let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Fn(r.props.children,i)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const al="6";try{window.__reactRouterVersion=al}catch{}const ll="startTransition",Dr=Xi[ll];function cl(e){let{basename:t,children:n,future:r,window:s}=e,i=x.useRef();i.current==null&&(i.current=Sa({window:s,v5Compat:!0}));let o=i.current,[a,l]=x.useState({action:o.action,location:o.location}),{v7_startTransition:c}=r||{},u=x.useCallback(d=>{c&&Dr?Dr(()=>l(d)):l(d)},[l,c]);return x.useLayoutEffect(()=>o.listen(u),[o,u]),x.useEffect(()=>sl(r),[r]),x.createElement(ol,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}var Nr;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Nr||(Nr={}));var Lr;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Lr||(Lr={}));const lo=x.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),sn=x.createContext({}),on=x.createContext(null),an=typeof document<"u",er=an?x.useLayoutEffect:x.useEffect,co=x.createContext({strict:!1}),tr=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),ul="framerAppearId",uo="data-"+tr(ul);function dl(e,t,n,r){const{visualElement:s}=x.useContext(sn),i=x.useContext(co),o=x.useContext(on),a=x.useContext(lo).reducedMotion,l=x.useRef();r=r||i.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:s,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;x.useInsertionEffect(()=>{c&&c.update(n,o)});const u=x.useRef(!!(n[uo]&&!window.HandoffComplete));return er(()=>{c&&(c.render(),u.current&&c.animationState&&c.animationState.animateChanges())}),x.useEffect(()=>{c&&(c.updateFeatures(),!u.current&&c.animationState&&c.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),c}function at(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function fl(e,t,n){return x.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):at(n)&&(n.current=r))},[t])}function kt(e){return typeof e=="string"||Array.isArray(e)}function ln(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const nr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rr=["initial",...nr];function cn(e){return ln(e.animate)||rr.some(t=>kt(e[t]))}function fo(e){return!!(cn(e)||e.variants)}function hl(e,t){if(cn(e)){const{initial:n,animate:r}=e;return{initial:n===!1||kt(n)?n:void 0,animate:kt(r)?r:void 0}}return e.inherit!==!1?t:{}}function ml(e){const{initial:t,animate:n}=hl(e,x.useContext(sn));return x.useMemo(()=>({initial:t,animate:n}),[Ir(t),Ir(n)])}function Ir(e){return Array.isArray(e)?e.join(" "):e}const Fr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},jt={};for(const e in Fr)jt[e]={isEnabled:t=>Fr[e].some(n=>!!t[n])};function pl(e){for(const t in e)jt[t]={...jt[t],...e[t]}}const sr=x.createContext({}),ho=x.createContext({}),gl=Symbol.for("motionComponentSymbol");function yl({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){e&&pl(e);function i(a,l){let c;const u={...x.useContext(lo),...a,layoutId:xl(a)},{isStatic:d}=u,f=ml(a),m=r(a,d);if(!d&&an){f.visualElement=dl(s,m,u,t);const g=x.useContext(ho),p=x.useContext(co).strict;f.visualElement&&(c=f.visualElement.loadFeatures(u,p,e,g))}return x.createElement(sn.Provider,{value:f},c&&f.visualElement?x.createElement(c,{visualElement:f.visualElement,...u}):null,n(s,a,fl(m,f.visualElement,l),m,d,f.visualElement))}const o=x.forwardRef(i);return o[gl]=s,o}function xl({layoutId:e}){const t=x.useContext(sr).id;return t&&e!==void 0?t+"-"+e:e}function vl(e){function t(r,s={}){return yl(e(r,s))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,s)=>(n.has(s)||n.set(s,t(s)),n.get(s))})}const bl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function or(e){return typeof e!="string"||e.includes("-")?!1:!!(bl.indexOf(e)>-1||/[A-Z]/.test(e))}const Gt={};function wl(e){Object.assign(Gt,e)}const At=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],tt=new Set(At);function mo(e,{layout:t,layoutId:n}){return tt.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Gt[e]||e==="opacity")}const ye=e=>!!(e&&e.getVelocity),Sl={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Cl=At.length;function Tl(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,s){let i="";for(let o=0;o<Cl;o++){const a=At[o];if(e[a]!==void 0){const l=Sl[a]||a;i+=`${l}(${e[a]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),s?i=s(e,r?"":i):n&&r&&(i="none"),i}const po=e=>t=>typeof t=="string"&&t.startsWith(e),go=po("--"),On=po("var(--"),Pl=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,kl=(e,t)=>t&&typeof e=="number"?t.transform(e):e,We=(e,t,n)=>Math.min(Math.max(n,e),t),nt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},St={...nt,transform:e=>We(0,1,e)},It={...nt,default:1},Ct=e=>Math.round(e*1e5)/1e5,un=/(-)?([\d]*\.?[\d])+/g,yo=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,jl=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Et(e){return typeof e=="string"}const Vt=e=>({test:t=>Et(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Oe=Vt("deg"),Me=Vt("%"),A=Vt("px"),Ml=Vt("vh"),Rl=Vt("vw"),Or={...Me,parse:e=>Me.parse(e)/100,transform:e=>Me.transform(e*100)},Br={...nt,transform:Math.round},xo={borderWidth:A,borderTopWidth:A,borderRightWidth:A,borderBottomWidth:A,borderLeftWidth:A,borderRadius:A,radius:A,borderTopLeftRadius:A,borderTopRightRadius:A,borderBottomRightRadius:A,borderBottomLeftRadius:A,width:A,maxWidth:A,height:A,maxHeight:A,size:A,top:A,right:A,bottom:A,left:A,padding:A,paddingTop:A,paddingRight:A,paddingBottom:A,paddingLeft:A,margin:A,marginTop:A,marginRight:A,marginBottom:A,marginLeft:A,rotate:Oe,rotateX:Oe,rotateY:Oe,rotateZ:Oe,scale:It,scaleX:It,scaleY:It,scaleZ:It,skew:Oe,skewX:Oe,skewY:Oe,distance:A,translateX:A,translateY:A,translateZ:A,x:A,y:A,z:A,perspective:A,transformPerspective:A,opacity:St,originX:Or,originY:Or,originZ:A,zIndex:Br,fillOpacity:St,strokeOpacity:St,numOctaves:Br};function ir(e,t,n,r){const{style:s,vars:i,transform:o,transformOrigin:a}=e;let l=!1,c=!1,u=!0;for(const d in t){const f=t[d];if(go(d)){i[d]=f;continue}const m=xo[d],g=kl(f,m);if(tt.has(d)){if(l=!0,o[d]=g,!u)continue;f!==(m.default||0)&&(u=!1)}else d.startsWith("origin")?(c=!0,a[d]=g):s[d]=g}if(t.transform||(l||r?s.transform=Tl(e.transform,n,u,r):s.transform&&(s.transform="none")),c){const{originX:d="50%",originY:f="50%",originZ:m=0}=a;s.transformOrigin=`${d} ${f} ${m}`}}const ar=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function vo(e,t,n){for(const r in t)!ye(t[r])&&!mo(r,n)&&(e[r]=t[r])}function Al({transformTemplate:e},t,n){return x.useMemo(()=>{const r=ar();return ir(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function El(e,t,n){const r=e.style||{},s={};return vo(s,r,e),Object.assign(s,Al(e,t,n)),e.transformValues?e.transformValues(s):s}function Vl(e,t,n){const r={},s=El(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=s,r}const Dl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ht(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Dl.has(e)}let bo=e=>!Ht(e);function Nl(e){e&&(bo=t=>t.startsWith("on")?!Ht(t):e(t))}try{Nl(require("@emotion/is-prop-valid").default)}catch{}function Ll(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(bo(s)||n===!0&&Ht(s)||!t&&!Ht(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function zr(e,t,n){return typeof e=="string"?e:A.transform(t+n*e)}function Il(e,t,n){const r=zr(t,e.x,e.width),s=zr(n,e.y,e.height);return`${r} ${s}`}const Fl={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ol={offset:"strokeDashoffset",array:"strokeDasharray"};function Bl(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?Fl:Ol;e[i.offset]=A.transform(-r);const o=A.transform(t),a=A.transform(n);e[i.array]=`${o} ${a}`}function lr(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...c},u,d,f){if(ir(e,c,u,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:g,dimensions:p}=e;m.transform&&(p&&(g.transform=m.transform),delete m.transform),p&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=Il(p,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),o!==void 0&&Bl(m,o,a,l,!1)}const wo=()=>({...ar(),attrs:{}}),cr=e=>typeof e=="string"&&e.toLowerCase()==="svg";function zl(e,t,n,r){const s=x.useMemo(()=>{const i=wo();return lr(i,t,{enableHardwareAcceleration:!1},cr(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};vo(i,e.style,e),s.style={...i,...s.style}}return s}function _l(e=!1){return(n,r,s,{latestValues:i},o)=>{const l=(or(n)?zl:Vl)(r,i,o,n),u={...Ll(r,typeof n=="string",e),...l,ref:s},{children:d}=r,f=x.useMemo(()=>ye(d)?d.get():d,[d]);return x.createElement(n,{...u,children:f})}}function So(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Co=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function To(e,t,n,r){So(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(Co.has(s)?s:tr(s),t.attrs[s])}function ur(e,t){const{style:n}=e,r={};for(const s in n)(ye(n[s])||t.style&&ye(t.style[s])||mo(s,e))&&(r[s]=n[s]);return r}function Po(e,t){const n=ur(e,t);for(const r in e)if(ye(e[r])||ye(t[r])){const s=At.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[s]=e[r]}return n}function dr(e,t,n,r={},s={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,s)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,s)),t}function ko(e){const t=x.useRef(null);return t.current===null&&(t.current=e()),t.current}const Yt=e=>Array.isArray(e),Wl=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),$l=e=>Yt(e)?e[e.length-1]||0:e;function _t(e){const t=ye(e)?e.get():e;return Wl(t)?t.toValue():t}function Ul({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,s,i){const o={latestValues:Gl(r,s,i,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const jo=e=>(t,n)=>{const r=x.useContext(sn),s=x.useContext(on),i=()=>Ul(e,t,r,s);return n?i():ko(i)};function Gl(e,t,n,r){const s={},i=r(e,{});for(const f in i)s[f]=_t(i[f]);let{initial:o,animate:a}=e;const l=cn(e),c=fo(e);t&&c&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const d=u?a:o;return d&&typeof d!="boolean"&&!ln(d)&&(Array.isArray(d)?d:[d]).forEach(m=>{const g=dr(e,m);if(!g)return;const{transitionEnd:p,transition:y,...v}=g;for(const b in v){let w=v[b];if(Array.isArray(w)){const C=u?w.length-1:0;w=w[C]}w!==null&&(s[b]=w)}for(const b in p)s[b]=p[b]}),s}const ne=e=>e;class _r{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Hl(e){let t=new _r,n=new _r,r=0,s=!1,i=!1;const o=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const d=u&&s,f=d?t:n;return c&&o.add(l),f.add(l)&&d&&s&&(r=t.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(s){i=!0;return}if(s=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let c=0;c<r;c++){const u=t.order[c];u(l),o.has(u)&&(a.schedule(u),e())}s=!1,i&&(i=!1,a.process(l))}};return a}const Ft=["prepare","read","update","preRender","render","postRender"],Yl=40;function Kl(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=Ft.reduce((d,f)=>(d[f]=Hl(()=>n=!0),d),{}),o=d=>i[d].process(s),a=()=>{const d=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(d-s.timestamp,Yl),1),s.timestamp=d,s.isProcessing=!0,Ft.forEach(o),s.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,s.isProcessing||e(a)};return{schedule:Ft.reduce((d,f)=>{const m=i[f];return d[f]=(g,p=!1,y=!1)=>(n||l(),m.schedule(g,p,y)),d},{}),cancel:d=>Ft.forEach(f=>i[f].cancel(d)),state:s,steps:i}}const{schedule:K,cancel:Ne,state:ce,steps:vn}=Kl(typeof requestAnimationFrame<"u"?requestAnimationFrame:ne,!0),ql={useVisualState:jo({scrapeMotionValuesFromProps:Po,createRenderState:wo,onMount:(e,t,{renderState:n,latestValues:r})=>{K.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),K.render(()=>{lr(n,r,{enableHardwareAcceleration:!1},cr(t.tagName),e.transformTemplate),To(t,n)})}})},Xl={useVisualState:jo({scrapeMotionValuesFromProps:ur,createRenderState:ar})};function Zl(e,{forwardMotionProps:t=!1},n,r){return{...or(e)?ql:Xl,preloadedFeatures:n,useRender:_l(t),createVisualElement:r,Component:e}}function Ee(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Mo=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function dn(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Jl=e=>t=>Mo(t)&&e(t,dn(t));function Ve(e,t,n,r){return Ee(e,t,Jl(n),r)}const Ql=(e,t)=>n=>t(e(n)),_e=(...e)=>e.reduce(Ql);function Ro(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Wr=Ro("dragHorizontal"),$r=Ro("dragVertical");function Ao(e){let t=!1;if(e==="y")t=$r();else if(e==="x")t=Wr();else{const n=Wr(),r=$r();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Eo(){const e=Ao(!0);return e?(e(),!1):!0}class Ue{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Ur(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),s=(i,o)=>{if(i.pointerType==="touch"||Eo())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&K.update(()=>a[r](i,o))};return Ve(e.current,n,s,{passive:!e.getProps()[r]})}class ec extends Ue{mount(){this.unmount=_e(Ur(this.node,!0),Ur(this.node,!1))}unmount(){}}class tc extends Ue{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=_e(Ee(this.node.current,"focus",()=>this.onFocus()),Ee(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Vo=(e,t)=>t?e===t?!0:Vo(e,t.parentElement):!1;function bn(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,dn(n))}class nc extends Ue{constructor(){super(...arguments),this.removeStartListeners=ne,this.removeEndListeners=ne,this.removeAccessibleListeners=ne,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=Ve(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:d}=this.node.getProps();K.update(()=>{!d&&!Vo(this.node.current,a.target)?u&&u(a,l):c&&c(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),o=Ve(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=_e(i,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||bn("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&K.update(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=Ee(this.node.current,"keyup",o),bn("down",(a,l)=>{this.startPress(a,l)})},n=Ee(this.node.current,"keydown",t),r=()=>{this.isPressing&&bn("cancel",(i,o)=>this.cancelPress(i,o))},s=Ee(this.node.current,"blur",r);this.removeAccessibleListeners=_e(n,s)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:s}=this.node.getProps();s&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Eo()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&K.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Ve(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Ee(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=_e(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Bn=new WeakMap,wn=new WeakMap,rc=e=>{const t=Bn.get(e.target);t&&t(e)},sc=e=>{e.forEach(rc)};function oc({root:e,...t}){const n=e||document;wn.has(n)||wn.set(n,{});const r=wn.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(sc,{root:e,...t})),r[s]}function ic(e,t,n){const r=oc(t);return Bn.set(e,n),r.observe(e),()=>{Bn.delete(e),r.unobserve(e)}}const ac={some:0,all:1};class lc extends Ue{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:ac[s]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,i&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:d}=this.node.getProps(),f=c?u:d;f&&f(l)};return ic(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(cc(t,n))&&this.startObserver()}unmount(){}}function cc({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const uc={inView:{Feature:lc},tap:{Feature:nc},focus:{Feature:tc},hover:{Feature:ec}};function Do(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function dc(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function fc(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function fn(e,t,n){const r=e.getProps();return dr(r,t,n!==void 0?n:r.custom,dc(e),fc(e))}let fr=ne;const et=e=>e*1e3,De=e=>e/1e3,hc={current:!1},No=e=>Array.isArray(e)&&typeof e[0]=="number";function Lo(e){return!!(!e||typeof e=="string"&&Io[e]||No(e)||Array.isArray(e)&&e.every(Lo))}const wt=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Io={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wt([0,.65,.55,1]),circOut:wt([.55,0,1,.45]),backIn:wt([.31,.01,.66,-.59]),backOut:wt([.33,1.53,.69,.99])};function Fo(e){if(e)return No(e)?wt(e):Array.isArray(e)?e.map(Fo):Io[e]}function mc(e,t,n,{delay:r=0,duration:s,repeat:i=0,repeatType:o="loop",ease:a,times:l}={}){const c={[t]:n};l&&(c.offset=l);const u=Fo(a);return Array.isArray(u)&&(c.easing=u),e.animate(c,{delay:r,duration:s,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:i+1,direction:o==="reverse"?"alternate":"normal"})}function pc(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Oo=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,gc=1e-7,yc=12;function xc(e,t,n,r,s){let i,o,a=0;do o=t+(n-t)/2,i=Oo(o,r,s)-e,i>0?n=o:t=o;while(Math.abs(i)>gc&&++a<yc);return o}function Dt(e,t,n,r){if(e===t&&n===r)return ne;const s=i=>xc(i,0,1,e,n);return i=>i===0||i===1?i:Oo(s(i),t,r)}const vc=Dt(.42,0,1,1),bc=Dt(0,0,.58,1),Bo=Dt(.42,0,.58,1),wc=e=>Array.isArray(e)&&typeof e[0]!="number",zo=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,_o=e=>t=>1-e(1-t),hr=e=>1-Math.sin(Math.acos(e)),Wo=_o(hr),Sc=zo(hr),$o=Dt(.33,1.53,.69,.99),mr=_o($o),Cc=zo(mr),Tc=e=>(e*=2)<1?.5*mr(e):.5*(2-Math.pow(2,-10*(e-1))),Pc={linear:ne,easeIn:vc,easeInOut:Bo,easeOut:bc,circIn:hr,circInOut:Sc,circOut:Wo,backIn:mr,backInOut:Cc,backOut:$o,anticipate:Tc},Gr=e=>{if(Array.isArray(e)){fr(e.length===4);const[t,n,r,s]=e;return Dt(t,n,r,s)}else if(typeof e=="string")return Pc[e];return e},pr=(e,t)=>n=>!!(Et(n)&&jl.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Uo=(e,t,n)=>r=>{if(!Et(r))return r;const[s,i,o,a]=r.match(un);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},kc=e=>We(0,255,e),Sn={...nt,transform:e=>Math.round(kc(e))},Qe={test:pr("rgb","red"),parse:Uo("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Sn.transform(e)+", "+Sn.transform(t)+", "+Sn.transform(n)+", "+Ct(St.transform(r))+")"};function jc(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const zn={test:pr("#"),parse:jc,transform:Qe.transform},lt={test:pr("hsl","hue"),parse:Uo("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Me.transform(Ct(t))+", "+Me.transform(Ct(n))+", "+Ct(St.transform(r))+")"},fe={test:e=>Qe.test(e)||zn.test(e)||lt.test(e),parse:e=>Qe.test(e)?Qe.parse(e):lt.test(e)?lt.parse(e):zn.parse(e),transform:e=>Et(e)?e:e.hasOwnProperty("red")?Qe.transform(e):lt.transform(e)},Q=(e,t,n)=>-n*e+n*t+e;function Cn(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Mc({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,o=0;if(!t)s=i=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;s=Cn(l,a,e+1/3),i=Cn(l,a,e),o=Cn(l,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}const Tn=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Rc=[zn,Qe,lt],Ac=e=>Rc.find(t=>t.test(e));function Hr(e){const t=Ac(e);let n=t.parse(e);return t===lt&&(n=Mc(n)),n}const Go=(e,t)=>{const n=Hr(e),r=Hr(t),s={...n};return i=>(s.red=Tn(n.red,r.red,i),s.green=Tn(n.green,r.green,i),s.blue=Tn(n.blue,r.blue,i),s.alpha=Q(n.alpha,r.alpha,i),Qe.transform(s))};function Ec(e){var t,n;return isNaN(e)&&Et(e)&&(((t=e.match(un))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(yo))===null||n===void 0?void 0:n.length)||0)>0}const Ho={regex:Pl,countKey:"Vars",token:"${v}",parse:ne},Yo={regex:yo,countKey:"Colors",token:"${c}",parse:fe.parse},Ko={regex:un,countKey:"Numbers",token:"${n}",parse:nt.parse};function Pn(e,{regex:t,countKey:n,token:r,parse:s}){const i=e.tokenised.match(t);i&&(e["num"+n]=i.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...i.map(s)))}function Kt(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Pn(n,Ho),Pn(n,Yo),Pn(n,Ko),n}function qo(e){return Kt(e).values}function Xo(e){const{values:t,numColors:n,numVars:r,tokenised:s}=Kt(e),i=t.length;return o=>{let a=s;for(let l=0;l<i;l++)l<r?a=a.replace(Ho.token,o[l]):l<r+n?a=a.replace(Yo.token,fe.transform(o[l])):a=a.replace(Ko.token,Ct(o[l]));return a}}const Vc=e=>typeof e=="number"?0:e;function Dc(e){const t=qo(e);return Xo(e)(t.map(Vc))}const $e={test:Ec,parse:qo,createTransformer:Xo,getAnimatableNone:Dc},Zo=(e,t)=>n=>`${n>0?t:e}`;function Jo(e,t){return typeof e=="number"?n=>Q(e,t,n):fe.test(e)?Go(e,t):e.startsWith("var(")?Zo(e,t):ei(e,t)}const Qo=(e,t)=>{const n=[...e],r=n.length,s=e.map((i,o)=>Jo(i,t[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}},Nc=(e,t)=>{const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=Jo(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}},ei=(e,t)=>{const n=$e.createTransformer(t),r=Kt(e),s=Kt(t);return r.numVars===s.numVars&&r.numColors===s.numColors&&r.numNumbers>=s.numNumbers?_e(Qo(r.values,s.values),n):Zo(e,t)},Mt=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Yr=(e,t)=>n=>Q(e,t,n);function Lc(e){return typeof e=="number"?Yr:typeof e=="string"?fe.test(e)?Go:ei:Array.isArray(e)?Qo:typeof e=="object"?Nc:Yr}function Ic(e,t,n){const r=[],s=n||Lc(e[0]),i=e.length-1;for(let o=0;o<i;o++){let a=s(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||ne:t;a=_e(l,a)}r.push(a)}return r}function ti(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(fr(i===t.length),i===1)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=Ic(t,r,s),a=o.length,l=c=>{let u=0;if(a>1)for(;u<e.length-2&&!(c<e[u+1]);u++);const d=Mt(e[u],e[u+1],c);return o[u](d)};return n?c=>l(We(e[0],e[i-1],c)):l}function Fc(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=Mt(0,t,r);e.push(Q(n,1,s))}}function Oc(e){const t=[0];return Fc(t,e.length-1),t}function Bc(e,t){return e.map(n=>n*t)}function zc(e,t){return e.map(()=>t||Bo).splice(0,e.length-1)}function qt({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=wc(r)?r.map(Gr):Gr(r),i={done:!1,value:t[0]},o=Bc(n&&n.length===t.length?n:Oc(t),e),a=ti(o,t,{ease:Array.isArray(s)?s:zc(t,s)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}function ni(e,t){return t?e*(1e3/t):0}const _c=5;function ri(e,t,n){const r=Math.max(t-_c,0);return ni(n-e(r),t-r)}const kn=.001,Wc=.01,$c=10,Uc=.05,Gc=1;function Hc({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let s,i,o=1-t;o=We(Uc,Gc,o),e=We(Wc,$c,De(e)),o<1?(s=c=>{const u=c*o,d=u*e,f=u-n,m=_n(c,o),g=Math.exp(-d);return kn-f/m*g},i=c=>{const d=c*o*e,f=d*n+n,m=Math.pow(o,2)*Math.pow(c,2)*e,g=Math.exp(-d),p=_n(Math.pow(c,2),o);return(-s(c)+kn>0?-1:1)*((f-m)*g)/p}):(s=c=>{const u=Math.exp(-c*e),d=(c-n)*e+1;return-kn+u*d},i=c=>{const u=Math.exp(-c*e),d=(n-c)*(e*e);return u*d});const a=5/e,l=Kc(s,i,a);if(e=et(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const c=Math.pow(l,2)*r;return{stiffness:c,damping:o*2*Math.sqrt(r*c),duration:e}}}const Yc=12;function Kc(e,t,n){let r=n;for(let s=1;s<Yc;s++)r=r-e(r)/t(r);return r}function _n(e,t){return e*Math.sqrt(1-t*t)}const qc=["duration","bounce"],Xc=["stiffness","damping","mass"];function Kr(e,t){return t.some(n=>e[n]!==void 0)}function Zc(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Kr(e,Xc)&&Kr(e,qc)){const n=Hc(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function si({keyframes:e,restDelta:t,restSpeed:n,...r}){const s=e[0],i=e[e.length-1],o={done:!1,value:s},{stiffness:a,damping:l,mass:c,duration:u,velocity:d,isResolvedFromDuration:f}=Zc({...r,velocity:-De(r.velocity||0)}),m=d||0,g=l/(2*Math.sqrt(a*c)),p=i-s,y=De(Math.sqrt(a/c)),v=Math.abs(p)<5;n||(n=v?.01:2),t||(t=v?.005:.5);let b;if(g<1){const w=_n(y,g);b=C=>{const P=Math.exp(-g*y*C);return i-P*((m+g*y*p)/w*Math.sin(w*C)+p*Math.cos(w*C))}}else if(g===1)b=w=>i-Math.exp(-y*w)*(p+(m+y*p)*w);else{const w=y*Math.sqrt(g*g-1);b=C=>{const P=Math.exp(-g*y*C),R=Math.min(w*C,300);return i-P*((m+g*y*p)*Math.sinh(R)+w*p*Math.cosh(R))/w}}return{calculatedDuration:f&&u||null,next:w=>{const C=b(w);if(f)o.done=w>=u;else{let P=m;w!==0&&(g<1?P=ri(b,w,C):P=0);const R=Math.abs(P)<=n,E=Math.abs(i-C)<=t;o.done=R&&E}return o.value=o.done?i:C,o}}}function qr({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const d=e[0],f={done:!1,value:d},m=j=>a!==void 0&&j<a||l!==void 0&&j>l,g=j=>a===void 0?l:l===void 0||Math.abs(a-j)<Math.abs(l-j)?a:l;let p=n*t;const y=d+p,v=o===void 0?y:o(y);v!==y&&(p=v-d);const b=j=>-p*Math.exp(-j/r),w=j=>v+b(j),C=j=>{const O=b(j),V=w(j);f.done=Math.abs(O)<=c,f.value=f.done?v:V};let P,R;const E=j=>{m(f.value)&&(P=j,R=si({keyframes:[f.value,g(f.value)],velocity:ri(w,j,f.value),damping:s,stiffness:i,restDelta:c,restSpeed:u}))};return E(0),{calculatedDuration:null,next:j=>{let O=!1;return!R&&P===void 0&&(O=!0,C(j),E(j)),P!==void 0&&j>P?R.next(j-P):(!O&&C(j),f)}}}const Jc=e=>{const t=({timestamp:n})=>e(n);return{start:()=>K.update(t,!0),stop:()=>Ne(t),now:()=>ce.isProcessing?ce.timestamp:performance.now()}},Xr=2e4;function Zr(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Xr;)t+=n,r=e.next(t);return t>=Xr?1/0:t}const Qc={decay:qr,inertia:qr,tween:qt,keyframes:qt,spring:si};function Xt({autoplay:e=!0,delay:t=0,driver:n=Jc,keyframes:r,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:c,onComplete:u,onUpdate:d,...f}){let m=1,g=!1,p,y;const v=()=>{y=new Promise(z=>{p=z})};v();let b;const w=Qc[s]||qt;let C;w!==qt&&typeof r[0]!="number"&&(C=ti([0,100],r,{clamp:!1}),r=[0,100]);const P=w({...f,keyframes:r});let R;a==="mirror"&&(R=w({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let E="idle",j=null,O=null,V=null;P.calculatedDuration===null&&i&&(P.calculatedDuration=Zr(P));const{calculatedDuration:ae}=P;let I=1/0,he=1/0;ae!==null&&(I=ae+o,he=I*(i+1)-o);let X=0;const Te=z=>{if(O===null)return;m>0&&(O=Math.min(O,z)),m<0&&(O=Math.min(z-he/m,O)),j!==null?X=j:X=Math.round(z-O)*m;const ke=X-t*(m>=0?1:-1),Nt=m>=0?ke<0:ke>he;X=Math.max(ke,0),E==="finished"&&j===null&&(X=he);let B=X,S=P;if(i){const M=Math.min(X,he)/I;let F=Math.floor(M),U=M%1;!U&&M>=1&&(U=1),U===1&&F--,F=Math.min(F,i+1),!!(F%2)&&(a==="reverse"?(U=1-U,o&&(U-=o/I)):a==="mirror"&&(S=R)),B=We(0,1,U)*I}const T=Nt?{done:!1,value:r[0]}:S.next(B);C&&(T.value=C(T.value));let{done:k}=T;!Nt&&ae!==null&&(k=m>=0?X>=he:X<=0);const N=j===null&&(E==="finished"||E==="running"&&k);return d&&d(T.value),N&&Pe(),T},Z=()=>{b&&b.stop(),b=void 0},xe=()=>{E="idle",Z(),p(),v(),O=V=null},Pe=()=>{E="finished",u&&u(),Z(),p()},me=()=>{if(g)return;b||(b=n(Te));const z=b.now();l&&l(),j!==null?O=z-j:(!O||E==="finished")&&(O=z),E==="finished"&&v(),V=O,j=null,E="running",b.start()};e&&me();const He={then(z,ke){return y.then(z,ke)},get time(){return De(X)},set time(z){z=et(z),X=z,j!==null||!b||m===0?j=z:O=b.now()-z/m},get duration(){const z=P.calculatedDuration===null?Zr(P):P.calculatedDuration;return De(z)},get speed(){return m},set speed(z){z===m||!b||(m=z,He.time=De(X))},get state(){return E},play:me,pause:()=>{E="paused",j=X},stop:()=>{g=!0,E!=="idle"&&(E="idle",c&&c(),xe())},cancel:()=>{V!==null&&Te(V),xe()},complete:()=>{E="finished"},sample:z=>(O=0,Te(z))};return He}function eu(e){let t;return()=>(t===void 0&&(t=e()),t)}const tu=eu(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),nu=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ot=10,ru=2e4,su=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Lo(t.ease);function ou(e,t,{onUpdate:n,onComplete:r,...s}){if(!(tu()&&nu.has(t)&&!s.repeatDelay&&s.repeatType!=="mirror"&&s.damping!==0&&s.type!=="inertia"))return!1;let o=!1,a,l,c=!1;const u=()=>{l=new Promise(w=>{a=w})};u();let{keyframes:d,duration:f=300,ease:m,times:g}=s;if(su(t,s)){const w=Xt({...s,repeat:0,delay:0});let C={done:!1,value:d[0]};const P=[];let R=0;for(;!C.done&&R<ru;)C=w.sample(R),P.push(C.value),R+=Ot;g=void 0,d=P,f=R-Ot,m="linear"}const p=mc(e.owner.current,t,d,{...s,duration:f,ease:m,times:g}),y=()=>{c=!1,p.cancel()},v=()=>{c=!0,K.update(y),a(),u()};return p.onfinish=()=>{c||(e.set(pc(d,s)),r&&r(),v())},{then(w,C){return l.then(w,C)},attachTimeline(w){return p.timeline=w,p.onfinish=null,ne},get time(){return De(p.currentTime||0)},set time(w){p.currentTime=et(w)},get speed(){return p.playbackRate},set speed(w){p.playbackRate=w},get duration(){return De(f)},play:()=>{o||(p.play(),Ne(y))},pause:()=>p.pause(),stop:()=>{if(o=!0,p.playState==="idle")return;const{currentTime:w}=p;if(w){const C=Xt({...s,autoplay:!1});e.setWithVelocity(C.sample(w-Ot).value,C.sample(w).value,Ot)}v()},complete:()=>{c||p.finish()},cancel:v}}function iu({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const s=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ne,pause:ne,stop:ne,then:i=>(i(),Promise.resolve()),cancel:ne,complete:ne});return t?Xt({keyframes:[0,1],duration:0,delay:t,onComplete:s}):s()}const au={type:"spring",stiffness:500,damping:25,restSpeed:10},lu=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),cu={type:"keyframes",duration:.8},uu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},du=(e,{keyframes:t})=>t.length>2?cu:tt.has(e)?e.startsWith("scale")?lu(t[1]):au:uu,Wn=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&($e.test(t)||t==="0")&&!t.startsWith("url(")),fu=new Set(["brightness","contrast","saturate","opacity"]);function hu(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(un)||[];if(!r)return e;const s=n.replace(r,"");let i=fu.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const mu=/([a-z-]*)\(.*?\)/g,$n={...$e,getAnimatableNone:e=>{const t=e.match(mu);return t?t.map(hu).join(" "):e}},pu={...xo,color:fe,backgroundColor:fe,outlineColor:fe,fill:fe,stroke:fe,borderColor:fe,borderTopColor:fe,borderRightColor:fe,borderBottomColor:fe,borderLeftColor:fe,filter:$n,WebkitFilter:$n},gr=e=>pu[e];function oi(e,t){let n=gr(e);return n!==$n&&(n=$e),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const ii=e=>/^0[^.\s]+$/.test(e);function gu(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||ii(e)}function yu(e,t,n,r){const s=Wn(t,n);let i;Array.isArray(n)?i=[...n]:i=[null,n];const o=r.from!==void 0?r.from:e.get();let a;const l=[];for(let c=0;c<i.length;c++)i[c]===null&&(i[c]=c===0?o:i[c-1]),gu(i[c])&&l.push(c),typeof i[c]=="string"&&i[c]!=="none"&&i[c]!=="0"&&(a=i[c]);if(s&&l.length&&a)for(let c=0;c<l.length;c++){const u=l[c];i[u]=oi(t,a)}return i}function xu({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function yr(e,t){return e[t]||e.default||e}const vu={skipAnimations:!1},xr=(e,t,n,r={})=>s=>{const i=yr(r,e)||{},o=i.delay||r.delay||0;let{elapsed:a=0}=r;a=a-et(o);const l=yu(t,e,n,i),c=l[0],u=l[l.length-1],d=Wn(e,c),f=Wn(e,u);let m={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-a,onUpdate:g=>{t.set(g),i.onUpdate&&i.onUpdate(g)},onComplete:()=>{s(),i.onComplete&&i.onComplete()}};if(xu(i)||(m={...m,...du(e,m)}),m.duration&&(m.duration=et(m.duration)),m.repeatDelay&&(m.repeatDelay=et(m.repeatDelay)),!d||!f||hc.current||i.type===!1||vu.skipAnimations)return iu(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const g=ou(t,e,m);if(g)return g}return Xt(m)};function Zt(e){return!!(ye(e)&&e.add)}const ai=e=>/^\-?\d*\.?\d+$/.test(e);function vr(e,t){e.indexOf(t)===-1&&e.push(t)}function br(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class wr{constructor(){this.subscriptions=[]}add(t){return vr(this.subscriptions,t),()=>br(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const o=this.subscriptions[i];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const bu=e=>!isNaN(parseFloat(e));class wu{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,s=!0)=>{this.prev=this.current,this.current=r;const{delta:i,timestamp:o}=ce;this.lastUpdated!==o&&(this.timeDelta=i,this.lastUpdated=o,K.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>K.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=bu(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new wr);const r=this.events[t].add(n);return t==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ni(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ht(e,t){return new wu(e,t)}const li=e=>t=>t.test(e),Su={test:e=>e==="auto",parse:e=>e},ci=[nt,A,Me,Oe,Rl,Ml,Su],yt=e=>ci.find(li(e)),Cu=[...ci,fe,$e],Tu=e=>Cu.find(li(e));function Pu(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ht(n))}function ku(e,t){const n=fn(e,t);let{transitionEnd:r={},transition:s={},...i}=n?e.makeTargetAnimatable(n,!1):{};i={...i,...r};for(const o in i){const a=$l(i[o]);Pu(e,o,a)}}function ju(e,t,n){var r,s;const i=Object.keys(t).filter(a=>!e.hasValue(a)),o=i.length;if(o)for(let a=0;a<o;a++){const l=i[a],c=t[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(s=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&s!==void 0?s:t[l]),u!=null&&(typeof u=="string"&&(ai(u)||ii(u))?u=parseFloat(u):!Tu(u)&&$e.test(c)&&(u=oi(l,c)),e.addValue(l,ht(u,{owner:e})),n[l]===void 0&&(n[l]=u),u!==null&&e.setBaseTarget(l,u))}}function Mu(e,t){return t?(t[e]||t.default||t).from:void 0}function Ru(e,t,n){const r={};for(const s in e){const i=Mu(s,t);if(i!==void 0)r[s]=i;else{const o=n.getValue(s);o&&(r[s]=o.get())}}return r}function Au({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Eu(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function ui(e,t,{delay:n=0,transitionOverride:r,type:s}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(i=r);const c=[],u=s&&e.animationState&&e.animationState.getState()[s];for(const d in a){const f=e.getValue(d),m=a[d];if(!f||m===void 0||u&&Au(u,d))continue;const g={delay:n,elapsed:0,...yr(i||{},d)};if(window.HandoffAppearAnimations){const v=e.getProps()[uo];if(v){const b=window.HandoffAppearAnimations(v,d,f,K);b!==null&&(g.elapsed=b,g.isHandoff=!0)}}let p=!g.isHandoff&&!Eu(f,m);if(g.type==="spring"&&(f.getVelocity()||g.velocity)&&(p=!1),f.animation&&(p=!1),p)continue;f.start(xr(d,f,m,e.shouldReduceMotion&&tt.has(d)?{type:!1}:g));const y=f.animation;Zt(l)&&(l.add(d),y.then(()=>l.remove(d))),c.push(y)}return o&&Promise.all(c).then(()=>{o&&ku(e,o)}),c}function Un(e,t,n={}){const r=fn(e,t,n.custom);let{transition:s=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(s=n.transitionOverride);const i=r?()=>Promise.all(ui(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:c=0,staggerChildren:u,staggerDirection:d}=s;return Vu(e,t,c+l,u,d,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[l,c]=a==="beforeChildren"?[i,o]:[o,i];return l().then(()=>c())}else return Promise.all([i(),o(n.delay)])}function Vu(e,t,n=0,r=0,s=1,i){const o=[],a=(e.variantChildren.size-1)*r,l=s===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(e.variantChildren).sort(Du).forEach((c,u)=>{c.notify("AnimationStart",t),o.push(Un(c,t,{...i,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(o)}function Du(e,t){return e.sortNodePosition(t)}function Nu(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>Un(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=Un(e,t,n);else{const s=typeof t=="function"?fn(e,t,n.custom):t;r=Promise.all(ui(e,s,n))}return r.then(()=>e.notify("AnimationComplete",t))}const Lu=[...nr].reverse(),Iu=nr.length;function Fu(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Nu(e,n,r)))}function Ou(e){let t=Fu(e);const n=zu();let r=!0;const s=(l,c)=>{const u=fn(e,c);if(u){const{transition:d,transitionEnd:f,...m}=u;l={...l,...m,...f}}return l};function i(l){t=l(e)}function o(l,c){const u=e.getProps(),d=e.getVariantContext(!0)||{},f=[],m=new Set;let g={},p=1/0;for(let v=0;v<Iu;v++){const b=Lu[v],w=n[b],C=u[b]!==void 0?u[b]:d[b],P=kt(C),R=b===c?w.isActive:null;R===!1&&(p=v);let E=C===d[b]&&C!==u[b]&&P;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),w.protectedKeys={...g},!w.isActive&&R===null||!C&&!w.prevProp||ln(C)||typeof C=="boolean")continue;let O=Bu(w.prevProp,C)||b===c&&w.isActive&&!E&&P||v>p&&P,V=!1;const ae=Array.isArray(C)?C:[C];let I=ae.reduce(s,{});R===!1&&(I={});const{prevResolvedValues:he={}}=w,X={...he,...I},Te=Z=>{O=!0,m.has(Z)&&(V=!0,m.delete(Z)),w.needsAnimating[Z]=!0};for(const Z in X){const xe=I[Z],Pe=he[Z];if(g.hasOwnProperty(Z))continue;let me=!1;Yt(xe)&&Yt(Pe)?me=!Do(xe,Pe):me=xe!==Pe,me?xe!==void 0?Te(Z):m.add(Z):xe!==void 0&&m.has(Z)?Te(Z):w.protectedKeys[Z]=!0}w.prevProp=C,w.prevResolvedValues=I,w.isActive&&(g={...g,...I}),r&&e.blockInitialAnimation&&(O=!1),O&&(!E||V)&&f.push(...ae.map(Z=>({animation:Z,options:{type:b,...l}})))}if(m.size){const v={};m.forEach(b=>{const w=e.getBaseTarget(b);w!==void 0&&(v[b]=w)}),f.push({animation:v})}let y=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(f):Promise.resolve()}function a(l,c,u){var d;if(n[l].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(m=>{var g;return(g=m.animationState)===null||g===void 0?void 0:g.setActive(l,c)}),n[l].isActive=c;const f=o(u,l);for(const m in n)n[m].protectedKeys={};return f}return{animateChanges:o,setActive:a,setAnimateFunction:i,getState:()=>n}}function Bu(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Do(t,e):!1}function Ke(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function zu(){return{animate:Ke(!0),whileInView:Ke(),whileHover:Ke(),whileTap:Ke(),whileDrag:Ke(),whileFocus:Ke(),exit:Ke()}}class _u extends Ue{constructor(t){super(t),t.animationState||(t.animationState=Ou(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),ln(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Wu=0;class $u extends Ue{constructor(){super(...arguments),this.id=Wu++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===s)return;const i=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Uu={animation:{Feature:_u},exit:{Feature:$u}},Jr=(e,t)=>Math.abs(e-t);function Gu(e,t){const n=Jr(e.x,t.x),r=Jr(e.y,t.y);return Math.sqrt(n**2+r**2)}class di{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Mn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Gu(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:g}=d,{timestamp:p}=ce;this.history.push({...g,timestamp:p});const{onStart:y,onMove:v}=this.handlers;f||(y&&y(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=jn(f,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:g,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=Mn(d.type==="pointercancel"?this.lastMoveEventInfo:jn(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,y),g&&g(d,y)},!Mo(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const o=dn(t),a=jn(o,this.transformPagePoint),{point:l}=a,{timestamp:c}=ce;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(t,Mn(a,this.history)),this.removeListeners=_e(Ve(this.contextWindow,"pointermove",this.handlePointerMove),Ve(this.contextWindow,"pointerup",this.handlePointerUp),Ve(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Ne(this.updatePoint)}}function jn(e,t){return t?{point:t(e.point)}:e}function Qr(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Mn({point:e},t){return{point:e,delta:Qr(e,fi(t)),offset:Qr(e,Hu(t)),velocity:Yu(t,.1)}}function Hu(e){return e[0]}function fi(e){return e[e.length-1]}function Yu(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=fi(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>et(t)));)n--;if(!r)return{x:0,y:0};const i=De(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const o={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function be(e){return e.max-e.min}function Gn(e,t=0,n=.01){return Math.abs(e-t)<=n}function es(e,t,n,r=.5){e.origin=r,e.originPoint=Q(t.min,t.max,e.origin),e.scale=be(n)/be(t),(Gn(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Q(n.min,n.max,e.origin)-e.originPoint,(Gn(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Tt(e,t,n,r){es(e.x,t.x,n.x,r?r.originX:void 0),es(e.y,t.y,n.y,r?r.originY:void 0)}function ts(e,t,n){e.min=n.min+t.min,e.max=e.min+be(t)}function Ku(e,t,n){ts(e.x,t.x,n.x),ts(e.y,t.y,n.y)}function ns(e,t,n){e.min=t.min-n.min,e.max=e.min+be(t)}function Pt(e,t,n){ns(e.x,t.x,n.x),ns(e.y,t.y,n.y)}function qu(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Q(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Q(n,e,r.max):Math.min(e,n)),e}function rs(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Xu(e,{top:t,left:n,bottom:r,right:s}){return{x:rs(e.x,n,s),y:rs(e.y,t,r)}}function ss(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Zu(e,t){return{x:ss(e.x,t.x),y:ss(e.y,t.y)}}function Ju(e,t){let n=.5;const r=be(e),s=be(t);return s>r?n=Mt(t.min,t.max-r,e.min):r>s&&(n=Mt(e.min,e.max-s,t.min)),We(0,1,n)}function Qu(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Hn=.35;function ed(e=Hn){return e===!1?e=0:e===!0&&(e=Hn),{x:os(e,"left","right"),y:os(e,"top","bottom")}}function os(e,t,n){return{min:is(e,t),max:is(e,n)}}function is(e,t){return typeof e=="number"?e:e[t]||0}const as=()=>({translate:0,scale:1,origin:0,originPoint:0}),ct=()=>({x:as(),y:as()}),ls=()=>({min:0,max:0}),oe=()=>({x:ls(),y:ls()});function Ce(e){return[e("x"),e("y")]}function hi({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function td({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function nd(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Rn(e){return e===void 0||e===1}function Yn({scale:e,scaleX:t,scaleY:n}){return!Rn(e)||!Rn(t)||!Rn(n)}function qe(e){return Yn(e)||mi(e)||e.z||e.rotate||e.rotateX||e.rotateY}function mi(e){return cs(e.x)||cs(e.y)}function cs(e){return e&&e!=="0%"}function Jt(e,t,n){const r=e-n,s=t*r;return n+s}function us(e,t,n,r,s){return s!==void 0&&(e=Jt(e,s,r)),Jt(e,n,r)+t}function Kn(e,t=0,n=1,r,s){e.min=us(e.min,t,n,r,s),e.max=us(e.max,t,n,r,s)}function pi(e,{x:t,y:n}){Kn(e.x,t.translate,t.scale,t.originPoint),Kn(e.y,n.translate,n.scale,n.originPoint)}function rd(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,o;for(let a=0;a<s;a++){i=n[a],o=i.projectionDelta;const l=i.instance;l&&l.style&&l.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ut(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,pi(e,o)),r&&qe(i.latestValues)&&ut(e,i.latestValues))}t.x=ds(t.x),t.y=ds(t.y)}function ds(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Be(e,t){e.min=e.min+t,e.max=e.max+t}function fs(e,t,[n,r,s]){const i=t[s]!==void 0?t[s]:.5,o=Q(e.min,e.max,i);Kn(e,t[n],t[r],o,t.scale)}const sd=["x","scaleX","originX"],od=["y","scaleY","originY"];function ut(e,t){fs(e.x,t,sd),fs(e.y,t,od)}function gi(e,t){return hi(nd(e.getBoundingClientRect(),t))}function id(e,t,n){const r=gi(e,n),{scroll:s}=t;return s&&(Be(r.x,s.offset.x),Be(r.y,s.offset.y)),r}const yi=({current:e})=>e?e.ownerDocument.defaultView:null,ad=new WeakMap;class ld{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=oe(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=u=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(dn(u,"page").point)},i=(u,d)=>{const{drag:f,dragPropagation:m,onDragStart:g}=this.getProps();if(f&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Ao(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ce(y=>{let v=this.getAxisMotionValue(y).get()||0;if(Me.test(v)){const{projection:b}=this.visualElement;if(b&&b.layout){const w=b.layout.layoutBox[y];w&&(v=be(w)*(parseFloat(v)/100))}}this.originPoint[y]=v}),g&&K.update(()=>g(u,d),!1,!0);const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(u,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:g,onDrag:p}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:y}=d;if(m&&this.currentDirection===null){this.currentDirection=cd(y),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",d.point,y),this.updateAxis("y",d.point,y),this.visualElement.render(),p&&p(u,d)},a=(u,d)=>this.stop(u,d),l=()=>Ce(u=>{var d;return this.getAnimationState(u)==="paused"&&((d=this.getAxisMotionValue(u).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new di(t,{onSessionStart:s,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:yi(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&K.update(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!Bt(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=qu(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&at(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=Xu(s.layoutBox,n):this.constraints=!1,this.elastic=ed(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&Ce(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=Qu(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!at(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=id(r,s.root,this.visualElement.getTransformPagePoint());let o=Zu(s.layout.layoutBox,i);if(n){const a=n(td(o));this.hasMutatedConstraints=!!a,a&&(o=hi(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=Ce(u=>{if(!Bt(u,n,this.currentDirection))return;let d=l&&l[u]||{};o&&(d={min:0,max:0});const f=s?200:1e6,m=s?40:1e7,g={type:"inertia",velocity:r?t[u]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(u,g)});return Promise.all(c).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(xr(t,r,0,n))}stopAnimation(){Ce(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ce(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ce(n=>{const{drag:r}=this.getProps();if(!Bt(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[n];i.set(t[n]-Q(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!at(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};Ce(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();s[o]=Ju({min:l,max:l},this.constraints[o])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ce(o=>{if(!Bt(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:c}=this.constraints[o];a.set(Q(l,c,s[o]))})}addListeners(){if(!this.visualElement.current)return;ad.set(this.visualElement,this);const t=this.visualElement.current,n=Ve(t,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();at(l)&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),r();const o=Ee(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(Ce(u=>{const d=this.getAxisMotionValue(u);d&&(this.originPoint[u]+=l[u].translate,d.set(d.get()+l[u].translate))}),this.visualElement.render())});return()=>{o(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:o=Hn,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function Bt(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function cd(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class ud extends Ue{constructor(t){super(t),this.removeGroupControls=ne,this.removeListeners=ne,this.controls=new ld(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ne}unmount(){this.removeGroupControls(),this.removeListeners()}}const hs=e=>(t,n)=>{e&&K.update(()=>e(t,n))};class dd extends Ue{constructor(){super(...arguments),this.removePointerDownListener=ne}onPointerDown(t){this.session=new di(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:yi(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:hs(t),onStart:hs(n),onMove:r,onEnd:(i,o)=>{delete this.session,s&&K.update(()=>s(i,o))}}}mount(){this.removePointerDownListener=Ve(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function fd(){const e=x.useContext(on);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,s=x.useId();return x.useEffect(()=>r(s),[]),!t&&n?[!1,()=>n&&n(s)]:[!0]}const Wt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ms(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const xt={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(A.test(e))e=parseFloat(e);else return e;const n=ms(e,t.target.x),r=ms(e,t.target.y);return`${n}% ${r}%`}},hd={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=$e.parse(e);if(s.length>5)return r;const i=$e.createTransformer(e),o=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;s[0+o]/=a,s[1+o]/=l;const c=Q(a,l,.5);return typeof s[2+o]=="number"&&(s[2+o]/=c),typeof s[3+o]=="number"&&(s[3+o]/=c),i(s)}};class md extends ue.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;wl(pd),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Wt.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,s||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||K.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function xi(e){const[t,n]=fd(),r=x.useContext(sr);return ue.createElement(md,{...e,layoutGroup:r,switchLayoutGroup:x.useContext(ho),isPresent:t,safeToRemove:n})}const pd={borderRadius:{...xt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomLeftRadius:xt,borderBottomRightRadius:xt,boxShadow:hd},vi=["TopLeft","TopRight","BottomLeft","BottomRight"],gd=vi.length,ps=e=>typeof e=="string"?parseFloat(e):e,gs=e=>typeof e=="number"||A.test(e);function yd(e,t,n,r,s,i){s?(e.opacity=Q(0,n.opacity!==void 0?n.opacity:1,xd(r)),e.opacityExit=Q(t.opacity!==void 0?t.opacity:1,0,vd(r))):i&&(e.opacity=Q(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<gd;o++){const a=`border${vi[o]}Radius`;let l=ys(t,a),c=ys(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||gs(l)===gs(c)?(e[a]=Math.max(Q(ps(l),ps(c),r),0),(Me.test(c)||Me.test(l))&&(e[a]+="%")):e[a]=c}(t.rotate||n.rotate)&&(e.rotate=Q(t.rotate||0,n.rotate||0,r))}function ys(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const xd=bi(0,.5,Wo),vd=bi(.5,.95,ne);function bi(e,t,n){return r=>r<e?0:r>t?1:n(Mt(e,t,r))}function xs(e,t){e.min=t.min,e.max=t.max}function Se(e,t){xs(e.x,t.x),xs(e.y,t.y)}function vs(e,t,n,r,s){return e-=t,e=Jt(e,1/n,r),s!==void 0&&(e=Jt(e,1/s,r)),e}function bd(e,t=0,n=1,r=.5,s,i=e,o=e){if(Me.test(t)&&(t=parseFloat(t),t=Q(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=Q(i.min,i.max,r);e===i&&(a-=t),e.min=vs(e.min,t,n,a,s),e.max=vs(e.max,t,n,a,s)}function bs(e,t,[n,r,s],i,o){bd(e,t[n],t[r],t[s],t.scale,i,o)}const wd=["x","scaleX","originX"],Sd=["y","scaleY","originY"];function ws(e,t,n,r){bs(e.x,t,wd,n?n.x:void 0,r?r.x:void 0),bs(e.y,t,Sd,n?n.y:void 0,r?r.y:void 0)}function Ss(e){return e.translate===0&&e.scale===1}function wi(e){return Ss(e.x)&&Ss(e.y)}function Cd(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Si(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Cs(e){return be(e.x)/be(e.y)}class Td{constructor(){this.members=[]}add(t){vr(this.members,t),t.scheduleRender()}remove(t){if(br(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ts(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y;if((s||i)&&(r=`translate3d(${s}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(r+=`rotate(${l}deg) `),c&&(r+=`rotateX(${c}deg) `),u&&(r+=`rotateY(${u}deg) `)}const o=e.x.scale*t.x,a=e.y.scale*t.y;return(o!==1||a!==1)&&(r+=`scale(${o}, ${a})`),r||"none"}const Pd=(e,t)=>e.depth-t.depth;class kd{constructor(){this.children=[],this.isDirty=!1}add(t){vr(this.children,t),this.isDirty=!0}remove(t){br(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Pd),this.isDirty=!1,this.children.forEach(t)}}function jd(e,t){const n=performance.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(Ne(r),e(i-t))};return K.read(r,!0),()=>Ne(r)}function Md(e){window.MotionDebug&&window.MotionDebug.record(e)}function Rd(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Ad(e,t,n){const r=ye(e)?e:ht(e);return r.start(xr("",r,t,n)),r.animation}const Ps=["","X","Y","Z"],Ed={visibility:"hidden"},ks=1e3;let Vd=0;const Xe={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Ci({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=Vd++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Xe.totalNodes=Xe.resolvedTargetDeltas=Xe.recalculatedProjection=0,this.nodes.forEach(Ld),this.nodes.forEach(zd),this.nodes.forEach(_d),this.nodes.forEach(Id),Md(Xe)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new kd)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new wr),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Rd(o),this.instance=o;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=jd(f,250),Wt.hasAnimatedSinceResize&&(Wt.hasAnimatedSinceResize=!1,this.nodes.forEach(Ms))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Hd,{onLayoutAnimationStart:y,onLayoutAnimationComplete:v}=u.getProps(),b=!this.targetLayout||!Si(this.targetLayout,g)||m,w=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||w||f&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,w);const C={...yr(p,"layout"),onPlay:y,onComplete:v};(u.shouldReduceMotion||this.options.layoutRoot)&&(C.delay=0,C.type=!1),this.startAnimation(C)}else f||Ms(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Ne(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Wd),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const d=this.path[u];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(js);return}this.isUpdating||this.nodes.forEach(Od),this.isUpdating=!1,this.nodes.forEach(Bd),this.nodes.forEach(Dd),this.nodes.forEach(Nd),this.clearAllSnapshots();const a=performance.now();ce.delta=We(0,1e3/60,a-ce.timestamp),ce.timestamp=a,ce.isProcessing=!0,vn.update.process(ce),vn.preRender.process(ce),vn.render.process(ce),ce.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Fd),this.sharedNodes.forEach($d)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=oe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!wi(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&(a||qe(this.latestValues)||u)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),Yd(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return oe();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(Be(a.x,l.offset.x),Be(a.y,l.offset.y)),a}removeElementScroll(o){const a=oe();Se(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:d}=c;if(c!==this.root&&u&&d.layoutScroll){if(u.isRoot){Se(a,o);const{scroll:f}=this.root;f&&(Be(a.x,-f.offset.x),Be(a.y,-f.offset.y))}Be(a.x,u.offset.x),Be(a.y,u.offset.y)}}return a}applyTransform(o,a=!1){const l=oe();Se(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&ut(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),qe(u.latestValues)&&ut(l,u.latestValues)}return qe(this.latestValues)&&ut(l,this.latestValues),l}removeTransform(o){const a=oe();Se(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!qe(c.latestValues))continue;Yn(c.latestValues)&&c.updateSnapshot();const u=oe(),d=c.measurePageBox();Se(u,d),ws(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return qe(this.latestValues)&&ws(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ce.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=ce.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=oe(),this.relativeTargetOrigin=oe(),Pt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Se(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=oe(),this.targetWithTransforms=oe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ku(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Se(this.target,this.layout.layoutBox),pi(this.target,this.targetDelta)):Se(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=oe(),this.relativeTargetOrigin=oe(),Pt(this.relativeTargetOrigin,this.target,m.target),Se(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Xe.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Yn(this.parent.latestValues)||mi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===ce.timestamp&&(c=!1),c)return;const{layout:u,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||d))return;Se(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;rd(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:g}=a;if(!g){this.projectionTransform&&(this.projectionDelta=ct(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=ct(),this.projectionDeltaWithTransform=ct());const p=this.projectionTransform;Tt(this.projectionDelta,this.layoutCorrected,g,this.latestValues),this.projectionTransform=Ts(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==f||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g)),Xe.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},d=ct();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=oe(),m=l?l.source:void 0,g=this.layout?this.layout.source:void 0,p=m!==g,y=this.getStack(),v=!y||y.members.length<=1,b=!!(p&&!v&&this.options.crossfade===!0&&!this.path.some(Gd));this.animationProgress=0;let w;this.mixTargetDelta=C=>{const P=C/1e3;Rs(d.x,o.x,P),Rs(d.y,o.y,P),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Pt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Ud(this.relativeTarget,this.relativeTargetOrigin,f,P),w&&Cd(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=oe()),Se(w,this.relativeTarget)),p&&(this.animationValues=u,yd(u,c,this.latestValues,P,b,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Ne(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{Wt.hasAnimatedSinceResize=!0,this.currentAnimation=Ad(0,ks,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ks),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&Ti(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||oe();const d=be(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const f=be(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}Se(a,l),ut(a,u),Tt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Td),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<Ps.length;u++){const d="rotate"+Ps[u];l[d]&&(c[d]=l[d],o.setStaticValue(d,0))}o.render();for(const u in c)o.setStaticValue(u,c[u]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ed;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=_t(o==null?void 0:o.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const p={};return this.options.layoutId&&(p.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,p.pointerEvents=_t(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!qe(this.latestValues)&&(p.transform=u?u({},""):"none",this.hasProjected=!1),p}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Ts(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:m,y:g}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${g.origin*100}% 0`,d.animationValues?c.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const p in Gt){if(f[p]===void 0)continue;const{correct:y,applyTo:v}=Gt[p],b=c.transform==="none"?f[p]:y(f[p],d);if(v){const w=v.length;for(let C=0;C<w;C++)c[v[C]]=b}else c[p]=b}return this.options.layoutId&&(c.pointerEvents=d===this?_t(o==null?void 0:o.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(js),this.root.sharedNodes.clear()}}}function Dd(e){e.updateLayout()}function Nd(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;i==="size"?Ce(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],m=be(f);f.min=r[d].min,f.max=f.min+m}):Ti(i,n.layoutBox,r)&&Ce(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],m=be(r[d]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const a=ct();Tt(a,r,n.layoutBox);const l=ct();o?Tt(l,e.applyTransform(s,!0),n.measuredBox):Tt(l,r,n.layoutBox);const c=!wi(a);let u=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const g=oe();Pt(g,n.layoutBox,f.layoutBox);const p=oe();Pt(p,r,m.layoutBox),Si(g,p)||(u=!0),d.options.layoutRoot&&(e.relativeTarget=p,e.relativeTargetOrigin=g,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Ld(e){Xe.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Id(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Fd(e){e.clearSnapshot()}function js(e){e.clearMeasurements()}function Od(e){e.isLayoutDirty=!1}function Bd(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ms(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function zd(e){e.resolveTargetDelta()}function _d(e){e.calcProjection()}function Wd(e){e.resetRotation()}function $d(e){e.removeLeadSnapshot()}function Rs(e,t,n){e.translate=Q(t.translate,0,n),e.scale=Q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function As(e,t,n,r){e.min=Q(t.min,n.min,r),e.max=Q(t.max,n.max,r)}function Ud(e,t,n,r){As(e.x,t.x,n.x,r),As(e.y,t.y,n.y,r)}function Gd(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Hd={duration:.45,ease:[.4,0,.1,1]},Es=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Vs=Es("applewebkit/")&&!Es("chrome/")?Math.round:ne;function Ds(e){e.min=Vs(e.min),e.max=Vs(e.max)}function Yd(e){Ds(e.x),Ds(e.y)}function Ti(e,t,n){return e==="position"||e==="preserve-aspect"&&!Gn(Cs(t),Cs(n),.2)}const Kd=Ci({attachResizeListener:(e,t)=>Ee(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),An={current:void 0},Pi=Ci({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!An.current){const e=new Kd({});e.mount(window),e.setOptions({layoutScroll:!0}),An.current=e}return An.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),qd={pan:{Feature:dd},drag:{Feature:ud,ProjectionNode:Pi,MeasureLayout:xi}},Xd=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Zd(e){const t=Xd.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function qn(e,t,n=1){const[r,s]=Zd(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const o=i.trim();return ai(o)?parseFloat(o):o}else return On(s)?qn(s,t,n+1):s}function Jd(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(s=>{const i=s.get();if(!On(i))return;const o=qn(i,r);o&&s.set(o)});for(const s in t){const i=t[s];if(!On(i))continue;const o=qn(i,r);o&&(t[s]=o,n||(n={}),n[s]===void 0&&(n[s]=i))}return{target:t,transitionEnd:n}}const Qd=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ki=e=>Qd.has(e),ef=e=>Object.keys(e).some(ki),Ns=e=>e===nt||e===A,Ls=(e,t)=>parseFloat(e.split(", ")[t]),Is=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/);if(s)return Ls(s[1],t);{const i=r.match(/^matrix\((.+)\)$/);return i?Ls(i[1],e):0}},tf=new Set(["x","y","z"]),nf=At.filter(e=>!tf.has(e));function rf(e){const t=[];return nf.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const mt={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Is(4,13),y:Is(5,14)};mt.translateX=mt.x;mt.translateY=mt.y;const sf=(e,t,n)=>{const r=t.measureViewportBox(),s=t.current,i=getComputedStyle(s),{display:o}=i,a={};o==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(c=>{a[c]=mt[c](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(c=>{const u=t.getValue(c);u&&u.jump(a[c]),e[c]=mt[c](l,i)}),e},of=(e,t,n={},r={})=>{t={...t},r={...r};const s=Object.keys(t).filter(ki);let i=[],o=!1;const a=[];if(s.forEach(l=>{const c=e.getValue(l);if(!e.hasValue(l))return;let u=n[l],d=yt(u);const f=t[l];let m;if(Yt(f)){const g=f.length,p=f[0]===null?1:0;u=f[p],d=yt(u);for(let y=p;y<g&&f[y]!==null;y++)m?fr(yt(f[y])===m):m=yt(f[y])}else m=yt(f);if(d!==m)if(Ns(d)&&Ns(m)){const g=c.get();typeof g=="string"&&c.set(parseFloat(g)),typeof f=="string"?t[l]=parseFloat(f):Array.isArray(f)&&m===A&&(t[l]=f.map(parseFloat))}else d!=null&&d.transform&&(m!=null&&m.transform)&&(u===0||f===0)?u===0?c.set(m.transform(u)):t[l]=d.transform(f):(o||(i=rf(e),o=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],c.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=sf(t,e,a);return i.length&&i.forEach(([u,d])=>{e.getValue(u).set(d)}),e.render(),an&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:r}}else return{target:t,transitionEnd:r}};function af(e,t,n,r){return ef(t)?of(e,t,n,r):{target:t,transitionEnd:r}}const lf=(e,t,n,r)=>{const s=Jd(e,t,r);return t=s.target,r=s.transitionEnd,af(e,t,n,r)},Xn={current:null},ji={current:!1};function cf(){if(ji.current=!0,!!an)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Xn.current=e.matches;e.addListener(t),t()}else Xn.current=!1}function uf(e,t,n){const{willChange:r}=t;for(const s in t){const i=t[s],o=n[s];if(ye(i))e.addValue(s,i),Zt(r)&&r.add(s);else if(ye(o))e.addValue(s,ht(i,{owner:e})),Zt(r)&&r.remove(s);else if(o!==i)if(e.hasValue(s)){const a=e.getValue(s);!a.hasAnimated&&a.set(i)}else{const a=e.getStaticValue(s);e.addValue(s,ht(a!==void 0?a:i,{owner:e}))}}for(const s in n)t[s]===void 0&&e.removeValue(s);return t}const Fs=new WeakMap,Mi=Object.keys(jt),df=Mi.length,Os=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],ff=rr.length;class hf{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>K.render(this.render,!1,!0);const{latestValues:a,renderState:l}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.isControllingVariants=cn(n),this.isVariantNode=fo(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...u}=this.scrapeMotionValuesFromProps(n,{});for(const d in u){const f=u[d];a[d]!==void 0&&ye(f)&&(f.set(a[d],!1),Zt(c)&&c.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Fs.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),ji.current||cf(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Xn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Fs.delete(this.current),this.projection&&this.projection.unmount(),Ne(this.notifyUpdate),Ne(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=tt.has(t),s=n.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&K.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{s(),i()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,s,i){let o,a;for(let l=0;l<df;l++){const c=Mi[l],{isEnabled:u,Feature:d,ProjectionNode:f,MeasureLayout:m}=jt[c];f&&(o=f),u(n)&&(!this.features[c]&&d&&(this.features[c]=new d(this)),m&&(a=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:d,layoutScroll:f,layoutRoot:m}=n;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||d&&at(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:i,layoutScroll:f,layoutRoot:m})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):oe()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Os.length;r++){const s=Os[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i=t["on"+s];i&&(this.propEventSubscriptions[s]=this.on(s,i))}this.prevMotionValues=uf(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<ff;r++){const s=rr[r],i=this.props[s];(kt(i)||i===!1)&&(n[s]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ht(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,s=typeof r=="string"||typeof r=="object"?(n=dr(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!ye(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new wr),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Ri extends hf{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:s},i){let o=Ru(r,t||{},this);if(s&&(n&&(n=s(n)),r&&(r=s(r)),o&&(o=s(o))),i){ju(this,r,o);const a=lf(this,r,o,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function mf(e){return window.getComputedStyle(e)}class pf extends Ri{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(tt.has(n)){const r=gr(n);return r&&r.default||0}else{const r=mf(t),s=(go(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return gi(t,n)}build(t,n,r,s){ir(t,n,r,s.transformTemplate)}scrapeMotionValuesFromProps(t,n){return ur(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ye(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,s){So(t,n,r,s)}}class gf extends Ri{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(tt.has(n)){const r=gr(n);return r&&r.default||0}return n=Co.has(n)?n:tr(n),t.getAttribute(n)}measureInstanceViewportBox(){return oe()}scrapeMotionValuesFromProps(t,n){return Po(t,n)}build(t,n,r,s){lr(t,n,r,this.isSVGTag,s.transformTemplate)}renderInstance(t,n,r,s){To(t,n,r,s)}mount(t){this.isSVGTag=cr(t.tagName),super.mount(t)}}const yf=(e,t)=>or(e)?new gf(t,{enableHardwareAcceleration:!1}):new pf(t,{enableHardwareAcceleration:!0}),xf={layout:{ProjectionNode:Pi,MeasureLayout:xi}},vf={...Uu,...uc,...qd,...xf},te=vl((e,t)=>Zl(e,t,vf,yf));function Ai(){const e=x.useRef(!1);return er(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function bf(){const e=Ai(),[t,n]=x.useState(0),r=x.useCallback(()=>{e.current&&n(t+1)},[t]);return[x.useCallback(()=>K.postRender(r),[r]),t]}class wf extends x.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Sf({children:e,isPresent:t}){const n=x.useId(),r=x.useRef(null),s=x.useRef({width:0,height:0,top:0,left:0});return x.useInsertionEffect(()=>{const{width:i,height:o,top:a,left:l}=s.current;if(t||!r.current||!i||!o)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${o}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),x.createElement(wf,{isPresent:t,childRef:r,sizeRef:s},x.cloneElement(e,{ref:r}))}const En=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:i,mode:o})=>{const a=ko(Cf),l=x.useId(),c=x.useMemo(()=>({id:l,initial:t,isPresent:n,custom:s,onExitComplete:u=>{a.set(u,!0);for(const d of a.values())if(!d)return;r&&r()},register:u=>(a.set(u,!1),()=>a.delete(u))}),i?void 0:[n]);return x.useMemo(()=>{a.forEach((u,d)=>a.set(d,!1))},[n]),x.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=x.createElement(Sf,{isPresent:n},e)),x.createElement(on.Provider,{value:c},e)};function Cf(){return new Map}function Tf(e){return x.useEffect(()=>()=>e(),[])}const Ze=e=>e.key||"";function Pf(e,t){e.forEach(n=>{const r=Ze(n);t.set(r,n)})}function kf(e){const t=[];return x.Children.forEach(e,n=>{x.isValidElement(n)&&t.push(n)}),t}const Ei=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:s,presenceAffectsLayout:i=!0,mode:o="sync"})=>{const a=x.useContext(sr).forceRender||bf()[0],l=Ai(),c=kf(e);let u=c;const d=x.useRef(new Map).current,f=x.useRef(u),m=x.useRef(new Map).current,g=x.useRef(!0);if(er(()=>{g.current=!1,Pf(c,m),f.current=u}),Tf(()=>{g.current=!0,m.clear(),d.clear()}),g.current)return x.createElement(x.Fragment,null,u.map(b=>x.createElement(En,{key:Ze(b),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:i,mode:o},b)));u=[...u];const p=f.current.map(Ze),y=c.map(Ze),v=p.length;for(let b=0;b<v;b++){const w=p[b];y.indexOf(w)===-1&&!d.has(w)&&d.set(w,void 0)}return o==="wait"&&d.size&&(u=[]),d.forEach((b,w)=>{if(y.indexOf(w)!==-1)return;const C=m.get(w);if(!C)return;const P=p.indexOf(w);let R=b;if(!R){const E=()=>{d.delete(w);const j=Array.from(m.keys()).filter(O=>!y.includes(O));if(j.forEach(O=>m.delete(O)),f.current=c.filter(O=>{const V=Ze(O);return V===w||j.includes(V)}),!d.size){if(l.current===!1)return;a(),r&&r()}};R=x.createElement(En,{key:Ze(C),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:i,mode:o},C),d.set(w,R)}u.splice(P,0,R)}),u=u.map(b=>{const w=b.key;return d.has(w)?b:x.createElement(En,{key:Ze(b),isPresent:!0,presenceAffectsLayout:i,mode:o},b)}),x.createElement(x.Fragment,null,d.size?u:u.map(b=>x.cloneElement(b)))};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var jf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),Y=(e,t)=>{const n=x.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:a="",children:l,...c},u)=>x.createElement("svg",{ref:u,...jf,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:["lucide",`lucide-${Mf(e)}`,a].join(" "),...c},[...t.map(([d,f])=>x.createElement(d,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rf=Y("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vi=Y("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Af=Y("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ef=Y("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sr=Y("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=Y("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Df=Y("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nf=Y("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lf=Y("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const If=Y("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ff=Y("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Of=Y("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=Y("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=Y("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _f=Y("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=Y("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Di=Y("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $f=Y("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=Y("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=Y("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=Y("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hf=Y("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=Y("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kf=Y("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=Y("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xf=Y("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=Y("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Jf={},Bs=e=>{let t;const n=new Set,r=(u,d)=>{const f=typeof u=="function"?u(t):u;if(!Object.is(f,t)){const m=t;t=d??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(g=>g(t,m))}},s=()=>t,l={setState:r,getState:s,getInitialState:()=>c,subscribe:u=>(n.add(u),()=>n.delete(u)),destroy:()=>{(Jf?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,s,l);return l},Qf=e=>e?Bs(e):Bs;var Li={exports:{}},Ii={},Fi={exports:{}},Oi={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pt=x;function eh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var th=typeof Object.is=="function"?Object.is:eh,nh=pt.useState,rh=pt.useEffect,sh=pt.useLayoutEffect,oh=pt.useDebugValue;function ih(e,t){var n=t(),r=nh({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return sh(function(){s.value=n,s.getSnapshot=t,Vn(s)&&i({inst:s})},[e,n,t]),rh(function(){return Vn(s)&&i({inst:s}),e(function(){Vn(s)&&i({inst:s})})},[e]),oh(n),n}function Vn(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!th(e,n)}catch{return!0}}function ah(e,t){return t()}var lh=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?ah:ih;Oi.useSyncExternalStore=pt.useSyncExternalStore!==void 0?pt.useSyncExternalStore:lh;Fi.exports=Oi;var ch=Fi.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hn=x,uh=ch;function dh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var fh=typeof Object.is=="function"?Object.is:dh,hh=uh.useSyncExternalStore,mh=hn.useRef,ph=hn.useEffect,gh=hn.useMemo,yh=hn.useDebugValue;Ii.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var i=mh(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=gh(function(){function l(m){if(!c){if(c=!0,u=m,m=r(m),s!==void 0&&o.hasValue){var g=o.value;if(s(g,m))return d=g}return d=m}if(g=d,fh(u,m))return g;var p=r(m);return s!==void 0&&s(g,p)?(u=m,g):(u=m,d=p)}var c=!1,u,d,f=n===void 0?null:n;return[function(){return l(t())},f===null?void 0:function(){return l(f())}]},[t,n,r,s]);var a=hh(e,i[0],i[1]);return ph(function(){o.hasValue=!0,o.value=a},[a]),yh(a),a};Li.exports=Ii;var xh=Li.exports;const vh=Ks(xh),Bi={},{useDebugValue:bh}=ue,{useSyncExternalStoreWithSelector:wh}=vh;let zs=!1;const Sh=e=>e;function Ch(e,t=Sh,n){(Bi?"production":void 0)!=="production"&&n&&!zs&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),zs=!0);const r=wh(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return bh(r),r}const Th=e=>{(Bi?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Qf(e):e,n=(r,s)=>Ch(t,r,s);return Object.assign(n,t),n},Ph=e=>Th,kh=e=>(t,n,r)=>{const s=r.subscribe;return r.subscribe=(o,a,l)=>{let c=o;if(a){const u=(l==null?void 0:l.equalityFn)||Object.is;let d=o(r.getState());c=f=>{const m=o(f);if(!u(d,m)){const g=d;a(d=m,g)}},l!=null&&l.fireImmediately&&a(d,d)}return s(c)},e(t,n,r)},jh=kh,Mh={isOnline:!1,lastUpdate:new Date().toISOString(),dataSource:{weibo:!1,zhihu:!1,news:!1},performance:{cpu:0,memory:0,network:0}},Rh={layout:"grid",refreshInterval:3e4,autoRefresh:!0,theme:"dark"},Ge=Ph()(jh((e,t)=>({realTimeData:null,systemStatus:Mh,dashboardConfig:Rh,isLoading:!1,error:null,selectedTimeRange:"24h",isConnected:!1,connectionRetries:0,setRealTimeData:n=>e({realTimeData:n}),updateStatistics:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,statistics:n}:null})),updateHotTopics:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,hotTopics:n}:null})),updateKeywords:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,keywords:n}:null})),updateTimeSeries:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,timeSeries:n}:null})),updateLocations:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,locations:n}:null})),addRecentPost:n=>e(r=>({realTimeData:r.realTimeData?{...r.realTimeData,recentPosts:[n,...r.realTimeData.recentPosts.slice(0,49)]}:null})),setSystemStatus:n=>e({systemStatus:n}),setDashboardConfig:n=>e(r=>({dashboardConfig:{...r.dashboardConfig,...n}})),setLoading:n=>e({isLoading:n}),setError:n=>e({error:n}),setSelectedTimeRange:n=>e({selectedTimeRange:n}),setConnectionStatus:n=>e({isConnected:n}),incrementRetries:()=>e(n=>({connectionRetries:n.connectionRetries+1})),resetRetries:()=>e({connectionRetries:0}),clearData:()=>e({realTimeData:null,error:null,isLoading:!1})}))),Cr="-",Ah=e=>{const t=Vh(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const a=o.split(Cr);return a[0]===""&&a.length!==1&&a.shift(),zi(a,t)||Eh(o)},getConflictingClassGroupIds:(o,a)=>{const l=n[o]||[];return a&&r[o]?[...l,...r[o]]:l}}},zi=(e,t)=>{var o;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?zi(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const i=e.join(Cr);return(o=t.validators.find(({validator:a})=>a(i)))==null?void 0:o.classGroupId},_s=/^\[(.+)\]$/,Eh=e=>{if(_s.test(e)){const t=_s.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Vh=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Nh(Object.entries(e.classGroups),n).forEach(([i,o])=>{Zn(o,r,i,t)}),r},Zn=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const i=s===""?t:Ws(t,s);i.classGroupId=n;return}if(typeof s=="function"){if(Dh(s)){Zn(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([i,o])=>{Zn(o,Ws(t,i),n,r)})})},Ws=(e,t)=>{let n=e;return t.split(Cr).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Dh=e=>e.isThemeGetter,Nh=(e,t)=>t?e.map(([n,r])=>{const s=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([o,a])=>[t+o,a])):i);return[n,s]}):e,Lh=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(i,o)=>{n.set(i,o),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let o=n.get(i);if(o!==void 0)return o;if((o=r.get(i))!==void 0)return s(i,o),o},set(i,o){n.has(i)?n.set(i,o):s(i,o)}}},_i="!",Ih=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],i=t.length,o=a=>{const l=[];let c=0,u=0,d;for(let y=0;y<a.length;y++){let v=a[y];if(c===0){if(v===s&&(r||a.slice(y,y+i)===t)){l.push(a.slice(u,y)),u=y+i;continue}if(v==="/"){d=y;continue}}v==="["?c++:v==="]"&&c--}const f=l.length===0?a:a.substring(u),m=f.startsWith(_i),g=m?f.substring(1):f,p=d&&d>u?d-u:void 0;return{modifiers:l,hasImportantModifier:m,baseClassName:g,maybePostfixModifierPosition:p}};return n?a=>n({className:a,parseClassName:o}):o},Fh=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Oh=e=>({cache:Lh(e.cacheSize),parseClassName:Ih(e),...Ah(e)}),Bh=/\s+/,zh=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,i=[],o=e.trim().split(Bh);let a="";for(let l=o.length-1;l>=0;l-=1){const c=o[l],{modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:m}=n(c);let g=!!m,p=r(g?f.substring(0,m):f);if(!p){if(!g){a=c+(a.length>0?" "+a:a);continue}if(p=r(f),!p){a=c+(a.length>0?" "+a:a);continue}g=!1}const y=Fh(u).join(":"),v=d?y+_i:y,b=v+p;if(i.includes(b))continue;i.push(b);const w=s(p,g);for(let C=0;C<w.length;++C){const P=w[C];i.push(v+P)}a=c+(a.length>0?" "+a:a)}return a};function _h(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Wi(t))&&(r&&(r+=" "),r+=n);return r}const Wi=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Wi(e[r]))&&(n&&(n+=" "),n+=t);return n};function Wh(e,...t){let n,r,s,i=o;function o(l){const c=t.reduce((u,d)=>d(u),e());return n=Oh(c),r=n.cache.get,s=n.cache.set,i=a,a(l)}function a(l){const c=r(l);if(c)return c;const u=zh(l,n);return s(l,u),u}return function(){return i(_h.apply(null,arguments))}}const q=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},$i=/^\[(?:([a-z-]+):)?(.+)\]$/i,$h=/^\d+\/\d+$/,Uh=new Set(["px","full","screen"]),Gh=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Hh=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Yh=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Kh=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,qh=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ae=e=>ft(e)||Uh.has(e)||$h.test(e),Ie=e=>gt(e,"length",rm),ft=e=>!!e&&!Number.isNaN(Number(e)),Dn=e=>gt(e,"number",ft),vt=e=>!!e&&Number.isInteger(Number(e)),Xh=e=>e.endsWith("%")&&ft(e.slice(0,-1)),L=e=>$i.test(e),Fe=e=>Gh.test(e),Zh=new Set(["length","size","percentage"]),Jh=e=>gt(e,Zh,Ui),Qh=e=>gt(e,"position",Ui),em=new Set(["image","url"]),tm=e=>gt(e,em,om),nm=e=>gt(e,"",sm),bt=()=>!0,gt=(e,t,n)=>{const r=$i.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},rm=e=>Hh.test(e)&&!Yh.test(e),Ui=()=>!1,sm=e=>Kh.test(e),om=e=>qh.test(e),im=()=>{const e=q("colors"),t=q("spacing"),n=q("blur"),r=q("brightness"),s=q("borderColor"),i=q("borderRadius"),o=q("borderSpacing"),a=q("borderWidth"),l=q("contrast"),c=q("grayscale"),u=q("hueRotate"),d=q("invert"),f=q("gap"),m=q("gradientColorStops"),g=q("gradientColorStopPositions"),p=q("inset"),y=q("margin"),v=q("opacity"),b=q("padding"),w=q("saturate"),C=q("scale"),P=q("sepia"),R=q("skew"),E=q("space"),j=q("translate"),O=()=>["auto","contain","none"],V=()=>["auto","hidden","clip","visible","scroll"],ae=()=>["auto",L,t],I=()=>[L,t],he=()=>["",Ae,Ie],X=()=>["auto",ft,L],Te=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Z=()=>["solid","dashed","dotted","double","none"],xe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Pe=()=>["start","end","center","between","around","evenly","stretch"],me=()=>["","0",L],He=()=>["auto","avoid","all","avoid-page","page","left","right","column"],z=()=>[ft,L];return{cacheSize:500,separator:":",theme:{colors:[bt],spacing:[Ae,Ie],blur:["none","",Fe,L],brightness:z(),borderColor:[e],borderRadius:["none","","full",Fe,L],borderSpacing:I(),borderWidth:he(),contrast:z(),grayscale:me(),hueRotate:z(),invert:me(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[Xh,Ie],inset:ae(),margin:ae(),opacity:z(),padding:I(),saturate:z(),scale:z(),sepia:me(),skew:z(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[Fe]}],"break-after":[{"break-after":He()}],"break-before":[{"break-before":He()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Te(),L]}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",vt,L]}],basis:[{basis:ae()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:me()}],shrink:[{shrink:me()}],order:[{order:["first","last","none",vt,L]}],"grid-cols":[{"grid-cols":[bt]}],"col-start-end":[{col:["auto",{span:["full",vt,L]},L]}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":[bt]}],"row-start-end":[{row:["auto",{span:[vt,L]},L]}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...Pe()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Pe(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Pe(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[Fe]},Fe]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Fe,Ie]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Dn]}],"font-family":[{font:[bt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",ft,Dn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ae,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ae,Ie]}],"underline-offset":[{"underline-offset":["auto",Ae,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Te(),Qh]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Jh]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},tm]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...Z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:Z()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...Z()]}],"outline-offset":[{"outline-offset":[Ae,L]}],"outline-w":[{outline:[Ae,Ie]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:he()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Ae,Ie]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Fe,nm]}],"shadow-color":[{shadow:[bt]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...xe(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":xe()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Fe,L]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:z()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:z()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[vt,L]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ae,Ie,Dn]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},am=Wh(im);var Gi={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(qs,function(){return function(n,r,s){n=n||{};var i=r.prototype,o={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function a(c,u,d,f){return i.fromToBase(c,u,d,f)}s.en.relativeTime=o,i.fromToBase=function(c,u,d,f,m){for(var g,p,y,v=d.$locale().relativeTime||o,b=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],w=b.length,C=0;C<w;C+=1){var P=b[C];P.d&&(g=f?s(c).diff(d,P.d,!0):d.diff(c,P.d,!0));var R=(n.rounding||Math.round)(Math.abs(g));if(y=g>0,R<=P.r||!P.r){R<=1&&C>0&&(P=b[C-1]);var E=v[P.l];m&&(R=m(""+R)),p=typeof E=="string"?E.replace("%d",R):E(R,u,P.l,y);break}}if(u)return p;var j=y?v.future:v.past;return typeof j=="function"?j(p):j.replace("%s",p)},i.to=function(c,u){return a(c,u,this,!0)},i.from=function(c,u){return a(c,u,this)};var l=function(c){return c.$u?s.utc():s()};i.toNow=function(c){return this.to(l(this),c)},i.fromNow=function(c){return this.from(l(this),c)}}})})(Gi);var lm=Gi.exports;const cm=Ks(lm);var um={exports:{}};(function(e,t){(function(n,r){e.exports=r(ha)})(qs,function(n){function r(o){return o&&typeof o=="object"&&"default"in o?o:{default:o}}var s=r(n),i={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(o,a){return a==="W"?o+"周":o+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(o,a){var l=100*o+a;return l<600?"凌晨":l<900?"早上":l<1100?"上午":l<1300?"中午":l<1800?"下午":"晚上"}};return s.default.locale(i,null,!0),i})})(um);Qt.extend(cm);Qt.locale("zh-cn");function D(...e){return am(Zi(e))}function pe(e){return e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString()}function $s(e){return Qt(e).format("YYYY-MM-DD HH:mm:ss")}function Hi(e){return Qt(e).fromNow()}function dm(e){switch(e){case"positive":return"text-green-400";case"negative":return"text-red-400";case"neutral":return"text-gray-400";default:return"text-gray-400"}}const fm=({className:e})=>{const{systemStatus:t,isConnected:n,selectedTimeRange:r,setSelectedTimeRange:s}=Ge(),[i,o]=ue.useState(new Date);ue.useEffect(()=>{const l=setInterval(()=>{o(new Date)},1e3);return()=>clearInterval(l)},[]);const a=[{value:"1h",label:"1小时"},{value:"6h",label:"6小时"},{value:"24h",label:"24小时"},{value:"7d",label:"7天"},{value:"30d",label:"30天"}];return h.jsxs("header",{className:D("glass-card border-b border-white/10 px-6 py-4 flex items-center justify-between",e),children:[h.jsxs("div",{className:"flex items-center space-x-6",children:[h.jsxs("div",{className:"flex items-center space-x-3",children:[h.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center",children:h.jsx(Rf,{className:"w-5 h-5 text-white"})}),h.jsxs("div",{children:[h.jsx("h1",{className:"text-xl font-bold text-white",children:"舆情监控大屏幕"}),h.jsx("p",{className:"text-sm text-gray-400",children:"实时数据分析与展示"})]})]}),h.jsx("div",{className:"flex items-center space-x-2",children:n?h.jsxs(h.Fragment,{children:[h.jsx(Kf,{className:"w-4 h-4 text-green-400"}),h.jsx("span",{className:"text-sm text-green-400",children:"已连接"}),h.jsx("div",{className:"status-indicator status-online"})]}):h.jsxs(h.Fragment,{children:[h.jsx(Yf,{className:"w-4 h-4 text-red-400"}),h.jsx("span",{className:"text-sm text-red-400",children:"连接断开"}),h.jsx("div",{className:"status-indicator status-offline"})]})}),h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("div",{className:D("w-2 h-2 rounded-full",t.dataSource.weibo?"bg-green-400":"bg-red-400")}),h.jsx("span",{className:"text-xs text-gray-400",children:"微博"})]}),h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("div",{className:D("w-2 h-2 rounded-full",t.dataSource.zhihu?"bg-green-400":"bg-red-400")}),h.jsx("span",{className:"text-xs text-gray-400",children:"知乎"})]}),h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("div",{className:D("w-2 h-2 rounded-full",t.dataSource.news?"bg-green-400":"bg-red-400")}),h.jsx("span",{className:"text-xs text-gray-400",children:"新闻"})]})]})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"text-sm text-gray-400",children:"时间范围:"}),h.jsx("div",{className:"flex bg-dark-800 rounded-lg p-1",children:a.map(l=>h.jsx("button",{onClick:()=>s(l.value),className:D("px-3 py-1 text-xs rounded-md transition-all duration-200",r===l.value?"bg-blue-500 text-white":"text-gray-400 hover:text-white hover:bg-dark-700"),children:l.label},l.value))})]}),h.jsxs("div",{className:"flex items-center space-x-6",children:[h.jsxs("div",{className:"flex items-center space-x-4 text-xs",children:[h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("span",{className:"text-gray-400",children:"CPU:"}),h.jsxs("span",{className:D("font-mono",t.performance.cpu>80?"text-red-400":t.performance.cpu>60?"text-yellow-400":"text-green-400"),children:[t.performance.cpu.toFixed(1),"%"]})]}),h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("span",{className:"text-gray-400",children:"内存:"}),h.jsxs("span",{className:D("font-mono",t.performance.memory>80?"text-red-400":t.performance.memory>60?"text-yellow-400":"text-green-400"),children:[t.performance.memory.toFixed(1),"%"]})]})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx(Df,{className:"w-4 h-4 text-gray-400"}),h.jsxs("div",{className:"text-right",children:[h.jsx("div",{className:"text-sm font-mono text-white",children:$s(i)}),h.jsxs("div",{className:"text-xs text-gray-400",children:["最后更新: ",$s(t.lastUpdate)]})]})]}),h.jsx("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:h.jsx(Uf,{className:"w-5 h-5 text-gray-400 hover:text-white"})})]})]})},hm=({className:e})=>{const{realTimeData:t}=Ge();if(!t)return h.jsx("aside",{className:D("glass-card border-r border-white/10 p-6",e),children:h.jsxs("div",{className:"animate-pulse space-y-4",children:[h.jsx("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),h.jsx("div",{className:"h-4 bg-gray-700 rounded w-1/2"}),h.jsx("div",{className:"h-4 bg-gray-700 rounded w-2/3"})]})});const{statistics:n,hotTopics:r,recentPosts:s}=t;return h.jsxs("aside",{className:D("glass-card border-r border-white/10 p-6 flex flex-col space-y-6 overflow-y-auto scrollbar-hide",e),children:[h.jsxs("div",{className:"space-y-4",children:[h.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center",children:[h.jsx(Sr,{className:"w-5 h-5 mr-2"}),"数据概览"]}),h.jsxs("div",{className:"grid grid-cols-1 gap-3",children:[h.jsxs("div",{className:"bg-dark-800/50 rounded-lg p-4",children:[h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsx("span",{className:"text-sm text-gray-400",children:"总数据量"}),h.jsx(Ni,{className:"w-4 h-4 text-blue-400"})]}),h.jsxs("div",{className:"mt-2",children:[h.jsx("div",{className:"text-2xl font-bold text-white",children:pe(n.total)}),h.jsxs("div",{className:D("text-xs flex items-center mt-1",n.growthRate>=0?"text-green-400":"text-red-400"),children:[n.growthRate>=0?"↗":"↘",Math.abs(n.growthRate).toFixed(1),"%"]})]})]}),h.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[h.jsxs("div",{className:"bg-green-500/20 rounded-lg p-3 text-center",children:[h.jsx("div",{className:"text-lg font-bold text-green-400",children:pe(n.positive)}),h.jsx("div",{className:"text-xs text-green-300",children:"正面"})]}),h.jsxs("div",{className:"bg-red-500/20 rounded-lg p-3 text-center",children:[h.jsx("div",{className:"text-lg font-bold text-red-400",children:pe(n.negative)}),h.jsx("div",{className:"text-xs text-red-300",children:"负面"})]}),h.jsxs("div",{className:"bg-gray-500/20 rounded-lg p-3 text-center",children:[h.jsx("div",{className:"text-lg font-bold text-gray-400",children:pe(n.neutral)}),h.jsx("div",{className:"text-xs text-gray-300",children:"中性"})]})]})]})]}),h.jsxs("div",{className:"space-y-4",children:[h.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center",children:[h.jsx(Of,{className:"w-5 h-5 mr-2"}),"热点话题"]}),h.jsx("div",{className:"space-y-2",children:r.slice(0,5).map((i,o)=>h.jsx("div",{className:"bg-dark-800/50 rounded-lg p-3 hover:bg-dark-700/50 transition-colors",children:h.jsx("div",{className:"flex items-start justify-between",children:h.jsxs("div",{className:"flex-1 min-w-0",children:[h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsxs("span",{className:"text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded",children:["#",o+1]}),h.jsx("span",{className:D("text-xs px-2 py-1 rounded",i.trend==="up"?"bg-green-500/20 text-green-400":i.trend==="down"?"bg-red-500/20 text-red-400":"bg-gray-500/20 text-gray-400"),children:i.trend==="up"?"↗":i.trend==="down"?"↘":"→"})]}),h.jsx("h3",{className:"text-sm font-medium text-white mt-1 truncate",children:i.title}),h.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[h.jsxs("span",{className:"text-xs text-gray-400",children:[pe(i.count)," 条"]}),h.jsx("span",{className:D("text-xs",dm(i.sentiment)),children:i.sentiment==="positive"?"正面":i.sentiment==="negative"?"负面":"中性"})]})]})})},i.id))})]}),h.jsxs("div",{className:"space-y-4 flex-1",children:[h.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center",children:[h.jsx(_f,{className:"w-5 h-5 mr-2"}),"最新动态"]}),h.jsx("div",{className:"space-y-3",children:s.slice(0,8).map(i=>h.jsx("div",{className:"bg-dark-800/30 rounded-lg p-3 hover:bg-dark-700/30 transition-colors",children:h.jsxs("div",{className:"flex items-start space-x-3",children:[h.jsx("div",{className:D("w-2 h-2 rounded-full mt-2 flex-shrink-0",i.sentiment==="positive"?"bg-green-400":i.sentiment==="negative"?"bg-red-400":"bg-gray-400")}),h.jsxs("div",{className:"flex-1 min-w-0",children:[h.jsx("p",{className:"text-sm text-gray-300 line-clamp-2",children:i.content}),h.jsxs("div",{className:"flex items-center justify-between mt-2",children:[h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"text-xs text-gray-500",children:i.source}),h.jsxs("span",{className:"text-xs text-gray-500",children:["@",i.author]})]}),h.jsx("span",{className:"text-xs text-gray-500",children:Hi(i.timestamp)})]})]})]})},i.id))})]}),h.jsxs("div",{className:"space-y-3 pt-4 border-t border-white/10",children:[h.jsxs("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors",children:[h.jsx(If,{className:"w-4 h-4"}),h.jsx("span",{children:"数据筛选"})]}),h.jsxs("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors",children:[h.jsx(Lf,{className:"w-4 h-4"}),h.jsx("span",{children:"详细视图"})]}),h.jsxs("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors",children:[h.jsx(Vi,{className:"w-4 h-4"}),h.jsx("span",{children:"预警设置"})]})]})]})},mm=({className:e})=>{const{systemStatus:t,realTimeData:n,isConnected:r,dashboardConfig:s}=Ge(),[i,o]=ue.useState(0);return ue.useEffect(()=>{if(!s.autoRefresh)return;const a=setInterval(()=>{o(l=>l<=0?Math.floor(s.refreshInterval/1e3):l-1)},1e3);return()=>clearInterval(a)},[s.autoRefresh,s.refreshInterval]),ue.useEffect(()=>{s.autoRefresh&&o(Math.floor(s.refreshInterval/1e3))},[s.autoRefresh,s.refreshInterval]),h.jsxs("footer",{className:D("glass-card border-t border-white/10 px-6 py-3 flex items-center justify-between text-sm",e),children:[h.jsxs("div",{className:"flex items-center space-x-6",children:[h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx($f,{className:D("w-4 h-4",t.isOnline?"text-green-400":"text-red-400")}),h.jsxs("span",{className:D("text-xs",t.isOnline?"text-green-400":"text-red-400"),children:["服务器 ",t.isOnline?"在线":"离线"]})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx(Nf,{className:"w-4 h-4 text-blue-400"}),h.jsx("span",{className:"text-xs text-gray-400",children:"数据库连接正常"})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx(Ff,{className:D("w-4 h-4",r?"text-green-400":"text-red-400")}),h.jsxs("span",{className:D("text-xs",r?"text-green-400":"text-red-400"),children:["网络 ",r?"已连接":"断开"]})]}),h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx(Zf,{className:"w-3 h-3 text-yellow-400"}),h.jsxs("span",{className:"text-xs text-gray-400",children:["延迟: ",h.jsx("span",{className:"text-white font-mono",children:"12ms"})]})]}),h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx(Hf,{className:"w-3 h-3 text-purple-400"}),h.jsxs("span",{className:"text-xs text-gray-400",children:["在线用户: ",h.jsx("span",{className:"text-white font-mono",children:"1"})]})]})]})]}),h.jsx("div",{className:"flex items-center space-x-6",children:n&&h.jsxs(h.Fragment,{children:[h.jsxs("div",{className:"text-xs text-gray-400",children:["总数据: ",h.jsx("span",{className:"text-white font-mono",children:pe(n.statistics.total)})]}),h.jsxs("div",{className:"text-xs text-gray-400",children:["热点话题: ",h.jsx("span",{className:"text-white font-mono",children:n.hotTopics.length})]}),h.jsxs("div",{className:"text-xs text-gray-400",children:["关键词: ",h.jsx("span",{className:"text-white font-mono",children:n.keywords.length})]})]})}),h.jsxs("div",{className:"flex items-center space-x-6",children:[s.autoRefresh&&h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx(Di,{className:D("w-3 h-3",i<=5?"text-yellow-400 animate-spin":"text-gray-400")}),h.jsxs("span",{className:"text-xs text-gray-400",children:["自动刷新: ",h.jsxs("span",{className:"text-white font-mono",children:[i,"s"]})]})]}),h.jsxs("div",{className:"text-xs text-gray-400",children:["更新时间: ",h.jsx("span",{className:"text-white",children:Hi(t.lastUpdate)})]}),h.jsx("div",{className:"text-xs text-gray-500",children:"v1.0.0"}),h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx("div",{className:D("w-2 h-2 rounded-full",t.isOnline&&r?"bg-green-400 animate-pulse":"bg-red-400")}),h.jsx("span",{className:"text-xs text-gray-400",children:t.isOnline&&r?"运行中":"异常"})]})]})]})},pm=({children:e,className:t})=>h.jsxs("div",{className:"h-screen flex flex-col bg-dark-900 overflow-hidden",children:[h.jsxs("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[h.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"}),h.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"}),h.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-500/5 rounded-full blur-3xl"})]}),h.jsx(te.div,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5},children:h.jsx(fm,{className:"flex-shrink-0 z-10 relative"})}),h.jsxs("div",{className:"flex flex-1 overflow-hidden relative",children:[h.jsx(te.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{duration:.5,delay:.1},className:"flex-shrink-0 w-80",children:h.jsx(hm,{className:"h-full"})}),h.jsx(te.main,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,delay:.2},className:D("flex-1 overflow-hidden relative",t),children:h.jsx("div",{className:"h-full p-6 overflow-y-auto scrollbar-hide",children:e})})]}),h.jsx(te.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.3},children:h.jsx(mm,{className:"flex-shrink-0 z-10 relative"})})]}),gm=({data:e,title:t="时间序列分析",height:n=400,className:r,showLegend:s=!0,showToolbox:i=!0})=>{const o=ue.useMemo(()=>{const a=e.map(f=>f.timestamp),l=e.map(f=>f.value),c=e.map(f=>f.positive||0),u=e.map(f=>f.negative||0),d=e.map(f=>f.neutral||0);return{title:{text:t,left:"center",textStyle:{color:"#ffffff",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#ffffff"},formatter:f=>{let m=`<div style="margin-bottom: 8px; font-weight: bold;">${f[0].axisValue}</div>`;return f.forEach(g=>{const p=g.color,y=g.value;m+=`
              <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${p}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="margin-right: 8px;">${g.seriesName}:</span>
                <span style="font-weight: bold;">${y}</span>
              </div>
            `}),m}},legend:s?{data:["总量","正面","负面","中性"],top:30,textStyle:{color:"#ffffff"}}:void 0,grid:{left:"3%",right:"4%",bottom:"3%",top:s?"15%":"10%",containLabel:!0},toolbox:i?{feature:{saveAsImage:{backgroundColor:"#1e293b"},dataZoom:{yAxisIndex:"none"},restore:{},magicType:{type:["line","bar"]}},iconStyle:{borderColor:"#ffffff"},emphasis:{iconStyle:{borderColor:"#3b82f6"}}}:void 0,xAxis:{type:"category",boundaryGap:!1,data:a,axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.3)"}},axisLabel:{color:"#ffffff",formatter:f=>{const m=new Date(f);return`${m.getMonth()+1}/${m.getDate()} ${m.getHours()}:${m.getMinutes().toString().padStart(2,"0")}`}},splitLine:{show:!0,lineStyle:{color:"rgba(255, 255, 255, 0.1)",type:"dashed"}}},yAxis:{type:"value",axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.3)"}},axisLabel:{color:"#ffffff"},splitLine:{lineStyle:{color:"rgba(255, 255, 255, 0.1)",type:"dashed"}}},series:[{name:"总量",type:"line",data:l,smooth:!0,lineStyle:{color:"#3b82f6",width:3},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(59, 130, 246, 0.3)"},{offset:1,color:"rgba(59, 130, 246, 0.05)"}]}},emphasis:{focus:"series"}},{name:"正面",type:"line",data:c,smooth:!0,lineStyle:{color:"#10b981",width:2},emphasis:{focus:"series"}},{name:"负面",type:"line",data:u,smooth:!0,lineStyle:{color:"#ef4444",width:2},emphasis:{focus:"series"}},{name:"中性",type:"line",data:d,smooth:!0,lineStyle:{color:"#6b7280",width:2},emphasis:{focus:"series"}}],animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}},[e,t,s,i]);return h.jsx(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:D("chart-container",r),children:h.jsx(Rt,{option:o,style:{height:`${n}px`,width:"100%"},opts:{renderer:"canvas"},notMerge:!0,lazyUpdate:!0})})},ym=({data:e,title:t="情感分析分布",height:n=400,className:r,showPercentage:s=!0})=>{const i=ue.useMemo(()=>{const o=e.total,a=[{value:e.positive,name:"正面",itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:1,colorStops:[{offset:0,color:"#10b981"},{offset:1,color:"#059669"}]}}},{value:e.negative,name:"负面",itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:1,colorStops:[{offset:0,color:"#ef4444"},{offset:1,color:"#dc2626"}]}}},{value:e.neutral,name:"中性",itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:1,colorStops:[{offset:0,color:"#6b7280"},{offset:1,color:"#4b5563"}]}}}];return{title:{text:t,left:"center",top:20,textStyle:{color:"#ffffff",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#ffffff"},formatter:l=>{const c=(l.value/o*100).toFixed(1);return`
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <span style="display: inline-block; width: 12px; height: 12px; background-color: ${l.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="font-weight: bold; margin-right: 8px;">${l.name}</span>
            </div>
            <div style="margin-left: 20px;">
              <div>数量: <span style="font-weight: bold;">${pe(l.value)}</span></div>
              <div>占比: <span style="font-weight: bold;">${c}%</span></div>
            </div>
          `}},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{color:"#ffffff"},formatter:l=>{const c=a.find(d=>d.name===l);if(!c)return l;const u=(c.value/o*100).toFixed(1);return`${l} ${s?`(${u}%)`:""}`}},series:[{name:"情感分析",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:8,borderColor:"rgba(0, 0, 0, 0.2)",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:20,fontWeight:"bold",color:"#ffffff",formatter:l=>{const c=(l.value/o*100).toFixed(1);return`${l.name}
${pe(l.value)}
${c}%`}},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},labelLine:{show:!1},data:a,animationType:"scale",animationEasing:"elasticOut",animationDelay:l=>Math.random()*200}],graphic:[{type:"text",left:"center",top:"middle",style:{text:`总计
${pe(o)}`,textAlign:"center",fill:"#ffffff",fontSize:16,fontWeight:"bold"}}]}},[e,t,s]);return h.jsx(te.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:D("chart-container",r),children:h.jsx(Rt,{option:i,style:{height:`${n}px`,width:"100%"},opts:{renderer:"canvas"},notMerge:!0,lazyUpdate:!0})})};Xs([Ji,Qi]);Xs(ea);ta({type:"series.wordCloud",visualStyleAccessPath:"textStyle",visualStyleMapper:function(e){return{fill:e.get("color")}},visualDrawType:"fill",optionUpdated:function(){var e=this.option;e.gridSize=Math.max(Math.floor(e.gridSize),4)},getInitialData:function(e,t){var n=na(e.data,{coordDimensions:["value"]}),r=new ra(n,this);return r.initData(e.data),r},defaultOption:{maskImage:null,shape:"circle",keepAspect:!1,left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,textStyle:{fontWeight:"normal"}}});sa({type:"wordCloud",render:function(e,t,n){var r=this.group;r.removeAll();var s=e.getData(),i=e.get("gridSize");e.layoutInstance.ondraw=function(o,a,l,c){var u=s.getItemModel(l),d=u.getModel("textStyle"),f=new oa({style:xn(d),scaleX:1/c.info.mu,scaleY:1/c.info.mu,x:(c.gx+c.info.gw/2)*i,y:(c.gy+c.info.gh/2)*i,rotation:c.rot});f.setStyle({x:c.info.fillTextOffsetX,y:c.info.fillTextOffsetY+a*.5,text:o,verticalAlign:"middle",fill:s.getItemVisual(l,"style").fill,fontSize:a}),r.add(f),s.setItemGraphicEl(l,f),f.ensureState("emphasis").style=xn(u.getModel(["emphasis","textStyle"]),{state:"emphasis"}),f.ensureState("blur").style=xn(u.getModel(["blur","textStyle"]),{state:"blur"}),ia(f,u.get(["emphasis","focus"]),u.get(["emphasis","blurScope"])),f.stateTransition={duration:e.get("animation")?e.get(["stateAnimation","duration"]):0,easing:e.get(["stateAnimation","easing"])},f.__highDownDispatcher=!0},this._model=e},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}});/*!
 * wordcloud2.js
 * http://timdream.org/wordcloud2.js/
 *
 * Copyright 2011 - 2019 Tim Guan-tin Chien and contributors.
 * Released under the MIT license
 */window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var n=[void 0],r="zero-timeout-message",s=function(o){var a=n.length;return n.push(o),window.postMessage(r+a.toString(36),"*"),a};return window.addEventListener("message",function(o){if(!(typeof o.data!="string"||o.data.substr(0,r.length)!==r)){o.stopImmediatePropagation();var a=parseInt(o.data.substr(r.length),36);n[a]&&(n[a](),n[a]=void 0)}},!0),window.clearImmediate=function(o){n[o]&&(n[o]=void 0)},s}()||function(n){window.setTimeout(n,0)}}());window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(n){window.clearTimeout(n)}}());var Tr=function(){var t=document.createElement("canvas");if(!t||!t.getContext)return!1;var n=t.getContext("2d");return!(!n||!n.getImageData||!n.fillText||!Array.prototype.some||!Array.prototype.push)}(),Jn=function(){if(Tr){for(var t=document.createElement("canvas").getContext("2d"),n=20,r,s;n;){if(t.font=n.toString(10)+"px sans-serif",t.measureText("Ｗ").width===r&&t.measureText("m").width===s)return n+1;r=t.measureText("Ｗ").width,s=t.measureText("m").width,n--}return 0}}(),xm=function(e){if(Array.isArray(e)){var t=e.slice();return t.splice(0,2),t}else return[]},vm=function(t){for(var n,r,s=t.length;s;)n=Math.floor(Math.random()*s),r=t[--s],t[s]=t[n],t[n]=r;return t},ot={},mn=function(t,n){if(!Tr)return;var r=Math.floor(Math.random()*Date.now());Array.isArray(t)||(t=[t]),t.forEach(function(B,S){if(typeof B=="string"){if(t[S]=document.getElementById(B),!t[S])throw new Error("The element id specified is not found.")}else if(!B.tagName&&!B.appendChild)throw new Error("You must pass valid HTML elements, or ID of the element.")});var s={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,layoutAnimation:!0,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(n)for(var i in n)i in s&&(s[i]=n[i]);if(typeof s.weightFactor!="function"){var o=s.weightFactor;s.weightFactor=function(S){return S*o}}if(typeof s.shape!="function")switch(s.shape){case"circle":default:s.shape="circle";break;case"cardioid":s.shape=function(S){return 1-Math.sin(S)};break;case"diamond":s.shape=function(S){var T=S%(2*Math.PI/4);return 1/(Math.cos(T)+Math.sin(T))};break;case"square":s.shape=function(S){return Math.min(1/Math.abs(Math.cos(S)),1/Math.abs(Math.sin(S)))};break;case"triangle-forward":s.shape=function(S){var T=S%(2*Math.PI/3);return 1/(Math.cos(T)+Math.sqrt(3)*Math.sin(T))};break;case"triangle":case"triangle-upright":s.shape=function(S){var T=(S+Math.PI*3/2)%(2*Math.PI/3);return 1/(Math.cos(T)+Math.sqrt(3)*Math.sin(T))};break;case"pentagon":s.shape=function(S){var T=(S+.955)%(2*Math.PI/5);return 1/(Math.cos(T)+.726543*Math.sin(T))};break;case"star":s.shape=function(S){var T=(S+.955)%(2*Math.PI/10);return(S+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-T)+3.07768*Math.sin(2*Math.PI/10-T)):1/(Math.cos(T)+3.07768*Math.sin(T))};break}s.gridSize=Math.max(Math.floor(s.gridSize),4);var a=s.gridSize,l=a-s.maskGapWidth,c=Math.abs(s.maxRotation-s.minRotation),u=Math.min(s.maxRotation,s.minRotation),d=s.rotationStep,f,m,g,p,y,v,b;function w(B,S){return"hsl("+(Math.random()*360).toFixed()+","+(Math.random()*30+70).toFixed()+"%,"+(Math.random()*(S-B)+B).toFixed()+"%)"}switch(s.color){case"random-dark":b=function(){return w(10,50)};break;case"random-light":b=function(){return w(50,90)};break;default:typeof s.color=="function"&&(b=s.color);break}var C;typeof s.fontWeight=="function"&&(C=s.fontWeight);var P=null;typeof s.classes=="function"&&(P=s.classes);var R=!1,E=[],j,O=function(S){var T=S.currentTarget,k=T.getBoundingClientRect(),N,M;S.touches?(N=S.touches[0].clientX,M=S.touches[0].clientY):(N=S.clientX,M=S.clientY);var F=N-k.left,U=M-k.top,_=Math.floor(F*(T.width/k.width||1)/a),$=Math.floor(U*(T.height/k.height||1)/a);return E[_]?E[_][$]:null},V=function(S){var T=O(S);if(j!==T){if(j=T,!T){s.hover(void 0,void 0,S);return}s.hover(T.item,T.dimension,S)}},ae=function(S){var T=O(S);T&&(s.click(T.item,T.dimension,S),S.preventDefault())},I=[],he=function(S){if(I[S])return I[S];var T=S*8,k=T,N=[];for(S===0&&N.push([p[0],p[1],0]);k--;){var M=1;s.shape!=="circle"&&(M=s.shape(k/T*2*Math.PI)),N.push([p[0]+S*M*Math.cos(-k/T*2*Math.PI),p[1]+S*M*Math.sin(-k/T*2*Math.PI)*s.ellipticity,k/T*2*Math.PI])}return I[S]=N,N},X=function(){return s.abortThreshold>0&&new Date().getTime()-v>s.abortThreshold},Te=function(){return s.rotateRatio===0||Math.random()>s.rotateRatio?0:c===0?u:u+Math.round(Math.random()*c/d)*d},Z=function(S,T,k,N){var M=s.weightFactor(T);if(M<=s.minSize)return!1;var F=1;M<Jn&&(F=function(){for(var yn=2;yn*M<Jn;)yn+=2;return yn}());var U;C?U=C(S,T,M,N):U=s.fontWeight;var _=document.createElement("canvas"),$=_.getContext("2d",{willReadFrequently:!0});$.font=U+" "+(M*F).toString(10)+"px "+s.fontFamily;var ie=$.measureText(S).width/F,G=Math.max(M*F,$.measureText("m").width,$.measureText("Ｗ").width)/F,H=ie+G*2,re=G*3,de=Math.ceil(H/a),ve=Math.ceil(re/a);H=de*a,re=ve*a;var ee=-ie/2,W=-G*.4,J=Math.ceil((H*Math.abs(Math.sin(k))+re*Math.abs(Math.cos(k)))/a),se=Math.ceil((H*Math.abs(Math.cos(k))+re*Math.abs(Math.sin(k)))/a),we=se*a,rt=J*a;_.setAttribute("width",we),_.setAttribute("height",rt),$.scale(1/F,1/F),$.translate(we*F/2,rt*F/2),$.rotate(-k),$.font=U+" "+(M*F).toString(10)+"px "+s.fontFamily,$.fillStyle="#000",$.textBaseline="middle",$.fillText(S,ee*F,(W+M*.5)*F);var Lt=$.getImageData(0,0,we,rt).data;if(X())return!1;for(var Pr=[],Ye=se,Le,pn,gn,Re=[J/2,se/2,J/2,se/2];Ye--;)for(Le=J;Le--;){gn=a;e:for(;gn--;)for(pn=a;pn--;)if(Lt[((Le*a+gn)*we+(Ye*a+pn))*4+3]){Pr.push([Ye,Le]),Ye<Re[3]&&(Re[3]=Ye),Ye>Re[1]&&(Re[1]=Ye),Le<Re[0]&&(Re[0]=Le),Le>Re[2]&&(Re[2]=Le);break e}}return{mu:F,occupied:Pr,bounds:Re,gw:se,gh:J,fillTextOffsetX:ee,fillTextOffsetY:W,fillTextWidth:ie,fillTextHeight:G,fontSize:M}},xe=function(S,T,k,N,M){for(var F=M.length;F--;){var U=S+M[F][0],_=T+M[F][1];if(U>=m||_>=g||U<0||_<0){if(!s.drawOutOfBound)return!1;continue}if(!f[U][_])return!1}return!0},Pe=function(S,T,k,N,M,F,U,_,$,ie){var G=k.fontSize,H;b?H=b(N,M,G,F,U,ie):H=s.color;var re;C?re=C(N,M,G,ie):re=s.fontWeight;var de;P?de=P(N,M,G,ie):de=s.classes,t.forEach(function(ve){if(ve.getContext){var ee=ve.getContext("2d"),W=k.mu;ee.save(),ee.scale(1/W,1/W),ee.font=re+" "+(G*W).toString(10)+"px "+s.fontFamily,ee.fillStyle=H,ee.translate((S+k.gw/2)*a*W,(T+k.gh/2)*a*W),_!==0&&ee.rotate(-_),ee.textBaseline="middle",ee.fillText(N,k.fillTextOffsetX*W,(k.fillTextOffsetY+G*.5)*W),ee.restore()}else{var J=document.createElement("span"),se="";se="rotate("+-_/Math.PI*180+"deg) ",k.mu!==1&&(se+="translateX(-"+k.fillTextWidth/4+"px) scale("+1/k.mu+")");var we={position:"absolute",display:"block",font:re+" "+G*k.mu+"px "+s.fontFamily,left:(S+k.gw/2)*a+k.fillTextOffsetX+"px",top:(T+k.gh/2)*a+k.fillTextOffsetY+"px",width:k.fillTextWidth+"px",height:k.fillTextHeight+"px",lineHeight:G+"px",whiteSpace:"nowrap",transform:se,webkitTransform:se,msTransform:se,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};H&&(we.color=H),J.textContent=N;for(var rt in we)J.style[rt]=we[rt];if($)for(var Lt in $)J.setAttribute(Lt,$[Lt]);de&&(J.className+=de),ve.appendChild(J)}})},me=function(S,T,k,N,M){if(!(S>=m||T>=g||S<0||T<0)){if(f[S][T]=!1,k){var F=t[0].getContext("2d");F.fillRect(S*a,T*a,l,l)}R&&(E[S][T]={item:M,dimension:N})}},He=function(S,T,k,N,M,F){var U=M.occupied,_=s.drawMask,$;_&&($=t[0].getContext("2d"),$.save(),$.fillStyle=s.maskColor);var ie;if(R){var G=M.bounds;ie={x:(S+G[3])*a,y:(T+G[0])*a,w:(G[1]-G[3]+1)*a,h:(G[2]-G[0]+1)*a}}for(var H=U.length;H--;){var re=S+U[H][0],de=T+U[H][1];re>=m||de>=g||re<0||de<0||me(re,de,_,ie,F)}_&&$.restore()},z=function B(S,T){if(T>20)return null;var k,N,M;Array.isArray(S)?(k=S[0],N=S[1]):(k=S.word,N=S.weight,M=S.attributes);var F=Te(),U=xm(S),_=Z(k,N,F,U);if(!_||X())return!1;if(!s.drawOutOfBound&&!s.shrinkToFit){var $=_.bounds;if($[1]-$[3]+1>m||$[2]-$[0]+1>g)return!1}for(var ie=y+1,G=function(ve){var ee=Math.floor(ve[0]-_.gw/2),W=Math.floor(ve[1]-_.gh/2),J=_.gw,se=_.gh;return xe(ee,W,J,se,_.occupied)?(Pe(ee,W,_,k,N,y-ie,ve[2],F,M,U),He(ee,W,J,se,_,S),{gx:ee,gy:W,rot:F,info:_}):!1};ie--;){var H=he(y-ie);s.shuffle&&(H=[].concat(H),vm(H));for(var re=0;re<H.length;re++){var de=G(H[re]);if(de)return de}}return s.shrinkToFit?(Array.isArray(S)?S[1]=S[1]*3/4:S.weight=S.weight*3/4,B(S,T+1)):null},ke=function(S,T,k){if(T)return!t.some(function(N){var M=new CustomEvent(S,{detail:k||{}});return!N.dispatchEvent(M)},this);t.forEach(function(N){var M=new CustomEvent(S,{detail:k||{}});N.dispatchEvent(M)},this)},Nt=function(){var S=t[0];if(S.getContext)m=Math.ceil(S.width/a),g=Math.ceil(S.height/a);else{var T=S.getBoundingClientRect();m=Math.ceil(T.width/a),g=Math.ceil(T.height/a)}if(ke("wordcloudstart",!0)){p=s.origin?[s.origin[0]/a,s.origin[1]/a]:[m/2,g/2],y=Math.floor(Math.sqrt(m*m+g*g)),f=[];var k,N,M;if(!S.getContext||s.clearCanvas)for(t.forEach(function(W){if(W.getContext){var J=W.getContext("2d");J.fillStyle=s.backgroundColor,J.clearRect(0,0,m*(a+1),g*(a+1)),J.fillRect(0,0,m*(a+1),g*(a+1))}else W.textContent="",W.style.backgroundColor=s.backgroundColor,W.style.position="relative"}),k=m;k--;)for(f[k]=[],N=g;N--;)f[k][N]=!0;else{var F=document.createElement("canvas").getContext("2d");F.fillStyle=s.backgroundColor,F.fillRect(0,0,1,1);var U=F.getImageData(0,0,1,1).data,_=S.getContext("2d").getImageData(0,0,m*a,g*a).data;k=m;for(var $,ie;k--;)for(f[k]=[],N=g;N--;){ie=a;e:for(;ie--;)for($=a;$--;)for(M=4;M--;)if(_[((N*a+ie)*m*a+(k*a+$))*4+M]!==U[M]){f[k][N]=!1;break e}f[k][N]!==!1&&(f[k][N]=!0)}_=F=U=void 0}if(s.hover||s.click){for(R=!0,k=m+1;k--;)E[k]=[];s.hover&&S.addEventListener("mousemove",V),s.click&&(S.addEventListener("click",ae),S.addEventListener("touchstart",ae),S.addEventListener("touchend",function(W){W.preventDefault()}),S.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),S.addEventListener("wordcloudstart",function W(){S.removeEventListener("wordcloudstart",W),S.removeEventListener("mousemove",V),S.removeEventListener("click",ae),j=void 0})}M=0;var G,H,re=!0;s.layoutAnimation?s.wait!==0?(G=window.setTimeout,H=window.clearTimeout):(G=window.setImmediate,H=window.clearImmediate):(G=function(W){W()},H=function(){re=!1});var de=function(J,se){t.forEach(function(we){we.addEventListener(J,se)},this)},ve=function(J,se){t.forEach(function(we){we.removeEventListener(J,se)},this)},ee=function W(){ve("wordcloudstart",W),H(ot[r])};de("wordcloudstart",ee),ot[r]=(s.layoutAnimation?G:setTimeout)(function W(){if(re){if(M>=s.list.length){H(ot[r]),ke("wordcloudstop",!1),ve("wordcloudstart",ee),delete ot[r];return}v=new Date().getTime();var J=z(s.list[M],0),se=!ke("wordclouddrawn",!0,{item:s.list[M],drawn:J});if(X()||se){H(ot[r]),s.abort(),ke("wordcloudabort",!1),ke("wordcloudstop",!1),ve("wordcloudstart",ee);return}M++,ot[r]=G(W,s.wait)}},s.wait)}};Nt()};mn.isSupported=Tr;mn.minFontSize=Jn;if(!mn.isSupported)throw new Error("Sorry your browser not support wordCloud");function bm(e){for(var t=e.getContext("2d"),n=t.getImageData(0,0,e.width,e.height),r=t.createImageData(n),s=0,i=0,o=0;o<n.data.length;o+=4){var a=n.data[o+3];if(a>128){var l=n.data[o]+n.data[o+1]+n.data[o+2];s+=l,++i}}for(var c=s/i,o=0;o<n.data.length;o+=4){var l=n.data[o]+n.data[o+1]+n.data[o+2],a=n.data[o+3];a<128||l>c?(r.data[o]=0,r.data[o+1]=0,r.data[o+2]=0,r.data[o+3]=0):(r.data[o]=255,r.data[o+1]=255,r.data[o+2]=255,r.data[o+3]=255)}t.putImageData(r,0,0)}aa(function(e,t){e.eachSeriesByType("wordCloud",function(n){var r=da(n.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),s=n.get("keepAspect"),i=n.get("maskImage"),o=i?i.width/i.height:1;s&&wm(r,o);var a=n.getData(),l=document.createElement("canvas");l.width=r.width,l.height=r.height;var c=l.getContext("2d");if(i)try{c.drawImage(i,0,0,l.width,l.height),bm(l)}catch(y){console.error("Invalid mask image"),console.error(y.toString())}var u=n.get("sizeRange"),d=n.get("rotationRange"),f=a.getDataExtent("value"),m=Math.PI/180,g=n.get("gridSize");mn(l,{list:a.mapArray("value",function(y,v){var b=a.getItemModel(v);return[a.getName(v),b.get("textStyle.fontSize",!0)||fa(y,f,u),v]}).sort(function(y,v){return v[1]-y[1]}),fontFamily:n.get("textStyle.fontFamily")||n.get("emphasis.textStyle.fontFamily")||e.get("textStyle.fontFamily"),fontWeight:n.get("textStyle.fontWeight")||n.get("emphasis.textStyle.fontWeight")||e.get("textStyle.fontWeight"),gridSize:g,ellipticity:r.height/r.width,minRotation:d[0]*m,maxRotation:d[1]*m,clearCanvas:!i,rotateRatio:1,rotationStep:n.get("rotationStep")*m,drawOutOfBound:n.get("drawOutOfBound"),shrinkToFit:n.get("shrinkToFit"),layoutAnimation:n.get("layoutAnimation"),shuffle:!1,shape:n.get("shape")});function p(y){var v=y.detail.item;y.detail.drawn&&n.layoutInstance.ondraw&&(y.detail.drawn.gx+=r.x/g,y.detail.drawn.gy+=r.y/g,n.layoutInstance.ondraw(v[0],v[1],v[2],y.detail.drawn))}l.addEventListener("wordclouddrawn",p),n.layoutInstance&&n.layoutInstance.dispose(),n.layoutInstance={ondraw:null,dispose:function(){l.removeEventListener("wordclouddrawn",p),l.addEventListener("wordclouddrawn",function(y){y.preventDefault()})}}})});la(function(e){var t=(e||{}).series;!ca(t)&&(t=t?[t]:[]);var n=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];kr(t,function(s){if(s&&s.type==="wordCloud"){var i=s.textStyle||{};r(i.normal),r(i.emphasis)}});function r(s){s&&kr(n,function(i){s.hasOwnProperty(i)&&(s["text"+ua(i)]=s[i])})}});function wm(e,t){var n=e.width,r=e.height;n>r*t?(e.x+=(n-r*t)/2,e.width=r*t):(e.y+=(r-n/t)/2,e.height=n/t)}const Sm=({data:e,title:t="关键词词云",height:n=400,className:r,maxWords:s=100})=>{const i=ue.useMemo(()=>{const a=e.slice(0,s).map(l=>({name:l.name,value:l.value,textStyle:{color:o(l.sentiment)}}));return{title:{text:t,left:"center",top:20,textStyle:{color:"#ffffff",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#ffffff"},formatter:l=>{var d;const c=((d=e.find(f=>f.name===l.name))==null?void 0:d.sentiment)||"neutral",u=c==="positive"?"正面":c==="negative"?"负面":"中性";return`
            <div style="font-weight: bold; margin-bottom: 4px;">${l.name}</div>
            <div>权重: <span style="font-weight: bold;">${l.value}</span></div>
            <div>情感: <span style="color: ${o(c)}; font-weight: bold;">${u}</span></div>
          `}},series:[{type:"wordCloud",gridSize:8,sizeRange:[12,60],rotationRange:[-45,45],rotationStep:15,shape:"pentagon",width:"90%",height:"80%",left:"center",top:"center",drawOutOfBound:!1,layoutAnimation:!0,textStyle:{fontFamily:"Inter, sans-serif",fontWeight:"bold",emphasis:{shadowBlur:10,shadowColor:"#333"}},emphasis:{focus:"self",textStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:a}],animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}},[e,t,s]);function o(a){switch(a){case"positive":return"#10b981";case"negative":return"#ef4444";case"neutral":return"#6b7280";default:return"#6b7280"}}return h.jsx(te.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.2},className:D("chart-container",r),children:h.jsx(Rt,{option:i,style:{height:`${n}px`,width:"100%"},opts:{renderer:"canvas"},notMerge:!0,lazyUpdate:!0})})},Cm=({data:e,title:t="热点话题排行",height:n=400,className:r,maxTopics:s=10,orientation:i="horizontal"})=>{const o=ue.useMemo(()=>{const u=e.slice(0,s).sort((p,y)=>y.count-p.count),d=u.map(p=>p.title),f=u.map(p=>p.count),m=u.map(p=>a(p.sentiment)),g=i==="horizontal";return{title:{text:t,left:"center",top:20,textStyle:{color:"#ffffff",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#ffffff"},formatter:p=>{const y=p[0],v=u[y.dataIndex],b=v.trend==="up"?"↗":v.trend==="down"?"↘":"→",w=v.sentiment==="positive"?"正面":v.sentiment==="negative"?"负面":"中性";return`
            <div style="font-weight: bold; margin-bottom: 8px; max-width: 200px; word-wrap: break-word;">
              ${v.title}
            </div>
            <div style="margin-bottom: 4px;">
              热度: <span style="font-weight: bold;">${pe(y.value)}</span>
            </div>
            <div style="margin-bottom: 4px;">
              趋势: <span style="color: ${l(v.trend)};">${b} ${v.trend==="up"?"上升":v.trend==="down"?"下降":"稳定"}</span>
            </div>
            <div>
              情感: <span style="color: ${a(v.sentiment)}; font-weight: bold;">${w}</span>
            </div>
          `}},grid:{left:g?"15%":"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:g?"value":"category",data:g?void 0:d,axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.3)"}},axisLabel:{color:"#ffffff",interval:0,rotate:g?0:45,formatter:p=>g?pe(parseInt(p)):p.length>8?p.substring(0,8)+"...":p},splitLine:{show:g,lineStyle:{color:"rgba(255, 255, 255, 0.1)",type:"dashed"}}},yAxis:{type:g?"category":"value",data:g?d:void 0,axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.3)"}},axisLabel:{color:"#ffffff",formatter:p=>typeof p=="string"?p.length>12?p.substring(0,12)+"...":p:pe(p)},splitLine:{show:!g,lineStyle:{color:"rgba(255, 255, 255, 0.1)",type:"dashed"}}},series:[{name:"热度",type:"bar",data:f.map((p,y)=>({value:p,itemStyle:{color:{type:"linear",x:0,y:0,x2:g?1:0,y2:g?0:1,colorStops:[{offset:0,color:m[y]},{offset:1,color:c(m[y],-20)}]},borderRadius:g?[0,4,4,0]:[4,4,0,0]}})),barWidth:"60%",emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}},animationDelay:p=>p*100}],animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}},[e,t,s,i]);function a(u){switch(u){case"positive":return"#10b981";case"negative":return"#ef4444";case"neutral":return"#6b7280";default:return"#6b7280"}}function l(u){switch(u){case"up":return"#10b981";case"down":return"#ef4444";case"stable":return"#6b7280";default:return"#6b7280"}}function c(u,d){const f=u[0]==="#",m=f?u.slice(1):u,g=parseInt(m,16);let p=(g>>16)+d,y=(g>>8&255)+d,v=(g&255)+d;return p=p>255?255:p<0?0:p,y=y>255?255:y<0?0:y,v=v>255?255:v<0?0:v,(f?"#":"")+(p<<16|y<<8|v).toString(16).padStart(6,"0")}return h.jsx(te.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.1},className:D("chart-container",r),children:h.jsx(Rt,{option:o,style:{height:`${n}px`,width:"100%"},opts:{renderer:"canvas"},notMerge:!0,lazyUpdate:!0})})},Tm=({data:e,title:t="地理位置分布",height:n=400,className:r})=>{const s=ue.useMemo(()=>{const o=e.map(l=>({name:l.name,value:[...l.coordinates,l.value],sentiment:l.sentiment})),a=Math.max(...e.map(l=>l.value));return{title:{text:t,left:"center",top:20,textStyle:{color:"#ffffff",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#ffffff"},formatter:l=>{const c=e.find(f=>f.name===l.name);if(!c)return"";const u=c.sentiment==="positive"?"正面":c.sentiment==="negative"?"负面":"中性",d=i(c.sentiment);return`
            <div style="font-weight: bold; margin-bottom: 8px;">${l.name}</div>
            <div style="margin-bottom: 4px;">
              数量: <span style="font-weight: bold;">${pe(l.value[2])}</span>
            </div>
            <div style="margin-bottom: 4px;">
              坐标: <span style="font-family: monospace;">${l.value[0].toFixed(2)}, ${l.value[1].toFixed(2)}</span>
            </div>
            <div>
              情感倾向: <span style="color: ${d}; font-weight: bold;">${u}</span>
            </div>
          `}},geo:{map:"world",roam:!0,zoom:1.2,center:[104.114129,37.550339],itemStyle:{areaColor:"rgba(30, 41, 59, 0.8)",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1},emphasis:{itemStyle:{areaColor:"rgba(30, 41, 59, 1)",borderColor:"rgba(255, 255, 255, 0.4)"}},label:{show:!1,color:"#ffffff"}},visualMap:{min:0,max:a,left:"left",top:"bottom",text:["高","低"],textStyle:{color:"#ffffff"},inRange:{color:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffcc","#fee090","#fdae61","#f46d43","#d73027","#a50026"]},calculable:!0},series:[{name:"数据分布",type:"heatmap",coordinateSystem:"geo",data:o,pointSize:20,blurSize:35},{name:"散点",type:"scatter",coordinateSystem:"geo",data:o.map(l=>({...l,symbolSize:Math.max(8,Math.min(30,l.value[2]/a*30)),itemStyle:{color:i(l.sentiment),opacity:.8}})),symbol:"circle",emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}],animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}},[e,t]);function i(o){switch(o){case"positive":return"#10b981";case"negative":return"#ef4444";case"neutral":return"#6b7280";default:return"#6b7280"}}return h.jsx(te.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.3},className:D("chart-container",r),children:h.jsx(Rt,{option:s,style:{height:`${n}px`,width:"100%"},opts:{renderer:"canvas"},notMerge:!0,lazyUpdate:!0})})},Pm={BarChart3:Sr,TrendingUp:Ni,TrendingDown:Gf,Minus:Wf},km={blue:{bg:"bg-blue-500/20",text:"text-blue-400",icon:"text-blue-400",gradient:"from-blue-400 to-blue-600"},green:{bg:"bg-green-500/20",text:"text-green-400",icon:"text-green-400",gradient:"from-green-400 to-green-600"},red:{bg:"bg-red-500/20",text:"text-red-400",icon:"text-red-400",gradient:"from-red-400 to-red-600"},gray:{bg:"bg-gray-500/20",text:"text-gray-400",icon:"text-gray-400",gradient:"from-gray-400 to-gray-600"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",icon:"text-purple-400",gradient:"from-purple-400 to-purple-600"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",icon:"text-yellow-400",gradient:"from-yellow-400 to-yellow-600"}},zt=({title:e,value:t,change:n,icon:r="BarChart3",color:s="blue",className:i,loading:o=!1})=>{const a=Pm[r]||Sr,l=km[s],c=n!==void 0&&n>=0,d=c?Ef:Af;return o?h.jsx("div",{className:D("data-card animate-pulse",i),children:h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs("div",{className:"space-y-2",children:[h.jsx("div",{className:"h-4 bg-gray-700 rounded w-20"}),h.jsx("div",{className:"h-8 bg-gray-700 rounded w-16"}),h.jsx("div",{className:"h-3 bg-gray-700 rounded w-12"})]}),h.jsx("div",{className:"w-12 h-12 bg-gray-700 rounded-lg"})]})}):h.jsxs(te.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:D("data-card cursor-pointer",i),children:[h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs("div",{className:"flex-1",children:[h.jsx("h3",{className:"text-sm font-medium text-gray-400 mb-2",children:e}),h.jsxs("div",{className:"space-y-2",children:[h.jsx("div",{className:D("text-3xl font-bold bg-gradient-to-r bg-clip-text text-transparent",l.gradient),children:pe(t)}),n!==void 0&&h.jsxs("div",{className:"flex items-center space-x-1",children:[h.jsx(d,{className:D("w-3 h-3",c?"text-green-400":"text-red-400")}),h.jsxs("span",{className:D("text-xs font-medium",c?"text-green-400":"text-red-400"),children:[Math.abs(n).toFixed(1),"%"]}),h.jsx("span",{className:"text-xs text-gray-500",children:"vs 上期"})]})]})]}),h.jsx("div",{className:D("w-12 h-12 rounded-lg flex items-center justify-center",l.bg),children:h.jsx(a,{className:D("w-6 h-6",l.icon)})})]}),h.jsx("div",{className:D("absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r rounded-b-lg",l.gradient)})]})},jm={success:{icon:Vf,bg:"bg-green-500/20",border:"border-green-500/30",text:"text-green-400",iconColor:"text-green-400"},error:{icon:qf,bg:"bg-red-500/20",border:"border-red-500/30",text:"text-red-400",iconColor:"text-red-400"},warning:{icon:Vi,bg:"bg-yellow-500/20",border:"border-yellow-500/30",text:"text-yellow-400",iconColor:"text-yellow-400"},info:{icon:Bf,bg:"bg-blue-500/20",border:"border-blue-500/30",text:"text-blue-400",iconColor:"text-blue-400"}},Us=({type:e,title:t,message:n,dismissible:r=!1,onDismiss:s,action:i,className:o})=>{const[a,l]=ue.useState(!0),c=jm[e],u=c.icon,d=()=>{l(!1),setTimeout(()=>{s==null||s()},300)};return h.jsx(Ei,{children:a&&h.jsx(te.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:.3},className:D("glass-card border rounded-lg p-4 max-w-md",c.bg,c.border,o),children:h.jsxs("div",{className:"flex items-start space-x-3",children:[h.jsx("div",{className:"flex-shrink-0",children:h.jsx(u,{className:D("w-5 h-5",c.iconColor)})}),h.jsxs("div",{className:"flex-1 min-w-0",children:[h.jsx("h3",{className:D("text-sm font-semibold",c.text),children:t}),h.jsx("p",{className:"text-sm text-gray-300 mt-1",children:n}),i&&h.jsx("div",{className:"mt-3",children:h.jsxs("button",{onClick:i.onClick,disabled:i.loading,className:D("inline-flex items-center space-x-2 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",c.text,"hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed"),children:[i.loading&&h.jsx(Di,{className:"w-3 h-3 animate-spin"}),h.jsx("span",{children:i.label})]})})]}),r&&h.jsx("button",{onClick:d,className:"flex-shrink-0 p-1 rounded-md hover:bg-white/10 transition-colors",children:h.jsx(Xf,{className:"w-4 h-4 text-gray-400 hover:text-white"})})]})})})},Mm={small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8"},Gs={blue:"text-blue-400",white:"text-white",gray:"text-gray-400"},Rm=({size:e="medium",color:t="blue",text:n,className:r})=>h.jsxs(te.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:D("flex flex-col items-center justify-center space-y-2",r),children:[h.jsx(zf,{className:D("animate-spin",Mm[e],Gs[t])}),n&&h.jsx("p",{className:D("text-sm",Gs[t]),children:n})]});class Am{constructor(t="ws://localhost:8080"){st(this,"socket",null);st(this,"reconnectAttempts",0);st(this,"maxReconnectAttempts",5);st(this,"reconnectDelay",1e3);st(this,"listeners",new Map);this.url=t}connect(){return new Promise((t,n)=>{try{this.socket=ma(this.url,{transports:["websocket"],autoConnect:!0,reconnection:!0,reconnectionAttempts:this.maxReconnectAttempts,reconnectionDelay:this.reconnectDelay}),this.socket.on("connect",()=>{console.log("WebSocket connected"),this.reconnectAttempts=0,this.emit("connected",!0),t()}),this.socket.on("disconnect",r=>{console.log("WebSocket disconnected:",r),this.emit("connected",!1),r==="io server disconnect"&&this.reconnect()}),this.socket.on("connect_error",r=>{console.error("WebSocket connection error:",r),this.emit("error",r),n(r)}),this.socket.on("reconnect",r=>{console.log("WebSocket reconnected after",r,"attempts"),this.emit("reconnected",r)}),this.socket.on("reconnect_error",r=>{console.error("WebSocket reconnection error:",r),this.emit("reconnectError",r)}),this.socket.on("reconnect_failed",()=>{console.error("WebSocket reconnection failed"),this.emit("reconnectFailed")}),this.socket.on("data:update",r=>{this.emit("dataUpdate",r)}),this.socket.on("data:alert",r=>{this.emit("alert",r)}),this.socket.on("data:heartbeat",r=>{this.emit("heartbeat",r)})}catch(r){console.error("Failed to create WebSocket connection:",r),n(r)}})}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.emit("connected",!1))}reconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.socket&&this.socket.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.emit("maxReconnectAttemptsReached"))}send(t,n){this.socket&&this.socket.connected?this.socket.emit(t,n):console.warn("WebSocket is not connected")}on(t,n){this.listeners.has(t)||this.listeners.set(t,[]),this.listeners.get(t).push(n)}off(t,n){if(this.listeners.has(t))if(n){const r=this.listeners.get(t),s=r.indexOf(n);s>-1&&r.splice(s,1)}else this.listeners.delete(t)}emit(t,n){this.listeners.has(t)&&this.listeners.get(t).forEach(r=>{try{r(n)}catch(s){console.error("Error in WebSocket event callback:",s)}})}get isConnected(){var t;return((t=this.socket)==null?void 0:t.connected)||!1}get reconnectCount(){return this.reconnectAttempts}setMaxReconnectAttempts(t){this.maxReconnectAttempts=t}setReconnectDelay(t){this.reconnectDelay=t}}const le=new Am,Em=(e={})=>{const{autoConnect:t=!0,reconnectOnClose:n=!0,maxReconnectAttempts:r=5,reconnectDelay:s=1e3}=e,{setConnectionStatus:i,setRealTimeData:o,updateStatistics:a,updateHotTopics:l,updateKeywords:c,updateTimeSeries:u,updateLocations:d,addRecentPost:f,setError:m,incrementRetries:g,resetRetries:p}=Ge(),y=x.useRef(),v=x.useRef(0),b=x.useCallback(async()=>{try{await le.connect(),i(!0),p(),v.current=0,m(null)}catch(R){console.error("WebSocket connection failed:",R),i(!1),m({code:"WEBSOCKET_CONNECTION_FAILED",message:"WebSocket 连接失败",details:R})}},[i,p,m]),w=x.useCallback(()=>{le.disconnect(),i(!1),y.current&&clearTimeout(y.current)},[i]),C=x.useCallback(()=>{if(v.current>=r){console.error("Max reconnection attempts reached"),m({code:"WEBSOCKET_MAX_RECONNECT_ATTEMPTS",message:"达到最大重连次数"});return}v.current++,g(),console.log(`Attempting to reconnect... (${v.current}/${r})`),y.current=setTimeout(()=>{b()},s*v.current)},[r,s,b,g,m]),P=x.useCallback((R,E)=>{le.send(R,E)},[]);return x.useEffect(()=>{const R=V=>{i(V),!V&&n&&C()},E=V=>{try{switch(V.type){case"update":V.data.type==="realtime"?o(V.data.payload):V.data.type==="statistics"?a(V.data.payload):V.data.type==="hotTopics"?l(V.data.payload):V.data.type==="keywords"?c(V.data.payload):V.data.type==="timeSeries"?u(V.data.payload):V.data.type==="locations"?d(V.data.payload):V.data.type==="newPost"&&f(V.data.payload);break;case"alert":console.warn("WebSocket Alert:",V.data);break;case"heartbeat":break;default:console.log("Unknown WebSocket message type:",V.type)}}catch(ae){console.error("Error processing WebSocket message:",ae),m({code:"WEBSOCKET_MESSAGE_PROCESSING_ERROR",message:"处理 WebSocket 消息时出错",details:ae})}},j=V=>{console.error("WebSocket error:",V),m({code:"WEBSOCKET_ERROR",message:"WebSocket 连接错误",details:V})},O=V=>{console.log("WebSocket reconnected after",V,"attempts"),p(),v.current=0,m(null)};return le.on("connected",R),le.on("dataUpdate",E),le.on("alert",E),le.on("heartbeat",E),le.on("error",j),le.on("reconnected",O),t&&b(),()=>{le.off("connected",R),le.off("dataUpdate",E),le.off("alert",E),le.off("heartbeat",E),le.off("error",j),le.off("reconnected",O),y.current&&clearTimeout(y.current)}},[t,n,b,C,i,o,a,l,c,u,d,f,m,p]),{connect:b,disconnect:w,sendMessage:P,isConnected:le.isConnected,reconnectCount:le.reconnectCount}},Je=pa.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});Je.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("Request Error:",e),Promise.reject(e)));Je.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{if(console.error("Response Error:",e),e.response){const{status:t,data:n}=e.response;switch(t){case 401:console.error("Unauthorized access");break;case 403:console.error("Forbidden access");break;case 404:console.error("Resource not found");break;case 500:console.error("Internal server error");break;default:console.error("API Error:",(n==null?void 0:n.message)||"Unknown error")}}else e.request?console.error("Network error"):console.error("Error:",e.message);return Promise.reject(e)});const je={get:async(e,t)=>(await Je.get(e,t)).data.data,post:async(e,t,n)=>(await Je.post(e,t,n)).data.data,put:async(e,t,n)=>(await Je.put(e,t,n)).data.data,delete:async(e,t)=>(await Je.delete(e,t)).data.data,patch:async(e,t,n)=>(await Je.patch(e,t,n)).data.data},it={getRealTimeData:async(e="24h")=>je.get(`/sentiment/realtime?range=${e}`),getStatistics:async(e="24h")=>je.get(`/sentiment/statistics?range=${e}`),getHotTopics:async(e=10)=>je.get(`/sentiment/hot-topics?limit=${e}`),getKeywords:async(e=50)=>je.get(`/sentiment/keywords?limit=${e}`),getTimeSeries:async(e="24h")=>je.get(`/sentiment/time-series?range=${e}`),getLocationData:async()=>je.get("/sentiment/locations"),getRecentPosts:async(e=20)=>je.get(`/sentiment/recent-posts?limit=${e}`),search:async(e,t)=>je.post("/sentiment/search",{query:e,filters:t})},Hs={getStatus:async()=>je.get("/system/status"),getPerformance:async()=>je.get("/system/performance"),healthCheck:async()=>je.get("/system/health")},Vm=(e={})=>{const{autoRefresh:t=!0,refreshInterval:n=3e4,onError:r,onSuccess:s}=e,{selectedTimeRange:i,setRealTimeData:o,setSystemStatus:a,setLoading:l,setError:c,dashboardConfig:u}=Ge(),d=x.useRef(),f=x.useRef(!1),m=x.useCallback(async()=>{if(!f.current)try{f.current=!0,l(!0),c(null);const[b,w,C,P,R,E]=await Promise.all([it.getStatistics(i),it.getHotTopics(10),it.getKeywords(50),it.getTimeSeries(i),it.getLocationData(),it.getRecentPosts(20)]),j={statistics:b,hotTopics:w,keywords:C,timeSeries:P,locations:R,recentPosts:E};o(j),s==null||s(j)}catch(b){console.error("Failed to fetch real-time data:",b),c({code:"FETCH_REALTIME_DATA_ERROR",message:"获取实时数据失败",details:b}),r==null||r(b)}finally{f.current=!1,l(!1)}},[i,o,l,c,s,r]),g=x.useCallback(async()=>{try{const[b,w]=await Promise.all([Hs.getStatus(),Hs.getPerformance()]),C={...b,performance:w,lastUpdate:new Date().toISOString()};a(C)}catch(b){console.error("Failed to fetch system status:",b)}},[a]),p=x.useCallback(async()=>{await Promise.all([m(),g()])},[m,g]),y=x.useCallback(()=>{d.current&&clearInterval(d.current);const b=u.autoRefresh?u.refreshInterval:n;d.current=setInterval(()=>{p()},b)},[p,n,u.autoRefresh,u.refreshInterval]),v=x.useCallback(()=>{d.current&&(clearInterval(d.current),d.current=void 0)},[]);return x.useEffect(()=>(p(),t&&u.autoRefresh&&y(),()=>{v()}),[i,t,u.autoRefresh]),x.useEffect(()=>{u.autoRefresh?y():v()},[u.autoRefresh,u.refreshInterval,y,v]),{fetchRealTimeData:m,fetchSystemStatus:g,refreshData:p,startAutoRefresh:y,stopAutoRefresh:v}},Dm=e=>{const{onRefresh:t,interval:n=3e4,enabled:r=!0,immediate:s=!0}=e,{dashboardConfig:i}=Ge(),[o,a]=x.useState(0),[l,c]=x.useState(!1),u=x.useRef(),d=x.useRef(),f=x.useRef(!1),m=i.autoRefresh?i.refreshInterval:n,g=x.useCallback(async()=>{if(!f.current)try{f.current=!0,await t(),a(Math.floor(m/1e3))}catch(w){console.error("Auto refresh failed:",w)}finally{f.current=!1}},[t,m]),p=x.useCallback(()=>{u.current&&clearInterval(u.current),d.current&&clearInterval(d.current),c(!0),a(Math.floor(m/1e3)),s&&g(),u.current=setInterval(()=>{g()},m),d.current=setInterval(()=>{a(w=>w<=1?Math.floor(m/1e3):w-1)},1e3)},[m,s,g]),y=x.useCallback(()=>{u.current&&(clearInterval(u.current),u.current=void 0),d.current&&(clearInterval(d.current),d.current=void 0),c(!1),a(0)},[]),v=x.useCallback(()=>{l&&(y(),p())},[l,y,p]),b=x.useCallback(async()=>{await g(),v()},[g,v]);return x.useEffect(()=>(r&&i.autoRefresh?p():y(),()=>{y()}),[r,i.autoRefresh,p,y]),x.useEffect(()=>{l&&v()},[m,v,l]),x.useEffect(()=>()=>{y()},[y]),{isActive:l,countdown:o,start:p,stop:y,reset:v,trigger:b,isRefreshing:f.current}},Ys=()=>{const{realTimeData:e,isLoading:t,error:n}=Ge();Em({autoConnect:!0,reconnectOnClose:!0,maxReconnectAttempts:5});const{refreshData:r}=Vm({autoRefresh:!0,refreshInterval:3e4});if(Dm({onRefresh:r,interval:3e4,enabled:!0,immediate:!1}),t&&!e)return h.jsx("div",{className:"flex items-center justify-center h-full",children:h.jsx(Rm,{size:"large"})});if(n&&!e)return h.jsx("div",{className:"flex items-center justify-center h-full",children:h.jsx(Us,{type:"error",title:"数据加载失败",message:n.message,action:{label:"重试",onClick:r}})});if(!e)return h.jsx("div",{className:"flex items-center justify-center h-full",children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"text-gray-400 mb-4",children:"暂无数据"}),h.jsx("button",{onClick:r,className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"加载数据"})]})});const{statistics:s,hotTopics:i,keywords:o,timeSeries:a,locations:l}=e;return h.jsxs("div",{className:"space-y-6",children:[h.jsxs(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[h.jsx(zt,{title:"总数据量",value:s.total,change:s.growthRate,icon:"BarChart3",color:"blue"}),h.jsx(zt,{title:"正面情感",value:s.positive,change:s.positive/s.total*100-33.33,icon:"TrendingUp",color:"green"}),h.jsx(zt,{title:"负面情感",value:s.negative,change:s.negative/s.total*100-33.33,icon:"TrendingDown",color:"red"}),h.jsx(zt,{title:"中性情感",value:s.neutral,change:s.neutral/s.total*100-33.33,icon:"Minus",color:"gray"})]}),h.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[h.jsx(te.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.1},className:"data-card",children:h.jsx(gm,{data:a,title:"情感趋势分析",height:350})}),h.jsx(te.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},className:"data-card",children:h.jsx(ym,{data:s,title:"情感分布",height:350})})]}),h.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[h.jsx(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"data-card",children:h.jsx(Cm,{data:i,title:"热点话题排行",height:400,maxTopics:8,orientation:"horizontal"})}),h.jsx(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"data-card",children:h.jsx(Sm,{data:o,title:"关键词分析",height:400,maxWords:50})}),h.jsx(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.5},className:"data-card",children:h.jsx(Tm,{data:l,title:"地理分布",height:400})})]}),n&&h.jsx(te.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},className:"fixed bottom-4 right-4 z-50",children:h.jsx(Us,{type:"warning",title:"数据更新异常",message:n.message,dismissible:!0,onDismiss:()=>{}})})]})},Nm=()=>{const{dashboardConfig:e}=Ge();return h.jsx(cl,{children:h.jsx("div",{className:D("min-h-screen transition-colors duration-300",e.theme==="dark"?"dark":""),children:h.jsx(pm,{children:h.jsx(Ei,{mode:"wait",children:h.jsxs(il,{children:[h.jsx(In,{path:"/",element:h.jsx(te.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:.3},children:h.jsx(Ys,{})},"dashboard")}),h.jsx(In,{path:"/dashboard",element:h.jsx(te.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:.3},children:h.jsx(Ys,{})},"dashboard")})]})})})})})};class Lm extends ue.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,n){console.error("Application Error:",t,n)}render(){var t;return this.state.hasError?h.jsx("div",{className:"min-h-screen bg-dark-900 flex items-center justify-center",children:h.jsxs("div",{className:"text-center",children:[h.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"应用程序出现错误"}),h.jsx("p",{className:"text-gray-400 mb-6",children:((t=this.state.error)==null?void 0:t.message)||"未知错误"}),h.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"重新加载"})]})}):this.props.children}}Nn.createRoot(document.getElementById("root")).render(h.jsx(ue.StrictMode,{children:h.jsx(Lm,{children:h.jsx(Nm,{})})}));
//# sourceMappingURL=index-BGAjxj_Y.js.map
