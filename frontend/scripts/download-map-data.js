#!/usr/bin/env node

/**
 * 下载中国地图GeoJSON数据脚本
 * 用于获取详细的中国地图数据并保存到本地
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 地图数据源配置
const MAP_SOURCES = {
  // 阿里云DataV - 中国全图（包含省份边界）
  china_full: {
    url: 'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json',
    filename: 'china.json',
    description: '中国全图（包含省份边界）'
  },
  // 中国简化版
  china_simple: {
    url: 'https://geo.datav.aliyun.com/areas_v3/bound/100000.json',
    filename: 'china-simple.json',
    description: '中国简化版'
  }
};

// 确保public目录存在
const publicDir = path.join(__dirname, '../public');
const mapsDir = path.join(publicDir, 'maps');

if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

if (!fs.existsSync(mapsDir)) {
  fs.mkdirSync(mapsDir, { recursive: true });
}

/**
 * 下载文件
 * @param {string} url 
 * @param {string} filepath 
 * @returns {Promise}
 */
function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    console.log(`正在下载: ${url}`);
    
    const request = https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }

      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });

      response.on('end', () => {
        try {
          // 验证JSON格式
          const jsonData = JSON.parse(data);
          
          // 美化JSON并保存
          const prettyJson = JSON.stringify(jsonData, null, 2);
          fs.writeFileSync(filepath, prettyJson, 'utf8');
          
          console.log(`✅ 下载成功: ${filepath}`);
          console.log(`   文件大小: ${(prettyJson.length / 1024).toFixed(2)} KB`);
          
          resolve(filepath);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    request.on('error', (error) => {
      reject(new Error(`下载失败: ${error.message}`));
    });

    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('下载超时'));
    });
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('🗺️  开始下载中国地图数据...\n');

  for (const [key, config] of Object.entries(MAP_SOURCES)) {
    try {
      const filepath = path.join(mapsDir, config.filename);
      await downloadFile(config.url, filepath);
      console.log(`📍 ${config.description} - 已保存\n`);
    } catch (error) {
      console.error(`❌ ${config.description} 下载失败:`, error.message);
      console.log('');
    }
  }

  // 创建使用说明文件
  const readmeContent = `# 地图数据文件说明

## 文件列表

- \`china.json\` - 中国全图（包含省份边界，推荐使用）
- \`china-simple.json\` - 中国简化版（文件较小）

## 使用方法

在React组件中使用：

\`\`\`typescript
useEffect(() => {
  fetch('/maps/china.json')
    .then(response => response.json())
    .then(geoJson => {
      echarts.registerMap('china', geoJson);
    })
    .catch(error => {
      console.error('加载地图数据失败:', error);
    });
}, []);
\`\`\`

## 数据来源

- 阿里云DataV地图数据：https://datav.aliyun.com/tools/atlas
- 坐标系：GCJ-02（适合中国地区使用）

## 更新数据

运行以下命令重新下载最新数据：

\`\`\`bash
node scripts/download-map-data.js
\`\`\`

最后更新时间：${new Date().toLocaleString('zh-CN')}
`;

  fs.writeFileSync(path.join(mapsDir, 'README.md'), readmeContent, 'utf8');
  
  console.log('🎉 所有任务完成！');
  console.log(`📁 地图数据已保存到: ${mapsDir}`);
  console.log('💡 请查看 README.md 了解使用方法');
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  });
}

export { downloadFile, MAP_SOURCES };
