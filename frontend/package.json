{"name": "weibo-sentiment-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "update-maps": "node scripts/download-map-data.js"}, "dependencies": {"axios": "^1.6.0", "clsx": "^2.0.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-countries-js": "^1.0.5", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^24.2.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}