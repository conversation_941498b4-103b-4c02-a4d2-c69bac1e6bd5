import React from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  ArrowUp,
  ArrowDown,
  LucideIcon
} from 'lucide-react';
import { formatNumber, cn } from '@/utils';

interface MetricCardProps {
  title: string;
  value: number;
  change?: number;
  icon?: string;
  color?: 'blue' | 'green' | 'red' | 'gray' | 'purple' | 'yellow';
  className?: string;
  loading?: boolean;
}

const iconMap: Record<string, LucideIcon> = {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Minus,
};

const colorMap = {
  blue: {
    bg: 'bg-blue-500/20',
    text: 'text-blue-400',
    icon: 'text-blue-400',
    gradient: 'from-blue-400 to-blue-600',
  },
  green: {
    bg: 'bg-green-500/20',
    text: 'text-green-400',
    icon: 'text-green-400',
    gradient: 'from-green-400 to-green-600',
  },
  red: {
    bg: 'bg-red-500/20',
    text: 'text-red-400',
    icon: 'text-red-400',
    gradient: 'from-red-400 to-red-600',
  },
  gray: {
    bg: 'bg-gray-500/20',
    text: 'text-gray-400',
    icon: 'text-gray-400',
    gradient: 'from-gray-400 to-gray-600',
  },
  purple: {
    bg: 'bg-purple-500/20',
    text: 'text-purple-400',
    icon: 'text-purple-400',
    gradient: 'from-purple-400 to-purple-600',
  },
  yellow: {
    bg: 'bg-yellow-500/20',
    text: 'text-yellow-400',
    icon: 'text-yellow-400',
    gradient: 'from-yellow-400 to-yellow-600',
  },
};

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon = 'BarChart3',
  color = 'blue',
  className,
  loading = false,
}) => {
  const IconComponent = iconMap[icon] || BarChart3;
  const colors = colorMap[color];

  const isPositiveChange = change !== undefined && change >= 0;
  const changeIcon = isPositiveChange ? ArrowUp : ArrowDown;
  const ChangeIcon = changeIcon;

  if (loading) {
    return (
      <div className={cn('data-card animate-pulse', className)}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-gray-700 rounded w-20"></div>
            <div className="h-8 bg-gray-700 rounded w-16"></div>
            <div className="h-3 bg-gray-700 rounded w-12"></div>
          </div>
          <div className="w-12 h-12 bg-gray-700 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={cn('data-card cursor-pointer', className)}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-400 mb-2">
            {title}
          </h3>
          
          <div className="space-y-2">
            <div className={cn(
              'text-3xl font-bold bg-gradient-to-r bg-clip-text text-transparent',
              colors.gradient
            )}>
              {formatNumber(value)}
            </div>
            
            {change !== undefined && (
              <div className="flex items-center space-x-1">
                <ChangeIcon className={cn(
                  'w-3 h-3',
                  isPositiveChange ? 'text-green-400' : 'text-red-400'
                )} />
                <span className={cn(
                  'text-xs font-medium',
                  isPositiveChange ? 'text-green-400' : 'text-red-400'
                )}>
                  {Math.abs(change).toFixed(1)}%
                </span>
                <span className="text-xs text-gray-500">
                  vs 上期
                </span>
              </div>
            )}
          </div>
        </div>
        
        <div className={cn(
          'w-12 h-12 rounded-lg flex items-center justify-center',
          colors.bg
        )}>
          <IconComponent className={cn('w-6 h-6', colors.icon)} />
        </div>
      </div>
      
      {/* 装饰性渐变条 */}
      <div className={cn(
        'absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r rounded-b-lg',
        colors.gradient
      )}></div>
    </motion.div>
  );
};

export default MetricCard;
