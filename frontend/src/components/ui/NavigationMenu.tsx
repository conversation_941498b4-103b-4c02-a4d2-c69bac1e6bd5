import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  BarChart3,
  Map,
  Maximize,
  Settings,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/utils';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

interface NavigationMenuProps {
  className?: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: '主仪表板',
    path: '/',
    icon: Home,
    description: '实时数据概览'
  },
  {
    id: 'analytics',
    label: '数据分析',
    path: '/dashboard',
    icon: BarChart3,
    description: '深度数据分析'
  },
  {
    id: 'map-test',
    label: '地图测试',
    path: '/map-test',
    icon: Map,
    description: '地图功能测试'
  },
  {
    id: 'fullscreen-demo',
    label: '全屏演示',
    path: '/fullscreen-demo',
    icon: Maximize,
    description: '全屏功能演示'
  }
];

const NavigationMenu: React.FC<NavigationMenuProps> = ({ className }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <nav className={cn('space-y-2', className)}>
      <h3 className="text-sm font-medium text-gray-400 uppercase tracking-wider mb-4">
        导航菜单
      </h3>
      
      {navigationItems.map((item, index) => {
        const isActive = location.pathname === item.path;
        const Icon = item.icon;
        
        return (
          <motion.button
            key={item.id}
            onClick={() => handleNavigation(item.path)}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className={cn(
              'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 group',
              isActive
                ? 'bg-blue-600/20 text-blue-400 border border-blue-500/30'
                : 'text-gray-400 hover:text-white hover:bg-white/5'
            )}
          >
            <Icon className={cn(
              'w-5 h-5 transition-colors',
              isActive ? 'text-blue-400' : 'text-gray-500 group-hover:text-gray-300'
            )} />
            
            <div className="flex-1 min-w-0">
              <div className={cn(
                'text-sm font-medium transition-colors',
                isActive ? 'text-blue-400' : 'text-gray-300 group-hover:text-white'
              )}>
                {item.label}
              </div>
              {item.description && (
                <div className="text-xs text-gray-500 truncate">
                  {item.description}
                </div>
              )}
            </div>
            
            {isActive && (
              <motion.div
                layoutId="activeIndicator"
                className="w-2 h-2 bg-blue-400 rounded-full"
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              />
            )}
          </motion.button>
        );
      })}
      
      {/* 分隔线 */}
      <div className="border-t border-white/10 my-4"></div>
      
      {/* 其他功能 */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
          其他功能
        </h4>
        
        <button className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-200 group">
          <Settings className="w-4 h-4 text-gray-500 group-hover:text-gray-300" />
          <span className="text-sm">系统设置</span>
        </button>
        
        <button className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-200 group">
          <HelpCircle className="w-4 h-4 text-gray-500 group-hover:text-gray-300" />
          <span className="text-sm">帮助文档</span>
        </button>
      </div>
    </nav>
  );
};

export default NavigationMenu;
