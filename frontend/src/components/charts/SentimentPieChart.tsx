import React from 'react';
import ReactECharts from 'echarts-for-react';
import { motion } from 'framer-motion';
import { StatisticsData } from '@/types';
import { cn, formatNumber } from '@/utils';

interface SentimentPieChartProps {
  data: StatisticsData;
  title?: string;
  height?: number;
  className?: string;
  showPercentage?: boolean;
}

const SentimentPieChart: React.FC<SentimentPieChartProps> = ({
  data,
  title = '情感分析分布',
  height = 400,
  className,
  showPercentage = true,
}) => {
  const option = React.useMemo(() => {
    const total = data.total;
    const pieData = [
      {
        value: data.positive,
        name: '正面',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#10b981' },
              { offset: 1, color: '#059669' },
            ],
          },
        },
      },
      {
        value: data.negative,
        name: '负面',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ef4444' },
              { offset: 1, color: '#dc2626' },
            ],
          },
        },
      },
      {
        value: data.neutral,
        name: '中性',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#6b7280' },
              { offset: 1, color: '#4b5563' },
            ],
          },
        },
      },
    ];

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (params: any) => {
          const percentage = ((params.value / total) * 100).toFixed(1);
          return `
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <span style="display: inline-block; width: 12px; height: 12px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="font-weight: bold; margin-right: 8px;">${params.name}</span>
            </div>
            <div style="margin-left: 20px;">
              <div>数量: <span style="font-weight: bold;">${formatNumber(params.value)}</span></div>
              <div>占比: <span style="font-weight: bold;">${percentage}%</span></div>
            </div>
          `;
        },
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (name: string) => {
          const item = pieData.find(d => d.name === name);
          if (!item) return name;
          const percentage = ((item.value / total) * 100).toFixed(1);
          return `${name} ${showPercentage ? `(${percentage}%)` : ''}`;
        },
      },
      series: [
        {
          name: '情感分析',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: 'rgba(0, 0, 0, 0.2)',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#ffffff',
              formatter: (params: any) => {
                const percentage = ((params.value / total) * 100).toFixed(1);
                return `${params.name}\n${formatNumber(params.value)}\n${percentage}%`;
              },
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: (_idx: number) => Math.random() * 200,
        },
      ],
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: `总计\n${formatNumber(total)}`,
            textAlign: 'center',
            fill: '#ffffff',
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
      ],
    };
  }, [data, title, showPercentage]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className={cn('chart-container', className)}
    >
      <ReactECharts
        option={option}
        style={{ height: `${height}px`, width: '100%' }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
    </motion.div>
  );
};

export default SentimentPieChart;
