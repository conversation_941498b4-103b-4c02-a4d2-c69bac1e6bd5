import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';

const SimpleMapTest: React.FC = () => {
  const [mapReady, setMapReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadMap = async () => {
      try {
        console.log('开始加载地图数据...');
        
        // 尝试加载本地地图数据
        const response = await fetch('/maps/china.json');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const geoJson = await response.json();
        console.log('地图数据加载成功，特征数量:', geoJson.features?.length);
        
        // 注册地图
        echarts.registerMap('china', geoJson);
        console.log('地图注册成功');
        
        setMapReady(true);
        setError(null);
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : String(err);
        console.error('地图加载失败:', errorMsg);
        setError(errorMsg);
        
        // 使用简单的备用地图
        const fallbackGeoJson = {
          type: "FeatureCollection",
          features: [
            {
              type: "Feature",
              properties: { name: "中国" },
              geometry: {
                type: "Polygon",
                coordinates: [[
                  [73.66, 53.56], [134.77, 53.56], [134.77, 18.16], [73.66, 18.16], [73.66, 53.56]
                ]]
              }
            }
          ]
        };
        
        echarts.registerMap('china', fallbackGeoJson);
        setMapReady(true);
        console.log('使用备用地图数据');
      }
    };

    loadMap();
  }, []);

  const option = {
    title: {
      text: '地图测试',
      left: 'center',
      textStyle: {
        color: '#ffffff',
        fontSize: 18,
      },
    },
    geo: {
      map: 'china',
      roam: true,
      zoom: 1.2,
      center: [104.114129, 37.550339],
      itemStyle: {
        areaColor: 'rgba(30, 41, 59, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(30, 41, 59, 1)',
          borderColor: 'rgba(255, 255, 255, 0.4)',
        },
      },
      label: {
        show: true,
        color: '#ffffff',
        fontSize: 12,
      },
    },
    series: [
      {
        name: '测试数据',
        type: 'scatter',
        coordinateSystem: 'geo',
        data: [
          { name: '北京', value: [116.4074, 39.9042, 100] },
          { name: '上海', value: [121.4737, 31.2304, 80] },
          { name: '广州', value: [113.2644, 23.1291, 60] },
        ],
        symbolSize: 20,
        itemStyle: {
          color: '#10b981',
          opacity: 0.8,
        },
      },
    ],
  };

  if (!mapReady) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-800 rounded-lg">
        <div className="text-white text-center">
          <div className="animate-spin w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full mx-auto mb-2"></div>
          <div>正在加载地图数据...</div>
          {error && (
            <div className="text-red-400 text-sm mt-2">
              错误: {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <ReactECharts
        option={option}
        style={{ height: '500px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
      {error && (
        <div className="text-yellow-400 text-sm mt-2">
          警告: 使用备用地图数据 - {error}
        </div>
      )}
    </div>
  );
};

export default SimpleMapTest;
