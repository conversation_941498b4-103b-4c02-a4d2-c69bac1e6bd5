import React, { useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import { motion } from 'framer-motion';
import { LocationData } from '@/types';
import { cn, formatNumber } from '@/utils';
import * as echarts from 'echarts';

interface LocationHeatMapProps {
  data: LocationData[];
  title?: string;
  height?: number;
  className?: string;
}

const LocationHeatMap: React.FC<LocationHeatMapProps> = ({
  data,
  title = '地理位置分布',
  height = 400,
  className,
}) => {
  // 注册简化的中国地图
  useEffect(() => {
    // 简化的中国地图数据（只包含基本轮廓）
    const chinaGeoJson = {
      type: "FeatureCollection" as const,
      features: [
        {
          type: "Feature" as const,
          properties: { name: "中国" },
          geometry: {
            type: "Polygon" as const,
            coordinates: [[
              [73.66, 53.56], [134.77, 53.56], [134.77, 18.16], [73.66, 18.16], [73.66, 53.56]
            ]]
          }
        }
      ]
    };

    // 注册地图
    echarts.registerMap('china', chinaGeoJson as any);
  }, []);
  const option = React.useMemo(() => {
    // 处理数据，转换为 ECharts 需要的格式
    const processedData = data.map(item => ({
      name: item.name,
      value: [...item.coordinates, item.value],
      sentiment: item.sentiment,
    }));

    // 获取最大值用于颜色映射
    const maxValue = Math.max(...data.map(item => item.value));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (params: any) => {
          const item = data.find(d => d.name === params.name);
          if (!item) return '';
          
          const sentimentText = item.sentiment === 'positive' ? '正面' : 
                               item.sentiment === 'negative' ? '负面' : '中性';
          const sentimentColor = getSentimentColorHex(item.sentiment);
          
          return `
            <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
            <div style="margin-bottom: 4px;">
              数量: <span style="font-weight: bold;">${formatNumber(params.value[2])}</span>
            </div>
            <div style="margin-bottom: 4px;">
              坐标: <span style="font-family: monospace;">${params.value[0].toFixed(2)}, ${params.value[1].toFixed(2)}</span>
            </div>
            <div>
              情感倾向: <span style="color: ${sentimentColor}; font-weight: bold;">${sentimentText}</span>
            </div>
          `;
        },
      },
      geo: {
        map: 'china',
        roam: true,
        zoom: 1.2,
        center: [104.114129, 37.550339],
        itemStyle: {
          areaColor: 'rgba(30, 41, 59, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(30, 41, 59, 1)',
            borderColor: 'rgba(255, 255, 255, 0.4)',
          },
        },
        label: {
          show: false,
          color: '#ffffff',
        },
      },
      visualMap: {
        min: 0,
        max: maxValue,
        left: 'left',
        top: 'bottom',
        text: ['高', '低'],
        textStyle: {
          color: '#ffffff',
        },
        inRange: {
          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'],
        },
        calculable: true,
      },
      series: [
        {
          name: '数据分布',
          type: 'heatmap',
          coordinateSystem: 'geo',
          data: processedData,
          pointSize: 20,
          blurSize: 35,
        },
        {
          name: '散点',
          type: 'scatter',
          coordinateSystem: 'geo',
          data: processedData.map(item => ({
            ...item,
            symbolSize: Math.max(8, Math.min(30, (item.value[2] / maxValue) * 30)),
            itemStyle: {
              color: getSentimentColorHex(item.sentiment),
              opacity: 0.8,
            },
          })),
          symbol: 'circle',
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };
  }, [data, title]);

  // 获取情感对应的颜色（十六进制）
  function getSentimentColorHex(sentiment: 'positive' | 'negative' | 'neutral'): string {
    switch (sentiment) {
      case 'positive':
        return '#10b981';
      case 'negative':
        return '#ef4444';
      case 'neutral':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className={cn('chart-container', className)}
    >
      <ReactECharts
        option={option}
        style={{ height: `${height}px`, width: '100%' }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
    </motion.div>
  );
};

export default LocationHeatMap;
