import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { motion } from 'framer-motion';
import { LocationData } from '@/types';
import { cn, formatNumber } from '@/utils';
import * as echarts from 'echarts';

interface LocationHeatMapProps {
  data: LocationData[];
  title?: string;
  height?: number;
  className?: string;
}

const LocationHeatMap: React.FC<LocationHeatMapProps> = ({
  data,
  title = '地理位置分布',
  height = 400,
  className,
}) => {
  const [mapReady, setMapReady] = useState(false);

  // 注册详细的中国地图
  useEffect(() => {
    const loadChinaMap = async () => {
      try {
        // 方案一：优先使用本地详细地图数据
        const localResponse = await fetch('/maps/china.json');
        if (localResponse.ok) {
          const localGeoJson = await localResponse.json();
          echarts.registerMap('china', localGeoJson);
          console.log('✅ 本地详细地图数据加载成功');
          setMapReady(true);
          return;
        }
        throw new Error('本地文件不存在');
      } catch (localError) {
        console.warn('本地地图数据加载失败，尝试在线获取:', localError instanceof Error ? localError.message : String(localError));

        try {
          // 方案二：使用阿里云DataV API获取详细的中国地图数据
          const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');
          if (!response.ok) {
            throw new Error(`网络请求失败: ${response.status}`);
          }
          const geoJson = await response.json();

          // 注册地图
          echarts.registerMap('china', geoJson);
          console.log('✅ 在线地图数据加载成功');
          setMapReady(true);
        } catch (onlineError) {
          console.error('在线地图数据加载失败，使用简化地图:', onlineError instanceof Error ? onlineError.message : String(onlineError));

          // 方案三：最后的备用方案，使用简化地图数据
          try {
            const simpleResponse = await fetch('/maps/china-simple.json');
            if (simpleResponse.ok) {
              const simpleGeoJson = await simpleResponse.json();
              echarts.registerMap('china', simpleGeoJson);
              console.log('✅ 简化地图数据加载成功');
              setMapReady(true);
              return;
            }
          } catch (simpleError) {
            console.warn('简化地图数据也加载失败:', simpleError instanceof Error ? simpleError.message : String(simpleError));
          }

          // 最终备用方案：使用内置的基础地图数据
          const fallbackGeoJson = {
            type: "FeatureCollection" as const,
            features: [
              {
                type: "Feature" as const,
                properties: { name: "中国" },
                geometry: {
                  type: "Polygon" as const,
                  coordinates: [[
                    [73.66, 53.56], [134.77, 53.56], [134.77, 18.16], [73.66, 18.16], [73.66, 53.56]
                  ]]
                }
              }
            ]
          };
          echarts.registerMap('china', fallbackGeoJson as any);
          console.log('⚠️  使用内置简化地图数据');
          setMapReady(true);
        }
      }
    };

    loadChinaMap();
  }, []);
  const option = React.useMemo(() => {
    // 只有在地图准备好且有数据时才生成配置
    if (!mapReady || !data.length) {
      return {};
    }

    // 处理数据，转换为 ECharts 需要的格式
    const processedData = data.map(item => ({
      name: item.name,
      value: [...item.coordinates, item.value],
      sentiment: item.sentiment,
    }));

    // 获取最大值用于颜色映射
    const maxValue = Math.max(...data.map(item => item.value), 1); // 避免除零错误

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (params: any) => {
          const item = data.find(d => d.name === params.name);
          if (!item) return '';
          
          const sentimentText = item.sentiment === 'positive' ? '正面' : 
                               item.sentiment === 'negative' ? '负面' : '中性';
          const sentimentColor = getSentimentColorHex(item.sentiment);
          
          return `
            <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
            <div style="margin-bottom: 4px;">
              数量: <span style="font-weight: bold;">${formatNumber(params.value[2])}</span>
            </div>
            <div style="margin-bottom: 4px;">
              坐标: <span style="font-family: monospace;">${params.value[0].toFixed(2)}, ${params.value[1].toFixed(2)}</span>
            </div>
            <div>
              情感倾向: <span style="color: ${sentimentColor}; font-weight: bold;">${sentimentText}</span>
            </div>
          `;
        },
      },
      geo: {
        map: 'china',
        roam: true,
        zoom: 1.2,
        center: [104.114129, 37.550339],
        itemStyle: {
          areaColor: 'rgba(30, 41, 59, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(30, 41, 59, 1)',
            borderColor: 'rgba(255, 255, 255, 0.4)',
          },
        },
        label: {
          show: false,
          color: '#ffffff',
        },
      },
      visualMap: {
        min: 0,
        max: maxValue,
        left: 'left',
        top: 'bottom',
        text: ['高', '低'],
        textStyle: {
          color: '#ffffff',
        },
        inRange: {
          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'],
        },
        calculable: true,
      },
      series: [
        {
          name: '散点分布',
          type: 'scatter',
          coordinateSystem: 'geo',
          data: processedData.map(item => ({
            name: item.name,
            value: item.value,
            symbolSize: Math.max(8, Math.min(30, (item.value[2] / maxValue) * 30)),
            itemStyle: {
              color: getSentimentColorHex(item.sentiment),
              opacity: 0.8,
            },
          })),
          symbol: 'circle',
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };
  }, [data, title, mapReady]);

  // 获取情感对应的颜色（十六进制）
  function getSentimentColorHex(sentiment: 'positive' | 'negative' | 'neutral'): string {
    switch (sentiment) {
      case 'positive':
        return '#10b981';
      case 'negative':
        return '#ef4444';
      case 'neutral':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  }

  // 只在地图数据准备好后才渲染
  if (!mapReady) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className={cn('chart-container flex items-center justify-center', className)}
        style={{ height: `${height}px` }}
      >
        <div className="text-white/60 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full mx-auto mb-2"></div>
          <div>正在加载地图数据...</div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className={cn('chart-container', className)}
    >
      <ReactECharts
        option={option}
        style={{ height: `${height}px`, width: '100%' }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
    </motion.div>
  );
};

export default LocationHeatMap;
