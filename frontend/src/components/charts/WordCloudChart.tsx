import React from 'react';
import ReactECharts from 'echarts-for-react';
import { motion } from 'framer-motion';
import { KeywordData } from '@/types';
import { cn } from '@/utils';

// 注册词云图
import 'echarts-wordcloud';

interface WordCloudChartProps {
  data: KeywordData[];
  title?: string;
  height?: number;
  className?: string;
  maxWords?: number;
}

const WordCloudChart: React.FC<WordCloudChartProps> = ({
  data,
  title = '关键词词云',
  height = 400,
  className,
  maxWords = 100,
}) => {
  const option = React.useMemo(() => {
    // 处理数据，限制词数量并按权重排序
    const processedData = data
      .slice(0, maxWords)
      .map((item) => ({
        name: item.name,
        value: item.value,
        textStyle: {
          color: getSentimentColorHex(item.sentiment),
        },
      }));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (params: any) => {
          const sentiment = data.find(d => d.name === params.name)?.sentiment || 'neutral';
          const sentimentText = sentiment === 'positive' ? '正面' : 
                               sentiment === 'negative' ? '负面' : '中性';
          return `
            <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
            <div>权重: <span style="font-weight: bold;">${params.value}</span></div>
            <div>情感: <span style="color: ${getSentimentColorHex(sentiment)}; font-weight: bold;">${sentimentText}</span></div>
          `;
        },
      },
      series: [
        {
          type: 'wordCloud',
          gridSize: 8,
          sizeRange: [12, 60],
          rotationRange: [-45, 45],
          rotationStep: 15,
          shape: 'pentagon',
          width: '90%',
          height: '80%',
          left: 'center',
          top: 'center',
          drawOutOfBound: false,
          layoutAnimation: true,
          textStyle: {
            fontFamily: 'Inter, sans-serif',
            fontWeight: 'bold',
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#333',
            },
          },
          emphasis: {
            focus: 'self',
            textStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: processedData,
        },
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };
  }, [data, title, maxWords]);

  // 获取情感对应的颜色（十六进制）
  function getSentimentColorHex(sentiment: 'positive' | 'negative' | 'neutral'): string {
    switch (sentiment) {
      case 'positive':
        return '#10b981';
      case 'negative':
        return '#ef4444';
      case 'neutral':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className={cn('chart-container', className)}
    >
      <ReactECharts
        option={option}
        style={{ height: `${height}px`, width: '100%' }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
    </motion.div>
  );
};

export default WordCloudChart;
