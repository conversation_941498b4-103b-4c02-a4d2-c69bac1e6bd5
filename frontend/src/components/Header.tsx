import React from 'react';
import { Clock, Wifi, WifiOff, Activity, Settings, Maximize, Minimize } from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { useFullscreen } from '@/hooks/useFullscreen';
import { formatTime, cn } from '@/utils';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const { systemStatus, isConnected, selectedTimeRange, setSelectedTimeRange } = useAppStore();
  const { isFullscreen, isSupported, toggleFullscreen } = useFullscreen();
  const [currentTime, setCurrentTime] = React.useState(new Date());

  // 更新当前时间
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const timeRangeOptions = [
    { value: '1h', label: '1小时' },
    { value: '6h', label: '6小时' },
    { value: '24h', label: '24小时' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
  ] as const;

  return (
    <header className={cn(
      'glass-card border-b border-white/10 px-6 py-4 flex items-center justify-between',
      className
    )}>
      {/* 左侧：标题和状态 */}
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Activity className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">舆情监控大屏幕</h1>
            <p className="text-sm text-gray-400">实时数据分析与展示</p>
          </div>
        </div>

        {/* 连接状态 */}
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <>
              <Wifi className="w-4 h-4 text-green-400" />
              <span className="text-sm text-green-400">已连接</span>
              <div className="status-indicator status-online"></div>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-red-400" />
              <span className="text-sm text-red-400">连接断开</span>
              <div className="status-indicator status-offline"></div>
            </>
          )}
        </div>

        {/* 数据源状态 */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <div className={cn(
              'w-2 h-2 rounded-full',
              systemStatus.dataSource.weibo ? 'bg-green-400' : 'bg-red-400'
            )}></div>
            <span className="text-xs text-gray-400">微博</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className={cn(
              'w-2 h-2 rounded-full',
              systemStatus.dataSource.zhihu ? 'bg-green-400' : 'bg-red-400'
            )}></div>
            <span className="text-xs text-gray-400">知乎</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className={cn(
              'w-2 h-2 rounded-full',
              systemStatus.dataSource.news ? 'bg-green-400' : 'bg-red-400'
            )}></div>
            <span className="text-xs text-gray-400">新闻</span>
          </div>
        </div>
      </div>

      {/* 中间：时间范围选择 */}
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-400">时间范围:</span>
        <div className="flex bg-dark-800 rounded-lg p-1">
          {timeRangeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setSelectedTimeRange(option.value)}
              className={cn(
                'px-3 py-1 text-xs rounded-md transition-all duration-200',
                selectedTimeRange === option.value
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-dark-700'
              )}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* 右侧：时间和设置 */}
      <div className="flex items-center space-x-6">
        {/* 系统性能指标 */}
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center space-x-1">
            <span className="text-gray-400">CPU:</span>
            <span className={cn(
              'font-mono',
              systemStatus.performance.cpu > 80 ? 'text-red-400' :
              systemStatus.performance.cpu > 60 ? 'text-yellow-400' : 'text-green-400'
            )}>
              {systemStatus.performance.cpu.toFixed(1)}%
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-gray-400">内存:</span>
            <span className={cn(
              'font-mono',
              systemStatus.performance.memory > 80 ? 'text-red-400' :
              systemStatus.performance.memory > 60 ? 'text-yellow-400' : 'text-green-400'
            )}>
              {systemStatus.performance.memory.toFixed(1)}%
            </span>
          </div>
        </div>

        {/* 当前时间 */}
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <div className="text-right">
            <div className="text-sm font-mono text-white">
              {formatTime(currentTime)}
            </div>
            <div className="text-xs text-gray-400">
              最后更新: {formatTime(systemStatus.lastUpdate)}
            </div>
          </div>
        </div>

        {/* 全屏按钮 */}
        {isSupported && (
          <button
            onClick={() => toggleFullscreen()}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors group"
            title={isFullscreen ? '退出全屏' : '进入全屏'}
          >
            {isFullscreen ? (
              <Minimize className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
            ) : (
              <Maximize className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
            )}
          </button>
        )}

        {/* 设置按钮 */}
        <button className="p-2 rounded-lg hover:bg-white/10 transition-colors group">
          <Settings className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
        </button>
      </div>
    </header>
  );
};

export default Header;
