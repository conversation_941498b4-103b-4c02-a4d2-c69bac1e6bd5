import React from 'react';
import {
  Bar<PERSON>hart3,
  MessageS<PERSON>re,
  Hash,
  AlertTriangle,
  Eye,
  Filter
} from 'lucide-react';
import NavigationMenu from './ui/NavigationMenu';
import { useAppStore } from '@/stores/useAppStore';
import { formatNumber, formatRelativeTime, cn } from '@/utils';

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const { realTimeData, isConnected } = useAppStore();

  if (!realTimeData) {
    return (
      <aside className={cn('glass-card border-r border-white/10 p-6', className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3"></div>
        </div>
      </aside>
    );
  }

  const { statistics, hotTopics, recentPosts } = realTimeData;

  return (
    <aside className={cn(
      'glass-card border-r border-white/10 flex flex-col overflow-y-auto scrollbar-hide',
      className
    )}>
      {/* 导航菜单 */}
      <div className="p-6 border-b border-white/10">
        <NavigationMenu />
      </div>

      {/* 实时监控 */}
      <div className="p-6 space-y-4 flex-1">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <Eye className="w-5 h-5 mr-2" />
          实时监控
        </h2>

        {/* 连接状态 */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">连接状态</span>
            <div className={cn(
              'w-2 h-2 rounded-full',
              isConnected ? 'bg-green-400' : 'bg-red-400'
            )}></div>
          </div>
          <div className="text-sm font-medium text-white">
            {isConnected ? '已连接' : '连接断开'}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            {isConnected ? '数据实时更新中' : '等待重新连接'}
          </div>
        </div>

        {/* 快速统计 */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-400">今日数据</span>
            <BarChart3 className="w-4 h-4 text-blue-400" />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">总量</span>
              <span className="text-sm font-medium text-white">
                {formatNumber(statistics.total)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">增长</span>
              <span className={cn(
                'text-sm font-medium',
                statistics.growthRate >= 0 ? 'text-green-400' : 'text-red-400'
              )}>
                {statistics.growthRate >= 0 ? '+' : ''}{(statistics.growthRate * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 热点话题 */}
      <div className="space-y-4">
        <h2 className="text-sm font-semibold text-white flex items-center">
          <Hash className="w-4 h-4 mr-2" />
          热点话题
        </h2>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="space-y-3">
            {hotTopics.slice(0, 3).map((topic, index) => (
              <div key={topic.id} className="flex items-center space-x-3">
                <div className={cn(
                  'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',
                  index === 0 ? 'bg-yellow-500 text-black' :
                  index === 1 ? 'bg-gray-400 text-black' :
                  'bg-orange-500 text-white'
                )}>
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white truncate">
                    {topic.title}
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-400">
                      {formatNumber(topic.count)}
                    </span>
                    <span className={cn(
                      'text-xs',
                      topic.trend === 'up' ? 'text-green-400' :
                      topic.trend === 'down' ? 'text-red-400' :
                      'text-gray-400'
                    )}>
                      {topic.trend === 'up' ? '↗' : topic.trend === 'down' ? '↘' : '→'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {hotTopics.length > 3 && (
            <div className="mt-3 pt-3 border-t border-gray-700">
              <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                查看更多 ({hotTopics.length - 3}+)
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 最新动态 */}
      <div className="space-y-4 flex-1">
        <h2 className="text-sm font-semibold text-white flex items-center">
          <MessageSquare className="w-4 h-4 mr-2" />
          最新动态
        </h2>

        <div className="bg-gray-800/50 rounded-lg p-4 max-h-80 overflow-y-auto scrollbar-hide">
          <div className="space-y-3">
            {recentPosts.slice(0, 5).map((post) => (
              <div key={post.id} className="border-b border-gray-700 last:border-b-0 pb-3 last:pb-0">
                <div className="flex items-start space-x-2">
                  <div className={cn(
                    'w-2 h-2 rounded-full mt-2 flex-shrink-0',
                    post.sentiment === 'positive' ? 'bg-green-400' :
                    post.sentiment === 'negative' ? 'bg-red-400' : 'bg-gray-400'
                  )}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs text-gray-300 line-clamp-2 leading-relaxed">
                      {post.content}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500 truncate">
                        @{post.author}
                      </span>
                      <span className="text-xs text-gray-500 flex-shrink-0">
                        {formatRelativeTime(post.timestamp)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {recentPosts.length > 5 && (
            <div className="mt-3 pt-3 border-t border-gray-700 text-center">
              <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                查看更多动态
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="p-6 border-t border-white/10">
        <div className="grid grid-cols-3 gap-2">
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors">
            <Filter className="w-4 h-4" />
            <span className="text-xs">筛选</span>
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors">
            <Eye className="w-4 h-4" />
            <span className="text-xs">详情</span>
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-xs">预警</span>
          </button>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
