import React from 'react';
import {
  BarChart3,
  TrendingUp,
  MessageSquare,
  Hash,
  AlertTriangle,
  Eye,
  Filter
} from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { formatNumber, formatRelativeTime, getSentimentColor, cn } from '@/utils';

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const { realTimeData } = useAppStore();

  if (!realTimeData) {
    return (
      <aside className={cn('glass-card border-r border-white/10 p-6', className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3"></div>
        </div>
      </aside>
    );
  }

  const { statistics, hotTopics, recentPosts } = realTimeData;

  return (
    <aside className={cn(
      'glass-card border-r border-white/10 p-6 flex flex-col space-y-6 overflow-y-auto scrollbar-hide',
      className
    )}>
      {/* 统计概览 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          数据概览
        </h2>
        
        <div className="grid grid-cols-1 gap-3">
          <div className="bg-dark-800/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">总数据量</span>
              <TrendingUp className="w-4 h-4 text-blue-400" />
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold text-white">
                {formatNumber(statistics.total)}
              </div>
              <div className={cn(
                'text-xs flex items-center mt-1',
                statistics.growthRate >= 0 ? 'text-green-400' : 'text-red-400'
              )}>
                {statistics.growthRate >= 0 ? '↗' : '↘'} 
                {Math.abs(statistics.growthRate).toFixed(1)}%
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div className="bg-green-500/20 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-green-400">
                {formatNumber(statistics.positive)}
              </div>
              <div className="text-xs text-green-300">正面</div>
            </div>
            <div className="bg-red-500/20 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-red-400">
                {formatNumber(statistics.negative)}
              </div>
              <div className="text-xs text-red-300">负面</div>
            </div>
            <div className="bg-gray-500/20 rounded-lg p-3 text-center">
              <div className="text-lg font-bold text-gray-400">
                {formatNumber(statistics.neutral)}
              </div>
              <div className="text-xs text-gray-300">中性</div>
            </div>
          </div>
        </div>
      </div>

      {/* 热点话题 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <Hash className="w-5 h-5 mr-2" />
          热点话题
        </h2>
        
        <div className="space-y-2">
          {hotTopics.slice(0, 5).map((topic, index) => (
            <div key={topic.id} className="bg-dark-800/50 rounded-lg p-3 hover:bg-dark-700/50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                      #{index + 1}
                    </span>
                    <span className={cn(
                      'text-xs px-2 py-1 rounded',
                      topic.trend === 'up' ? 'bg-green-500/20 text-green-400' :
                      topic.trend === 'down' ? 'bg-red-500/20 text-red-400' :
                      'bg-gray-500/20 text-gray-400'
                    )}>
                      {topic.trend === 'up' ? '↗' : topic.trend === 'down' ? '↘' : '→'}
                    </span>
                  </div>
                  <h3 className="text-sm font-medium text-white mt-1 truncate">
                    {topic.title}
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-400">
                      {formatNumber(topic.count)} 条
                    </span>
                    <span className={cn('text-xs', getSentimentColor(topic.sentiment))}>
                      {topic.sentiment === 'positive' ? '正面' :
                       topic.sentiment === 'negative' ? '负面' : '中性'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 最新动态 */}
      <div className="space-y-4 flex-1">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <MessageSquare className="w-5 h-5 mr-2" />
          最新动态
        </h2>
        
        <div className="space-y-3">
          {recentPosts.slice(0, 8).map((post) => (
            <div key={post.id} className="bg-dark-800/30 rounded-lg p-3 hover:bg-dark-700/30 transition-colors">
              <div className="flex items-start space-x-3">
                <div className={cn(
                  'w-2 h-2 rounded-full mt-2 flex-shrink-0',
                  post.sentiment === 'positive' ? 'bg-green-400' :
                  post.sentiment === 'negative' ? 'bg-red-400' : 'bg-gray-400'
                )}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-300 line-clamp-2">
                    {post.content}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">
                        {post.source}
                      </span>
                      <span className="text-xs text-gray-500">
                        @{post.author}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {formatRelativeTime(post.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="space-y-3 pt-4 border-t border-white/10">
        <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors">
          <Filter className="w-4 h-4" />
          <span>数据筛选</span>
        </button>
        <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors">
          <Eye className="w-4 h-4" />
          <span>详细视图</span>
        </button>
        <button className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-dark-700/50 rounded-lg transition-colors">
          <AlertTriangle className="w-4 h-4" />
          <span>预警设置</span>
        </button>
      </div>
    </aside>
  );
};

export default Sidebar;
