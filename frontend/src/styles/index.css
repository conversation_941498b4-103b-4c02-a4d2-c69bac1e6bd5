@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-dark-900 text-white font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html, body, #root {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg;
  }
  
  .gradient-border {
    @apply relative;
  }
  
  .gradient-border::before {
    content: '';
    @apply absolute inset-0 rounded-lg p-[1px] bg-gradient-to-r from-blue-500 to-purple-500;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
  }

  .data-card {
    @apply glass-card p-6 hover:bg-white/10 transition-all duration-300;
  }

  .metric-value {
    @apply text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent;
  }

  .chart-container {
    @apply w-full h-full min-h-[300px];
  }

  .status-indicator {
    @apply w-3 h-3 rounded-full animate-pulse;
  }

  .status-online {
    @apply bg-green-500;
  }

  .status-offline {
    @apply bg-red-500;
  }

  .status-warning {
    @apply bg-yellow-500;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}

/* 图表容器样式 */
.echarts-container {
  width: 100% !important;
  height: 100% !important;
}

/* 响应式字体大小 */
@media (max-width: 1920px) {
  .metric-value {
    @apply text-2xl;
  }
}

@media (max-width: 1366px) {
  .metric-value {
    @apply text-xl;
  }
  
  .data-card {
    @apply p-4;
  }
}
