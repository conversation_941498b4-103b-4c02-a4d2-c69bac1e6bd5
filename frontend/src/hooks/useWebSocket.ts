import { useEffect, useCallback, useRef } from 'react';
import { useAppStore } from '@/stores/useAppStore';
import { wsManager } from '@/utils/websocket';
import { WebSocketMessage, RealTimeData } from '@/types';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  reconnectOnClose?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const {
    autoConnect = true,
    reconnectOnClose = true,
    maxReconnectAttempts = 5,
    reconnectDelay = 1000,
  } = options;

  const {
    setConnectionStatus,
    setRealTimeData,
    updateStatistics,
    updateHotTopics,
    updateKeywords,
    updateTimeSeries,
    updateLocations,
    addRecentPost,
    setError,
    incrementRetries,
    resetRetries,
  } = useAppStore();

  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);

  // 连接 WebSocket
  const connect = useCallback(async () => {
    try {
      await wsManager.connect();
      setConnectionStatus(true);
      resetRetries();
      reconnectAttemptsRef.current = 0;
      setError(null);
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      setConnectionStatus(false);
      setError({
        code: 'WEBSOCKET_CONNECTION_FAILED',
        message: 'WebSocket 连接失败',
        details: error,
      });
    }
  }, [setConnectionStatus, resetRetries, setError]);

  // 断开连接
  const disconnect = useCallback(() => {
    wsManager.disconnect();
    setConnectionStatus(false);
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, [setConnectionStatus]);

  // 重连逻辑
  const attemptReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      setError({
        code: 'WEBSOCKET_MAX_RECONNECT_ATTEMPTS',
        message: '达到最大重连次数',
      });
      return;
    }

    reconnectAttemptsRef.current++;
    incrementRetries();

    console.log(`Attempting to reconnect... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);

    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, reconnectDelay * reconnectAttemptsRef.current);
  }, [maxReconnectAttempts, reconnectDelay, connect, incrementRetries, setError]);

  // 发送消息
  const sendMessage = useCallback((event: string, data: any) => {
    wsManager.send(event, data);
  }, []);

  // 设置事件监听器
  useEffect(() => {
    // 连接状态监听
    const handleConnected = (connected: boolean) => {
      setConnectionStatus(connected);
      if (!connected && reconnectOnClose) {
        attemptReconnect();
      }
    };

    // 数据更新监听
    const handleDataUpdate = (message: WebSocketMessage) => {
      try {
        switch (message.type) {
          case 'update':
            if (message.data.type === 'realtime') {
              setRealTimeData(message.data.payload as RealTimeData);
            } else if (message.data.type === 'statistics') {
              updateStatistics(message.data.payload);
            } else if (message.data.type === 'hotTopics') {
              updateHotTopics(message.data.payload);
            } else if (message.data.type === 'keywords') {
              updateKeywords(message.data.payload);
            } else if (message.data.type === 'timeSeries') {
              updateTimeSeries(message.data.payload);
            } else if (message.data.type === 'locations') {
              updateLocations(message.data.payload);
            } else if (message.data.type === 'newPost') {
              addRecentPost(message.data.payload);
            }
            break;
          case 'alert':
            console.warn('WebSocket Alert:', message.data);
            break;
          case 'heartbeat':
            // 心跳消息，保持连接活跃
            break;
          default:
            console.log('Unknown WebSocket message type:', message.type);
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
        setError({
          code: 'WEBSOCKET_MESSAGE_PROCESSING_ERROR',
          message: '处理 WebSocket 消息时出错',
          details: error,
        });
      }
    };

    // 错误处理
    const handleError = (error: any) => {
      console.error('WebSocket error:', error);
      setError({
        code: 'WEBSOCKET_ERROR',
        message: 'WebSocket 连接错误',
        details: error,
      });
    };

    // 重连成功
    const handleReconnected = (attemptNumber: number) => {
      console.log('WebSocket reconnected after', attemptNumber, 'attempts');
      resetRetries();
      reconnectAttemptsRef.current = 0;
      setError(null);
    };

    // 注册事件监听器
    wsManager.on('connected', handleConnected);
    wsManager.on('dataUpdate', handleDataUpdate);
    wsManager.on('alert', handleDataUpdate);
    wsManager.on('heartbeat', handleDataUpdate);
    wsManager.on('error', handleError);
    wsManager.on('reconnected', handleReconnected);

    // 自动连接
    if (autoConnect) {
      connect();
    }

    // 清理函数
    return () => {
      wsManager.off('connected', handleConnected);
      wsManager.off('dataUpdate', handleDataUpdate);
      wsManager.off('alert', handleDataUpdate);
      wsManager.off('heartbeat', handleDataUpdate);
      wsManager.off('error', handleError);
      wsManager.off('reconnected', handleReconnected);

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [
    autoConnect,
    reconnectOnClose,
    connect,
    attemptReconnect,
    setConnectionStatus,
    setRealTimeData,
    updateStatistics,
    updateHotTopics,
    updateKeywords,
    updateTimeSeries,
    updateLocations,
    addRecentPost,
    setError,
    resetRetries,
  ]);

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: wsManager.isConnected,
    reconnectCount: wsManager.reconnectCount,
  };
};
