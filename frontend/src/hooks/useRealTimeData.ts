import { useEffect, useCallback, useRef } from 'react';
import { useAppStore } from '@/stores/useAppStore';
import { sentimentAPI, systemAPI } from '@/utils/api';
import { RealTimeData, SystemStatus } from '@/types';

interface UseRealTimeDataOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onError?: (error: any) => void;
  onSuccess?: (data: RealTimeData) => void;
}

export const useRealTimeData = (options: UseRealTimeDataOptions = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 seconds
    onError,
    onSuccess,
  } = options;

  const {
    selectedTimeRange,
    setRealTimeData,
    setSystemStatus,
    setLoading,
    setError,
    dashboardConfig,
  } = useAppStore();

  const intervalRef = useRef<NodeJS.Timeout>();
  const isLoadingRef = useRef(false);

  // 获取实时数据
  const fetchRealTimeData = useCallback(async () => {
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      // 并行获取所有数据
      const [
        statistics,
        hotTopics,
        keywords,
        timeSeries,
        locations,
        recentPosts,
      ] = await Promise.all([
        sentimentAPI.getStatistics(selectedTimeRange),
        sentimentAPI.getHotTopics(10),
        sentimentAPI.getKeywords(50),
        sentimentAPI.getTimeSeries(selectedTimeRange),
        sentimentAPI.getLocationData(),
        sentimentAPI.getRecentPosts(20),
      ]);

      const realTimeData: RealTimeData = {
        statistics,
        hotTopics,
        keywords,
        timeSeries,
        locations,
        recentPosts,
      };

      setRealTimeData(realTimeData);
      onSuccess?.(realTimeData);

    } catch (error) {
      console.error('Failed to fetch real-time data:', error);
      const errorObj = {
        code: 'FETCH_REALTIME_DATA_ERROR',
        message: '获取实时数据失败',
        details: error,
      };
      setError(errorObj);
      onError?.(error);
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [selectedTimeRange, setRealTimeData, setLoading, setError, onSuccess, onError]);

  // 获取系统状态
  const fetchSystemStatus = useCallback(async () => {
    try {
      const [status, performance] = await Promise.all([
        systemAPI.getStatus(),
        systemAPI.getPerformance(),
      ]);

      const systemStatus: SystemStatus = {
        ...status,
        performance,
        lastUpdate: new Date().toISOString(),
      };

      setSystemStatus(systemStatus);
    } catch (error) {
      console.error('Failed to fetch system status:', error);
    }
  }, [setSystemStatus]);

  // 手动刷新数据
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchRealTimeData(),
      fetchSystemStatus(),
    ]);
  }, [fetchRealTimeData, fetchSystemStatus]);

  // 启动自动刷新
  const startAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    const interval = dashboardConfig.autoRefresh ? dashboardConfig.refreshInterval : refreshInterval;
    
    intervalRef.current = setInterval(() => {
      refreshData();
    }, interval);
  }, [refreshData, refreshInterval, dashboardConfig.autoRefresh, dashboardConfig.refreshInterval]);

  // 停止自动刷新
  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
  }, []);

  // 初始化数据获取
  useEffect(() => {
    // 立即获取一次数据
    refreshData();

    // 如果启用自动刷新，则开始定时器
    if (autoRefresh && dashboardConfig.autoRefresh) {
      startAutoRefresh();
    }

    return () => {
      stopAutoRefresh();
    };
  }, [selectedTimeRange, autoRefresh, dashboardConfig.autoRefresh]);

  // 监听配置变化
  useEffect(() => {
    if (dashboardConfig.autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }, [dashboardConfig.autoRefresh, dashboardConfig.refreshInterval, startAutoRefresh, stopAutoRefresh]);

  return {
    fetchRealTimeData,
    fetchSystemStatus,
    refreshData,
    startAutoRefresh,
    stopAutoRefresh,
  };
};
