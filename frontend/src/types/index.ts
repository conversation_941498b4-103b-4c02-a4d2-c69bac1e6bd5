// 基础数据类型
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 舆情数据类型
export interface SentimentData extends BaseEntity {
  content: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  score: number;
  source: 'weibo' | 'zhihu' | 'news' | 'other';
  author: string;
  platform: string;
  url?: string;
  tags: string[];
  location?: string;
  timestamp: string;
}

// 统计数据类型
export interface StatisticsData {
  total: number;
  positive: number;
  negative: number;
  neutral: number;
  growth: number;
  growthRate: number;
}

// 热点话题类型
export interface HotTopic extends BaseEntity {
  title: string;
  count: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
}

// 关键词数据类型
export interface KeywordData {
  name: string;
  value: number;
  sentiment: 'positive' | 'negative' | 'neutral';
}

// 时间序列数据类型
export interface TimeSeriesData {
  timestamp: string;
  value: number;
  positive?: number;
  negative?: number;
  neutral?: number;
}

// 地理位置数据类型
export interface LocationData {
  name: string;
  value: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  coordinates: [number, number];
}

// 实时数据类型
export interface RealTimeData {
  statistics: StatisticsData;
  hotTopics: HotTopic[];
  keywords: KeywordData[];
  timeSeries: TimeSeriesData[];
  locations: LocationData[];
  recentPosts: SentimentData[];
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'update' | 'alert' | 'heartbeat';
  data: any;
  timestamp: string;
}

// 系统状态类型
export interface SystemStatus {
  isOnline: boolean;
  lastUpdate: string;
  dataSource: {
    weibo: boolean;
    zhihu: boolean;
    news: boolean;
  };
  performance: {
    cpu: number;
    memory: number;
    network: number;
  };
}

// 图表配置类型
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'wordcloud' | 'map';
  title: string;
  data: any[];
  options?: any;
}

// 仪表板配置类型
export interface DashboardConfig {
  layout: 'grid' | 'flex';
  refreshInterval: number;
  autoRefresh: boolean;
  theme: 'dark' | 'light';
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}
