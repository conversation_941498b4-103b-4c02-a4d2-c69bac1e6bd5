import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Layout } from '@/components';
import Dashboard from '@/pages/Dashboard';
import { useAppStore } from '@/stores/useAppStore';
import { cn } from '@/utils';

const App: React.FC = () => {
  const { dashboardConfig } = useAppStore();

  return (
    <Router>
      <div className={cn(
        'min-h-screen transition-colors duration-300',
        dashboardConfig.theme === 'dark' ? 'dark' : ''
      )}>
        <Layout>
          <AnimatePresence mode="wait">
            <Routes>
              <Route
                path="/"
                element={
                  <motion.div
                    key="dashboard"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Dashboard />
                  </motion.div>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <motion.div
                    key="dashboard"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Dashboard />
                  </motion.div>
                }
              />
            </Routes>
          </AnimatePresence>
        </Layout>
      </div>
    </Router>
  );
};

export default App;
