import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('Response Error:', error);
    
    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权，可能需要重新登录
          console.error('Unauthorized access');
          break;
        case 403:
          // 禁止访问
          console.error('Forbidden access');
          break;
        case 404:
          // 资源不存在
          console.error('Resource not found');
          break;
        case 500:
          // 服务器错误
          console.error('Internal server error');
          break;
        default:
          console.error('API Error:', data?.message || 'Unknown error');
      }
    } else if (error.request) {
      // 网络错误
      console.error('Network error');
    } else {
      // 其他错误
      console.error('Error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// API 方法封装
export const apiClient = {
  // GET 请求
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.get<ApiResponse<T>>(url, config);
    return response.data.data;
  },

  // POST 请求
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.post<ApiResponse<T>>(url, data, config);
    return response.data.data;
  },

  // PUT 请求
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.put<ApiResponse<T>>(url, data, config);
    return response.data.data;
  },

  // DELETE 请求
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.delete<ApiResponse<T>>(url, config);
    return response.data.data;
  },

  // PATCH 请求
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.patch<ApiResponse<T>>(url, data, config);
    return response.data.data;
  },
};

// 导入模拟数据（仅在开发环境使用）
import {
  generateStatistics,
  generateHotTopics,
  generateKeywords,
  generateTimeSeries,
  generateLocationData,
  generateRecentPosts,
  generateSystemStatus
} from './mockData';

// 检查是否为开发环境
const isDevelopment = import.meta.env.DEV;

// 模拟 API 延迟
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 具体的 API 接口
export const sentimentAPI = {
  // 获取实时数据
  getRealTimeData: async (timeRange: string = '24h') => {
    if (isDevelopment) {
      await mockDelay();
      return {
        statistics: generateStatistics(),
        hotTopics: generateHotTopics(10),
        keywords: generateKeywords(50),
        timeSeries: generateTimeSeries(24),
        locations: generateLocationData(),
        recentPosts: generateRecentPosts(20),
      };
    }
    return apiClient.get(`/sentiment/realtime?range=${timeRange}`);
  },

  // 获取统计数据
  getStatistics: async (timeRange: string = '24h') => {
    if (isDevelopment) {
      await mockDelay(200);
      return generateStatistics();
    }
    return apiClient.get(`/sentiment/statistics?range=${timeRange}`);
  },

  // 获取热点话题
  getHotTopics: async (limit: number = 10) => {
    if (isDevelopment) {
      await mockDelay(300);
      return generateHotTopics(limit);
    }
    return apiClient.get(`/sentiment/hot-topics?limit=${limit}`);
  },

  // 获取关键词
  getKeywords: async (limit: number = 50) => {
    if (isDevelopment) {
      await mockDelay(250);
      return generateKeywords(limit);
    }
    return apiClient.get(`/sentiment/keywords?limit=${limit}`);
  },

  // 获取时间序列数据
  getTimeSeries: async (timeRange: string = '24h') => {
    if (isDevelopment) {
      await mockDelay(400);
      const hours = timeRange === '1h' ? 1 : timeRange === '6h' ? 6 : timeRange === '7d' ? 168 : 24;
      return generateTimeSeries(hours);
    }
    return apiClient.get(`/sentiment/time-series?range=${timeRange}`);
  },

  // 获取地理位置数据
  getLocationData: async () => {
    if (isDevelopment) {
      await mockDelay(350);
      return generateLocationData();
    }
    return apiClient.get('/sentiment/locations');
  },

  // 获取最新帖子
  getRecentPosts: async (limit: number = 20) => {
    if (isDevelopment) {
      await mockDelay(200);
      return generateRecentPosts(limit);
    }
    return apiClient.get(`/sentiment/recent-posts?limit=${limit}`);
  },

  // 搜索相关内容
  search: async (query: string, filters?: any) => {
    if (isDevelopment) {
      await mockDelay(600);
      return generateRecentPosts(10);
    }
    return apiClient.post('/sentiment/search', { query, filters });
  },
};

export const systemAPI = {
  // 获取系统状态
  getStatus: async () => {
    if (isDevelopment) {
      await mockDelay(150);
      return generateSystemStatus();
    }
    return apiClient.get('/system/status');
  },

  // 获取性能指标
  getPerformance: async () => {
    if (isDevelopment) {
      await mockDelay(100);
      return {
        cpu: Math.random() * 80 + 10,
        memory: Math.random() * 70 + 20,
        network: Math.random() * 50 + 5,
      };
    }
    return apiClient.get('/system/performance');
  },

  // 健康检查
  healthCheck: async () => {
    if (isDevelopment) {
      await mockDelay(50);
      return { status: 'ok', timestamp: new Date().toISOString() };
    }
    return apiClient.get('/system/health');
  },
};

export default api;
