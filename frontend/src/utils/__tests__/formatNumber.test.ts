import { formatNumber, formatPercentage } from '../index';

describe('formatNumber', () => {
  // 正常数字测试
  test('formats regular numbers correctly', () => {
    expect(formatNumber(123)).toBe('123');
    expect(formatNumber(0)).toBe('0');
    expect(formatNumber(42)).toBe('42');
  });

  // 千位数测试
  test('formats thousands correctly', () => {
    expect(formatNumber(1000)).toBe('1.0K');
    expect(formatNumber(1500)).toBe('1.5K');
    expect(formatNumber(12345)).toBe('12.3K');
    expect(formatNumber(999999)).toBe('1000.0K');
  });

  // 百万位数测试
  test('formats millions correctly', () => {
    expect(formatNumber(1000000)).toBe('1.0M');
    expect(formatNumber(1500000)).toBe('1.5M');
    expect(formatNumber(12345678)).toBe('12.3M');
  });

  // 空值测试
  test('handles null and undefined values', () => {
    expect(formatNumber(null)).toBe('0');
    expect(formatNumber(undefined)).toBe('0');
  });

  // NaN 测试
  test('handles NaN values', () => {
    expect(formatNumber(NaN)).toBe('0');
    expect(formatNumber(Number('invalid'))).toBe('0');
  });

  // 字符串数字测试
  test('handles string numbers', () => {
    expect(formatNumber('123' as any)).toBe('123');
    expect(formatNumber('1500' as any)).toBe('1.5K');
    expect(formatNumber('1500000' as any)).toBe('1.5M');
  });

  // 负数测试
  test('handles negative numbers', () => {
    expect(formatNumber(-123)).toBe('-123');
    expect(formatNumber(-1500)).toBe('-1.5K');
    expect(formatNumber(-1500000)).toBe('-1.5M');
  });

  // 小数测试
  test('handles decimal numbers', () => {
    expect(formatNumber(123.45)).toBe('123');
    expect(formatNumber(1234.56)).toBe('1.2K');
    expect(formatNumber(1234567.89)).toBe('1.2M');
  });
});

describe('formatPercentage', () => {
  // 正常百分比测试
  test('formats regular percentages correctly', () => {
    expect(formatPercentage(0.5)).toBe('50.0%');
    expect(formatPercentage(0.123)).toBe('12.3%');
    expect(formatPercentage(1)).toBe('100.0%');
    expect(formatPercentage(0)).toBe('0.0%');
  });

  // 空值测试
  test('handles null and undefined values', () => {
    expect(formatPercentage(null)).toBe('0.0%');
    expect(formatPercentage(undefined)).toBe('0.0%');
  });

  // NaN 测试
  test('handles NaN values', () => {
    expect(formatPercentage(NaN)).toBe('0.0%');
    expect(formatPercentage(Number('invalid'))).toBe('0.0%');
  });

  // 字符串数字测试
  test('handles string numbers', () => {
    expect(formatPercentage('0.5' as any)).toBe('50.0%');
    expect(formatPercentage('0.123' as any)).toBe('12.3%');
  });

  // 负数测试
  test('handles negative numbers', () => {
    expect(formatPercentage(-0.5)).toBe('-50.0%');
    expect(formatPercentage(-0.123)).toBe('-12.3%');
  });

  // 大于1的数值测试
  test('handles values greater than 1', () => {
    expect(formatPercentage(1.5)).toBe('150.0%');
    expect(formatPercentage(2.345)).toBe('234.5%');
  });
});
