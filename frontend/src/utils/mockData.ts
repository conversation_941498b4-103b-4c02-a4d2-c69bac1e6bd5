import { 
  RealTimeData, 
  StatisticsData, 
  HotTopic, 
  KeywordData, 
  TimeSeriesData, 
  LocationData, 
  SentimentData,
  SystemStatus
} from '@/types';
// 生成 ID 的本地函数
const generateId = (): string => {
  return Math.random().toString(36).substring(2, 11);
};

// 生成随机数
const random = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;

// 生成随机情感
const randomSentiment = (): 'positive' | 'negative' | 'neutral' => {
  const sentiments = ['positive', 'negative', 'neutral'] as const;
  return sentiments[random(0, 2)];
};

// 生成随机趋势
const randomTrend = (): 'up' | 'down' | 'stable' => {
  const trends = ['up', 'down', 'stable'] as const;
  return trends[random(0, 2)];
};

// 生成统计数据
export const generateStatistics = (): StatisticsData => {
  const total = random(10000, 50000);
  const positive = random(Math.floor(total * 0.2), Math.floor(total * 0.5));
  const negative = random(Math.floor(total * 0.1), Math.floor(total * 0.3));
  const neutral = total - positive - negative;
  
  return {
    total,
    positive,
    negative,
    neutral,
    growth: random(-1000, 2000),
    growthRate: random(-10, 25),
  };
};

// 生成热点话题
export const generateHotTopics = (count: number = 10): HotTopic[] => {
  const topics = [
    '人工智能发展趋势',
    '新能源汽车市场',
    '疫情防控政策',
    '教育改革方案',
    '房地产市场调控',
    '科技创新发展',
    '环保政策实施',
    '医疗健康服务',
    '数字经济建设',
    '文化产业发展',
    '体育赛事举办',
    '旅游业复苏',
    '金融市场波动',
    '就业形势分析',
    '社会保障制度',
  ];

  return Array.from({ length: count }, (_, _index) => ({
    id: generateId(),
    title: topics[random(0, topics.length - 1)],
    count: random(100, 5000),
    sentiment: randomSentiment(),
    keywords: ['关键词1', '关键词2', '关键词3'],
    trend: randomTrend(),
    trendValue: random(-50, 100),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }));
};

// 生成关键词数据
export const generateKeywords = (count: number = 50): KeywordData[] => {
  const keywords = [
    '人工智能', '机器学习', '深度学习', '大数据', '云计算',
    '区块链', '物联网', '5G', '新能源', '电动汽车',
    '疫情', '防控', '疫苗', '健康', '医疗',
    '教育', '改革', '学校', '学生', '老师',
    '房价', '楼市', '调控', '政策', '投资',
    '创新', '科技', '研发', '技术', '专利',
    '环保', '绿色', '节能', '减排', '生态',
    '数字化', '智能化', '自动化', '信息化', '网络',
    '文化', '艺术', '娱乐', '影视', '音乐',
    '体育', '运动', '健身', '比赛', '奥运',
  ];

  return Array.from({ length: count }, () => ({
    name: keywords[random(0, keywords.length - 1)],
    value: random(10, 1000),
    sentiment: randomSentiment(),
  }));
};

// 生成时间序列数据
export const generateTimeSeries = (hours: number = 24): TimeSeriesData[] => {
  const now = new Date();
  return Array.from({ length: hours }, (_, index) => {
    const timestamp = new Date(now.getTime() - (hours - index - 1) * 60 * 60 * 1000);
    const total = random(100, 1000);
    const positive = random(Math.floor(total * 0.2), Math.floor(total * 0.5));
    const negative = random(Math.floor(total * 0.1), Math.floor(total * 0.3));
    const neutral = total - positive - negative;
    
    return {
      timestamp: timestamp.toISOString(),
      value: total,
      positive,
      negative,
      neutral,
    };
  });
};

// 生成地理位置数据
export const generateLocationData = (): LocationData[] => {
  const locations = [
    { name: '北京', coordinates: [116.4074, 39.9042] as [number, number] },
    { name: '上海', coordinates: [121.4737, 31.2304] as [number, number] },
    { name: '广州', coordinates: [113.2644, 23.1291] as [number, number] },
    { name: '深圳', coordinates: [114.0579, 22.5431] as [number, number] },
    { name: '杭州', coordinates: [120.1551, 30.2741] as [number, number] },
    { name: '南京', coordinates: [118.7969, 32.0603] as [number, number] },
    { name: '武汉', coordinates: [114.3054, 30.5931] as [number, number] },
    { name: '成都', coordinates: [104.0665, 30.5723] as [number, number] },
    { name: '西安', coordinates: [108.9402, 34.3416] as [number, number] },
    { name: '重庆', coordinates: [106.5516, 29.5630] as [number, number] },
  ];

  return locations.map(location => ({
    name: location.name,
    value: random(50, 500),
    sentiment: randomSentiment(),
    coordinates: location.coordinates,
  }));
};

// 生成最新帖子
export const generateRecentPosts = (count: number = 20): SentimentData[] => {
  const contents = [
    '今天的天气真不错，心情也很好！',
    '新的政策出台了，希望能带来积极的变化。',
    '这个产品的质量有待提高，用户体验不太好。',
    '科技发展真是日新月异，令人惊叹。',
    '教育改革需要更多的时间和耐心。',
    '环保意识越来越重要了。',
    '医疗服务质量有了明显提升。',
    '数字化转型是大势所趋。',
    '文化产业发展前景广阔。',
    '体育运动对健康很重要。',
  ];

  const sources = ['weibo', 'zhihu', 'news', 'other'] as const;
  const authors = ['用户A', '用户B', '用户C', '用户D', '用户E'];

  return Array.from({ length: count }, () => ({
    id: generateId(),
    content: contents[random(0, contents.length - 1)],
    sentiment: randomSentiment(),
    score: random(0, 100) / 100,
    source: sources[random(0, sources.length - 1)],
    author: authors[random(0, authors.length - 1)],
    platform: '微博',
    tags: ['标签1', '标签2'],
    timestamp: new Date(Date.now() - random(0, 24 * 60 * 60 * 1000)).toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }));
};

// 生成系统状态
export const generateSystemStatus = (): SystemStatus => ({
  isOnline: true,
  lastUpdate: new Date().toISOString(),
  dataSource: {
    weibo: Math.random() > 0.1,
    zhihu: Math.random() > 0.1,
    news: Math.random() > 0.1,
  },
  performance: {
    cpu: random(20, 80),
    memory: random(30, 70),
    network: random(10, 50),
  },
});

// 生成完整的实时数据
export const generateRealTimeData = (): RealTimeData => ({
  statistics: generateStatistics(),
  hotTopics: generateHotTopics(10),
  keywords: generateKeywords(50),
  timeSeries: generateTimeSeries(24),
  locations: generateLocationData(),
  recentPosts: generateRecentPosts(20),
});
