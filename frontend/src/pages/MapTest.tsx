import React from 'react';
import LocationHeatMap from '@/components/charts/LocationHeatMap';
import SimpleMapTest from '@/components/charts/SimpleMapTest';
import { LocationData } from '@/types';

// 测试数据
const testLocationData: LocationData[] = [
  {
    name: '北京',
    coordinates: [116.4074, 39.9042],
    value: 1500,
    sentiment: 'positive'
  },
  {
    name: '上海',
    coordinates: [121.4737, 31.2304],
    value: 1200,
    sentiment: 'positive'
  },
  {
    name: '广州',
    coordinates: [113.2644, 23.1291],
    value: 800,
    sentiment: 'neutral'
  },
  {
    name: '深圳',
    coordinates: [114.0579, 22.5431],
    value: 900,
    sentiment: 'positive'
  },
  {
    name: '杭州',
    coordinates: [120.1551, 30.2741],
    value: 600,
    sentiment: 'positive'
  },
  {
    name: '成都',
    coordinates: [104.0665, 30.5723],
    value: 700,
    sentiment: 'neutral'
  },
  {
    name: '西安',
    coordinates: [108.9398, 34.3416],
    value: 400,
    sentiment: 'negative'
  },
  {
    name: '武汉',
    coordinates: [114.3054, 30.5931],
    value: 500,
    sentiment: 'neutral'
  },
  {
    name: '重庆',
    coordinates: [106.5516, 29.5630],
    value: 550,
    sentiment: 'positive'
  },
  {
    name: '天津',
    coordinates: [117.2008, 39.0842],
    value: 350,
    sentiment: 'neutral'
  }
];

const MapTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          中国地图数据测试
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-gray-800 rounded-lg p-6 shadow-xl">
            <h2 className="text-xl font-semibold text-white mb-4">简单地图测试</h2>
            <SimpleMapTest />
          </div>

          <div className="bg-gray-800 rounded-lg p-6 shadow-xl">
            <h2 className="text-xl font-semibold text-white mb-4">完整热力图测试</h2>
            <LocationHeatMap
              data={testLocationData}
              title="微博地理位置分布测试"
              height={500}
            />
          </div>
        </div>
        
        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">测试说明</h2>
          <div className="text-gray-300 space-y-2">
            <p>• 此页面用于测试中国地图数据的加载和显示效果</p>
            <p>• 地图数据加载优先级：本地详细数据 → 在线详细数据 → 本地简化数据 → 内置基础数据</p>
            <p>• 请打开浏览器开发者工具查看控制台日志，了解地图数据加载情况</p>
            <p>• 地图支持缩放和拖拽操作</p>
            <p>• 鼠标悬停可查看详细信息</p>
          </div>
          
          <div className="mt-4">
            <h3 className="text-lg font-medium text-white mb-2">测试数据</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-gray-400">
              {testLocationData.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span>{item.name}</span>
                  <span className={`font-medium ${
                    item.sentiment === 'positive' ? 'text-green-400' :
                    item.sentiment === 'negative' ? 'text-red-400' : 'text-gray-400'
                  }`}>
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapTest;
