import React from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { useWebSocket, useRealTimeData, useAutoRefresh } from '@/hooks';
import {
  TimeSeries<PERSON>hart,
  Sentiment<PERSON>ie<PERSON>hart,
  WordCloudChart,
  HotTopicsChart,
  LocationHeatMap,
} from '@/components/charts';
import { MetricCard, AlertPanel, LoadingSpinner } from '@/components/ui';

const Dashboard: React.FC = () => {
  const { realTimeData, isLoading, error } = useAppStore();

  // 初始化 WebSocket 连接
  useWebSocket({
    autoConnect: true,
    reconnectOnClose: true,
    maxReconnectAttempts: 5,
  });

  // 初始化数据获取
  const { refreshData } = useRealTimeData({
    autoRefresh: true,
    refreshInterval: 30000,
  });

  // 自动刷新
  useAutoRefresh({
    onRefresh: refreshData,
    interval: 30000,
    enabled: true,
    immediate: false,
  });

  if (isLoading && !realTimeData) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error && !realTimeData) {
    return (
      <div className="flex items-center justify-center h-full">
        <AlertPanel
          type="error"
          title="数据加载失败"
          message={error.message}
          action={{
            label: '重试',
            onClick: refreshData,
          }}
        />
      </div>
    );
  }

  if (!realTimeData) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-gray-400 mb-4">暂无数据</div>
          <button
            onClick={refreshData}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            加载数据
          </button>
        </div>
      </div>
    );
  }

  const { statistics, hotTopics, keywords, timeSeries, locations } = realTimeData;

  return (
    <div className="space-y-6">
      {/* 顶部指标卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <MetricCard
          title="总数据量"
          value={statistics.total}
          change={statistics.growthRate}
          icon="BarChart3"
          color="blue"
        />
        <MetricCard
          title="正面情感"
          value={statistics.positive}
          change={((statistics.positive / statistics.total) * 100) - 33.33}
          icon="TrendingUp"
          color="green"
        />
        <MetricCard
          title="负面情感"
          value={statistics.negative}
          change={((statistics.negative / statistics.total) * 100) - 33.33}
          icon="TrendingDown"
          color="red"
        />
        <MetricCard
          title="中性情感"
          value={statistics.neutral}
          change={((statistics.neutral / statistics.total) * 100) - 33.33}
          icon="Minus"
          color="gray"
        />
      </motion.div>

      {/* 主要图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 时间序列图 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="data-card"
        >
          <TimeSeriesChart
            data={timeSeries}
            title="情感趋势分析"
            height={350}
          />
        </motion.div>

        {/* 情感分布饼图 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="data-card"
        >
          <SentimentPieChart
            data={statistics}
            title="情感分布"
            height={350}
          />
        </motion.div>
      </div>

      {/* 第二行图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 热点话题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="data-card"
        >
          <HotTopicsChart
            data={hotTopics}
            title="热点话题排行"
            height={400}
            maxTopics={8}
            orientation="horizontal"
          />
        </motion.div>

        {/* 关键词词云 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="data-card"
        >
          <WordCloudChart
            data={keywords}
            title="关键词分析"
            height={400}
            maxWords={50}
          />
        </motion.div>

        {/* 地理分布 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="data-card"
        >
          <LocationHeatMap
            data={locations}
            title="地理分布"
            height={400}
          />
        </motion.div>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-4 right-4 z-50"
        >
          <AlertPanel
            type="warning"
            title="数据更新异常"
            message={error.message}
            dismissible
            onDismiss={() => {
              // 清除错误状态的逻辑可以在这里实现
            }}
          />
        </motion.div>
      )}
    </div>
  );
};

export default Dashboard;
