import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  Filter,
  Download,
  Refresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  MapPin,
  MessageSquare
} from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { useRealTimeData } from '@/hooks';
import {
  TimeSeriesChart,
  SentimentPieChart,
  WordCloudChart,
  HotTopicsChart,
  LocationHeatMap,
} from '@/components/charts';
import { MetricCard, LoadingSpinner } from '@/components/ui';
import { formatNumber, formatPercentage, cn } from '@/utils';

const Analytics: React.FC = () => {
  const { realTimeData, selectedTimeRange, setSelectedTimeRange } = useAppStore();
  const { loading, error } = useRealTimeData();
  const [activeTab, setActiveTab] = useState<'overview' | 'sentiment' | 'topics' | 'location'>('overview');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!realTimeData) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="text-gray-400 mb-2">暂无数据</div>
          <button className="text-blue-400 hover:text-blue-300">
            刷新数据
          </button>
        </div>
      </div>
    );
  }

  const { statistics, hotTopics, keywords, timeSeries, locations } = realTimeData;

  const tabs = [
    { id: 'overview', label: '综合分析', icon: BarChart3 },
    { id: 'sentiment', label: '情感分析', icon: PieChart },
    { id: 'topics', label: '话题分析', icon: MessageSquare },
    { id: 'location', label: '地域分析', icon: MapPin },
  ] as const;

  const timeRangeOptions = [
    { value: '1h', label: '1小时' },
    { value: '6h', label: '6小时' },
    { value: '24h', label: '24小时' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
  ] as const;

  return (
    <div className="space-y-6">
      {/* 页面标题和控制区域 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">数据分析</h1>
          <p className="text-gray-400">深入分析舆情数据趋势和模式</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* 时间范围选择 */}
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500"
            >
              {timeRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 操作按钮 */}
          <button className="flex items-center space-x-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors">
            <Filter className="w-4 h-4" />
            <span>筛选</span>
          </button>

          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <Download className="w-4 h-4" />
            <span>导出</span>
          </button>

          <button className="p-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </motion.div>

      {/* 关键指标卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6"
      >
        <MetricCard
          title="总数据量"
          value={statistics.total}
          change={statistics.growthRate}
          icon="BarChart3"
          color="blue"
        />
        <MetricCard
          title="正面情感"
          value={statistics.positive}
          change={((statistics.positive / statistics.total) * 100) - 33.33}
          icon="TrendingUp"
          color="green"
        />
        <MetricCard
          title="负面情感"
          value={statistics.negative}
          change={((statistics.negative / statistics.total) * 100) - 33.33}
          icon="TrendingUp"
          color="red"
        />
        <MetricCard
          title="中性情感"
          value={statistics.neutral}
          change={((statistics.neutral / statistics.total) * 100) - 33.33}
          icon="TrendingUp"
          color="gray"
        />
        <MetricCard
          title="增长率"
          value={statistics.growthRate * 100}
          change={statistics.growthRate > 0 ? 5.2 : -2.1}
          icon="TrendingUp"
          color="purple"
        />
      </motion.div>

      {/* 分析标签页 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="bg-gray-800 rounded-lg p-6"
      >
        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6 bg-gray-900 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200',
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                )}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* 标签页内容 */}
        <div className="min-h-[600px]">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* 第一行：时间序列图表 */}
              <div className="bg-gray-900 rounded-lg p-4">
                <TimeSeriesChart
                  data={timeSeries}
                  title="情感趋势分析"
                  height={350}
                />
              </div>

              {/* 第二行：左右分栏布局 */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 左侧：情感分布饼图 */}
                <div className="lg:col-span-1">
                  <div className="bg-gray-900 rounded-lg p-4 h-full">
                    <SentimentPieChart
                      data={statistics}
                      title="情感分布"
                      height={320}
                    />
                  </div>
                </div>

                {/* 右侧：数据概览和关键词云 */}
                <div className="lg:col-span-2 space-y-6">
                  {/* 数据概览卡片 */}
                  <div className="bg-gray-900 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-white mb-4">数据概览</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">
                          {formatPercentage(statistics.positive / statistics.total)}
                        </div>
                        <div className="text-sm text-gray-400">正面情感</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-400">
                          {formatPercentage(statistics.negative / statistics.total)}
                        </div>
                        <div className="text-sm text-gray-400">负面情感</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">
                          {formatNumber(statistics.growth)}
                        </div>
                        <div className="text-sm text-gray-400">新增数据</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">
                          {hotTopics.length}
                        </div>
                        <div className="text-sm text-gray-400">热点话题</div>
                      </div>
                    </div>
                  </div>

                  {/* 关键词云 */}
                  <div className="bg-gray-900 rounded-lg p-4">
                    <WordCloudChart
                      data={keywords}
                      title="关键词云"
                      height={200}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'sentiment' && (
            <div className="space-y-6">
              {/* 情感趋势分析 */}
              <div className="bg-gray-900 rounded-lg p-4">
                <TimeSeriesChart
                  data={timeSeries}
                  title="情感趋势详细分析"
                  height={400}
                />
              </div>

              {/* 情感分布和统计 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-900 rounded-lg p-4">
                  <SentimentPieChart
                    data={statistics}
                    title="情感分布详情"
                    height={350}
                  />
                </div>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-4">情感统计详情</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span className="text-gray-300">正面情感</span>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-400">
                          {formatNumber(statistics.positive)}
                        </div>
                        <div className="text-sm text-gray-400">
                          {formatPercentage(statistics.positive / statistics.total)}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                        <span className="text-gray-300">负面情感</span>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-red-400">
                          {formatNumber(statistics.negative)}
                        </div>
                        <div className="text-sm text-gray-400">
                          {formatPercentage(statistics.negative / statistics.total)}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span className="text-gray-300">中性情感</span>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-400">
                          {formatNumber(statistics.neutral)}
                        </div>
                        <div className="text-sm text-gray-400">
                          {formatPercentage(statistics.neutral / statistics.total)}
                        </div>
                      </div>
                    </div>
                    <div className="border-t border-gray-700 pt-3 mt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300 font-medium">总计</span>
                        <div className="text-right">
                          <div className="text-xl font-bold text-white">
                            {formatNumber(statistics.total)}
                          </div>
                          <div className="text-sm text-gray-400">数据条目</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'topics' && (
            <div className="space-y-6">
              {/* 热点话题和关键词 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-900 rounded-lg p-4">
                  <HotTopicsChart
                    data={hotTopics}
                    title="热点话题排行"
                    height={450}
                    maxTopics={15}
                    orientation="horizontal"
                  />
                </div>
                <div className="bg-gray-900 rounded-lg p-4">
                  <WordCloudChart
                    data={keywords}
                    title="话题关键词"
                    height={450}
                    maxWords={100}
                  />
                </div>
              </div>

              {/* 话题统计信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">话题统计</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">总话题数</span>
                      <span className="text-blue-400 font-medium">
                        {hotTopics.length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">热门话题</span>
                      <span className="text-green-400 font-medium">
                        {hotTopics.filter(topic => topic.count > 100).length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">关键词数</span>
                      <span className="text-purple-400 font-medium">
                        {keywords.length}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">热度排行</h4>
                  <div className="space-y-3">
                    {hotTopics.slice(0, 3).map((topic, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={cn(
                            'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',
                            index === 0 ? 'bg-yellow-500 text-black' :
                            index === 1 ? 'bg-gray-400 text-black' :
                            'bg-orange-500 text-white'
                          )}>
                            {index + 1}
                          </div>
                          <span className="text-gray-300 text-sm truncate">
                            {topic.topic}
                          </span>
                        </div>
                        <span className="text-blue-400 font-medium text-sm">
                          {formatNumber(topic.count)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">趋势分析</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">上升趋势</span>
                      <span className="text-green-400 font-medium">
                        {Math.floor(hotTopics.length * 0.3)} 个
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">下降趋势</span>
                      <span className="text-red-400 font-medium">
                        {Math.floor(hotTopics.length * 0.2)} 个
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">稳定趋势</span>
                      <span className="text-gray-400 font-medium">
                        {hotTopics.length - Math.floor(hotTopics.length * 0.5)} 个
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'location' && (
            <div className="space-y-6">
              {/* 地域分布热力图 */}
              <div className="bg-gray-900 rounded-lg p-4">
                <LocationHeatMap
                  data={locations}
                  title="地域分布热力图"
                  height={500}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">地域统计</h4>
                  <div className="space-y-3">
                    {locations.slice(0, 5).map((location, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-300">{location.name}</span>
                        <span className="text-blue-400 font-medium">
                          {formatNumber(location.value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">情感分布</h4>
                  <div className="space-y-3">
                    {['positive', 'negative', 'neutral'].map((sentiment) => {
                      const count = locations.filter(l => l.sentiment === sentiment).length;
                      const color = sentiment === 'positive' ? 'text-green-400' : 
                                   sentiment === 'negative' ? 'text-red-400' : 'text-gray-400';
                      const label = sentiment === 'positive' ? '正面' : 
                                   sentiment === 'negative' ? '负面' : '中性';
                      return (
                        <div key={sentiment} className="flex justify-between items-center">
                          <span className="text-gray-300">{label}</span>
                          <span className={`font-medium ${color}`}>
                            {count} 个地区
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-4">覆盖范围</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">总地区数</span>
                      <span className="text-blue-400 font-medium">
                        {locations.length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">最高数值</span>
                      <span className="text-green-400 font-medium">
                        {formatNumber(Math.max(...locations.map(l => l.value)))}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">平均数值</span>
                      <span className="text-yellow-400 font-medium">
                        {formatNumber(locations.reduce((sum, l) => sum + l.value, 0) / locations.length)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-red-900/20 border border-red-500/30 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span className="text-red-400">数据加载异常: {error.message}</span>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Analytics;
