import React from 'react';
import { motion } from 'framer-motion';
import { Maximize, Minimize, Keyboard, Monitor } from 'lucide-react';
import { useFullscreen } from '@/hooks/useFullscreen';
import { useFullscreenShortcuts } from '@/hooks/useKeyboardShortcuts';
import LocationHeatMap from '@/components/charts/LocationHeatMap';
import TimeSeriesChart from '@/components/charts/TimeSeriesChart';
import SentimentPieChart from '@/components/charts/SentimentPieChart';
import { LocationData, TimeSeriesData, SentimentData, StatisticsData } from '@/types';

// 演示数据
const demoLocationData: LocationData[] = [
  { name: '北京', coordinates: [116.4074, 39.9042], value: 1500, sentiment: 'positive' },
  { name: '上海', coordinates: [121.4737, 31.2304], value: 1200, sentiment: 'positive' },
  { name: '广州', coordinates: [113.2644, 23.1291], value: 800, sentiment: 'neutral' },
  { name: '深圳', coordinates: [114.0579, 22.5431], value: 900, sentiment: 'positive' },
  { name: '杭州', coordinates: [120.1551, 30.2741], value: 600, sentiment: 'positive' },
  { name: '成都', coordinates: [104.0665, 30.5723], value: 700, sentiment: 'neutral' },
];

const demoTimeSeriesData: TimeSeriesData[] = [
  { timestamp: new Date('2024-01-01'), positive: 120, negative: 30, neutral: 80 },
  { timestamp: new Date('2024-01-02'), positive: 150, negative: 25, neutral: 90 },
  { timestamp: new Date('2024-01-03'), positive: 180, negative: 40, neutral: 100 },
  { timestamp: new Date('2024-01-04'), positive: 200, negative: 35, neutral: 110 },
  { timestamp: new Date('2024-01-05'), positive: 170, negative: 45, neutral: 95 },
];

const demoStatisticsData: StatisticsData = {
  total: 1267,
  positive: 825,
  negative: 127,
  neutral: 315,
  growth: 156,
  growthRate: 0.14
};

const FullscreenDemo: React.FC = () => {
  const { isFullscreen, isSupported, toggleFullscreen, exitFullscreen } = useFullscreen();
  
  // 注册快捷键
  const { shortcuts } = useFullscreenShortcuts(toggleFullscreen, exitFullscreen);

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-7xl mx-auto">
        {/* 标题和控制区域 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                全屏功能演示
              </h1>
              <p className="text-gray-400">
                体验大屏幕系统的全屏显示功能
              </p>
            </div>

            {/* 全屏控制按钮 */}
            {isSupported && (
              <div className="flex items-center space-x-4">
                <button
                  onClick={toggleFullscreen}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {isFullscreen ? (
                    <>
                      <Minimize className="w-5 h-5" />
                      <span>退出全屏</span>
                    </>
                  ) : (
                    <>
                      <Maximize className="w-5 h-5" />
                      <span>进入全屏</span>
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* 功能说明卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center space-x-3 mb-3">
                <Monitor className="w-6 h-6 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">全屏显示</h3>
              </div>
              <p className="text-gray-400 text-sm">
                点击全屏按钮或使用快捷键进入全屏模式，获得更好的大屏幕展示效果。
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center space-x-3 mb-3">
                <Keyboard className="w-6 h-6 text-green-400" />
                <h3 className="text-lg font-semibold text-white">快捷键支持</h3>
              </div>
              <div className="space-y-2 text-sm">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex justify-between text-gray-400">
                    <span>{shortcut.description}</span>
                    <code className="bg-gray-700 px-2 py-1 rounded text-xs">
                      {shortcut.displayKey}
                    </code>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-800 rounded-lg p-6"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-6 h-6 bg-purple-500 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">UI</span>
                </div>
                <h3 className="text-lg font-semibold text-white">智能布局</h3>
              </div>
              <p className="text-gray-400 text-sm">
                全屏模式下自动隐藏侧边栏和底部状态栏，最大化内容显示区域。
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* 图表展示区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-800 rounded-lg p-6"
          >
            <LocationHeatMap 
              data={demoLocationData}
              title="地理位置分布"
              height={400}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gray-800 rounded-lg p-6"
          >
            <TimeSeriesChart 
              data={demoTimeSeriesData}
              title="情感趋势分析"
              height={400}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gray-800 rounded-lg p-6 lg:col-span-2"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <SentimentPieChart
                data={demoStatisticsData}
                title="情感分布"
                height={300}
              />
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-4">
                    全屏模式特性
                  </h3>
                  <ul className="text-gray-400 space-y-2 text-left">
                    <li>• 自动隐藏导航栏和侧边栏</li>
                    <li>• 智能显示全屏指示器</li>
                    <li>• 支持多种退出方式</li>
                    <li>• 保持实时数据连接状态</li>
                    <li>• 优化的大屏幕显示效果</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* 状态信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-8 bg-gray-800 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-4">当前状态</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-400">全屏支持:</span>
              <span className={`ml-2 ${isSupported ? 'text-green-400' : 'text-red-400'}`}>
                {isSupported ? '支持' : '不支持'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">当前模式:</span>
              <span className={`ml-2 ${isFullscreen ? 'text-blue-400' : 'text-gray-300'}`}>
                {isFullscreen ? '全屏模式' : '窗口模式'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">屏幕分辨率:</span>
              <span className="ml-2 text-gray-300">
                {window.screen.width} × {window.screen.height}
              </span>
            </div>
            <div>
              <span className="text-gray-400">浏览器:</span>
              <span className="ml-2 text-gray-300">
                {navigator.userAgent.includes('Chrome') ? 'Chrome' :
                 navigator.userAgent.includes('Firefox') ? 'Firefox' :
                 navigator.userAgent.includes('Safari') ? 'Safari' : '其他'}
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default FullscreenDemo;
