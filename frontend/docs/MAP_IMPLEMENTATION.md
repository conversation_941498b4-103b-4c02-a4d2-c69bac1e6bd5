# 中国地图实现文档

## 问题背景

原始的 `LocationHeatMap.tsx` 组件使用了过于简化的中国地图数据（只是一个矩形轮廓），导致地图显示效果很差。同时，项目中安装的 `echarts-countries-js` 包与自定义地图注册产生冲突，导致 `Cannot read properties of undefined (reading 'regions')` 错误。

## 解决方案

### 1. 移除冲突的依赖包

```bash
pnpm remove echarts-countries-js
```

**原因**: `echarts-countries-js` 包会自动注册地图数据，与我们的自定义地图注册产生冲突。

### 2. 获取详细的中国地图数据

创建了 `scripts/download-map-data.js` 脚本，从阿里云DataV获取详细的中国地图GeoJSON数据：

- **完整版**: `/maps/china.json` (2.1MB) - 包含省份边界
- **简化版**: `/maps/china-simple.json` (618KB) - 简化版本

**数据来源**: 
- 完整版: `https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json`
- 简化版: `https://geo.datav.aliyun.com/areas_v3/bound/100000.json`

### 3. 优化地图组件

#### LocationHeatMap.tsx 改进:

1. **多级备用方案**:
   - 优先使用本地详细地图数据 (`/maps/china.json`)
   - 备用在线获取详细数据
   - 再备用本地简化数据 (`/maps/china-simple.json`)
   - 最终备用内置基础数据

2. **加载状态管理**:
   - 添加 `mapReady` 状态
   - 只在地图数据准备好后才渲染ECharts
   - 显示加载动画和错误信息

3. **ECharts 5.6.0 兼容性**:
   - 移除了不兼容的 `heatmap` 系列类型
   - 使用 `scatter` 类型替代
   - 优化数据处理和错误处理

#### SimpleMapTest.tsx:

创建了一个简化的测试组件，用于验证基本地图功能：
- 基础地图显示
- 简单的散点数据
- 详细的错误日志
- 备用地图数据

### 4. 测试页面

创建了 `/map-test` 路由，包含：
- 简单地图测试组件
- 完整热力图组件
- 测试数据和说明

## 使用方法

### 更新地图数据

```bash
# 下载最新的地图数据
npm run update-maps
# 或者
node scripts/download-map-data.js
```

### 在组件中使用

```typescript
import LocationHeatMap from '@/components/charts/LocationHeatMap';

const data: LocationData[] = [
  {
    name: '北京',
    coordinates: [116.4074, 39.9042],
    value: 1500,
    sentiment: 'positive'
  },
  // ... 更多数据
];

<LocationHeatMap 
  data={data}
  title="地理位置分布"
  height={600}
/>
```

## 技术细节

### 地图数据格式

使用标准的GeoJSON格式：
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "省份名称",
        "adcode": 110000
      },
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      }
    }
  ]
}
```

### 坐标系

- **坐标系**: GCJ-02（适合中国地区使用）
- **中心点**: [104.114129, 37.550339]
- **缩放级别**: 1.2

### ECharts 配置

```typescript
const option = {
  geo: {
    map: 'china',
    roam: true,
    zoom: 1.2,
    center: [104.114129, 37.550339],
    // ...
  },
  series: [{
    type: 'scatter',
    coordinateSystem: 'geo',
    data: processedData,
    // ...
  }]
};
```

## 故障排除

### 常见问题

1. **地图不显示**:
   - 检查浏览器控制台是否有地图数据加载错误
   - 确认 `/maps/china.json` 文件存在
   - 检查网络连接（如果使用在线数据源）

2. **数据点不显示**:
   - 确认数据格式正确：`[经度, 纬度, 数值]`
   - 检查坐标范围是否在中国境内
   - 确认 `maxValue` 计算正确

3. **性能问题**:
   - 使用简化版地图数据 (`china-simple.json`)
   - 减少数据点数量
   - 启用 `lazyUpdate` 选项

### 调试方法

1. 打开浏览器开发者工具
2. 查看控制台日志，了解地图数据加载情况
3. 访问 `/map-test` 页面进行功能测试
4. 检查网络面板，确认地图数据请求成功

## 未来改进

1. **省份下钻功能**: 支持点击省份查看详细数据
2. **动态数据更新**: 支持实时数据更新
3. **自定义主题**: 支持多种地图主题
4. **性能优化**: 大数据量时的渲染优化
5. **移动端适配**: 响应式设计优化
