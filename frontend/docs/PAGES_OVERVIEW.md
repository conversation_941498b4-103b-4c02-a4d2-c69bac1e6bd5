# 页面功能说明

## 页面结构

舆情监控大屏幕系统包含以下主要页面：

### 1. 主仪表板 (`/`)
**定位**: 实时数据概览和监控
**特点**:
- 实时数据展示
- 关键指标卡片
- 基础图表组件
- 适合日常监控和快速查看

**主要功能**:
- 实时数据统计
- 情感趋势图表
- 热点话题展示
- 地理分布概览
- 关键词云图

### 2. 数据分析 (`/dashboard`)
**定位**: 深度数据分析和洞察
**特点**:
- 多维度分析视图
- 交互式标签页
- 详细统计信息
- 数据导出功能
- 高级筛选选项

**主要功能**:
- **综合分析**: 全面的数据概览和趋势分析
- **情感分析**: 专门的情感数据深度分析
- **话题分析**: 热点话题和关键词详细分析
- **地域分析**: 地理分布的深入洞察
- 数据筛选和导出
- 时间范围自定义选择

### 3. 地图测试 (`/map-test`)
**定位**: 地图功能测试和演示
**特点**:
- 地图组件测试
- 数据可视化验证
- 功能演示

### 4. 全屏演示 (`/fullscreen-demo`)
**定位**: 全屏功能展示
**特点**:
- 全屏模式演示
- 快捷键功能展示
- 布局适配演示

## 页面对比

| 特性 | 主仪表板 | 数据分析 |
|------|----------|----------|
| **主要用途** | 实时监控 | 深度分析 |
| **数据展示** | 概览式 | 详细式 |
| **交互性** | 基础 | 高级 |
| **分析维度** | 单一视图 | 多标签页 |
| **适用场景** | 日常监控 | 专业分析 |
| **目标用户** | 运营人员 | 分析师 |

## 使用建议

### 日常使用流程
1. **主仪表板** - 快速查看当前状态
2. **数据分析** - 深入分析特定问题
3. **全屏模式** - 大屏幕展示

### 角色分工
- **运营人员**: 主要使用主仪表板进行日常监控
- **数据分析师**: 主要使用数据分析页面进行深度分析
- **管理层**: 使用全屏模式进行汇报展示

## 技术实现

### 主仪表板 (Dashboard.tsx)
```typescript
// 简洁的实时数据展示
- MetricCard 组件展示关键指标
- 基础图表组件
- 实时数据更新
- 响应式布局
```

### 数据分析 (Analytics.tsx)
```typescript
// 复杂的分析界面
- 标签页导航 (overview/sentiment/topics/location)
- 高级筛选和导出功能
- 详细统计信息
- 交互式图表
```

## 导航说明

### 侧边栏导航
- **主仪表板**: 点击进入实时监控视图
- **数据分析**: 点击进入深度分析视图
- **地图测试**: 地图功能测试页面
- **全屏演示**: 全屏功能演示页面

### 快速切换
用户可以通过侧边栏快速在不同页面间切换，每个页面都有明确的功能定位和使用场景。

## 未来扩展

### 计划添加的页面
1. **报告中心**: 自动生成分析报告
2. **设置中心**: 系统配置和个性化设置
3. **用户管理**: 用户权限和角色管理
4. **数据源管理**: 数据源配置和监控

### 功能增强
1. **个性化仪表板**: 用户自定义布局
2. **智能预警**: 基于AI的异常检测
3. **协作功能**: 团队协作和分享
4. **移动端适配**: 响应式移动端支持
