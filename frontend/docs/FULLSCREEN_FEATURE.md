# 全屏功能文档

## 功能概述

大屏幕系统现已集成完整的全屏功能，为用户提供沉浸式的数据展示体验。全屏功能包括智能布局调整、快捷键支持、状态指示器等多项特性。

## 核心特性

### 1. 全屏切换
- **按钮控制**: 头部工具栏提供全屏切换按钮
- **快捷键支持**: 
  - `F11` - 切换全屏模式
  - `Ctrl + F` - 切换全屏模式
  - `ESC` - 退出全屏模式
- **浏览器兼容**: 支持主流浏览器的全屏API

### 2. 智能布局
- **自动隐藏**: 全屏时自动隐藏侧边栏和底部状态栏
- **内容最大化**: 主内容区域占据全部屏幕空间
- **响应式调整**: 根据屏幕尺寸自动调整布局
- **平滑过渡**: 使用动画效果实现平滑的布局切换

### 3. 全屏指示器
- **状态显示**: 显示连接状态、当前时间等关键信息
- **智能隐藏**: 3秒无操作后自动隐藏，鼠标移动时重新显示
- **快速退出**: 提供便捷的退出全屏按钮
- **键盘提示**: 显示退出全屏的快捷键提示

## 技术实现

### Hook 组件

#### useFullscreen
```typescript
const { 
  isFullscreen,      // 当前是否处于全屏状态
  isSupported,       // 浏览器是否支持全屏API
  toggleFullscreen,  // 切换全屏状态
  enterFullscreen,   // 进入全屏
  exitFullscreen     // 退出全屏
} = useFullscreen();
```

#### useKeyboardShortcuts
```typescript
const { shortcuts } = useFullscreenShortcuts(
  toggleFullscreen,  // 切换全屏回调
  exitFullscreen     // 退出全屏回调
);
```

### 组件结构

```
Layout
├── Header (包含全屏按钮)
├── Sidebar (全屏时隐藏)
├── MainContent (全屏时扩展)
├── Footer (全屏时隐藏)
└── FullscreenIndicator (全屏时显示)
```

## 使用方法

### 基本使用

1. **进入全屏**:
   - 点击头部工具栏的全屏按钮
   - 按 `F11` 键
   - 按 `Ctrl + F` 组合键

2. **退出全屏**:
   - 按 `ESC` 键
   - 点击全屏指示器的退出按钮
   - 点击头部工具栏的退出全屏按钮

### 在组件中使用

```typescript
import { useFullscreen } from '@/hooks/useFullscreen';

const MyComponent = () => {
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  
  return (
    <div>
      <button onClick={toggleFullscreen}>
        {isFullscreen ? '退出全屏' : '进入全屏'}
      </button>
    </div>
  );
};
```

## 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 71+ | ✅ 完全支持 |
| Firefox | 64+ | ✅ 完全支持 |
| Safari | 16.4+ | ✅ 完全支持 |
| Edge | 79+ | ✅ 完全支持 |

## 配置选项

### 全屏指示器配置
```typescript
// 自动隐藏时间（毫秒）
const AUTO_HIDE_DELAY = 3000;

// 指示器位置
const INDICATOR_POSITION = 'top-center';

// 显示的信息项
const INDICATOR_ITEMS = [
  'connection-status',
  'current-time',
  'exit-button'
];
```

### 快捷键配置
```typescript
const shortcuts = [
  { key: 'F11', action: 'toggle' },
  { key: 'f', ctrlKey: true, action: 'toggle' },
  { key: 'Escape', action: 'exit' }
];
```

## 最佳实践

### 1. 用户体验
- 提供明确的进入/退出全屏提示
- 保持关键信息的可见性
- 确保快捷键的一致性

### 2. 性能优化
- 全屏切换时避免重新渲染大量组件
- 使用CSS动画而非JavaScript动画
- 合理使用防抖和节流

### 3. 错误处理
- 检测浏览器支持情况
- 处理全屏API调用失败
- 提供降级方案

## 故障排除

### 常见问题

1. **全屏按钮不显示**
   - 检查浏览器是否支持全屏API
   - 确认组件正确导入useFullscreen Hook

2. **快捷键不生效**
   - 检查是否有其他组件阻止了事件传播
   - 确认快捷键监听器正确注册

3. **布局异常**
   - 检查CSS样式是否正确应用
   - 确认动画过渡效果设置

### 调试方法

```typescript
// 开启调试模式
const DEBUG_FULLSCREEN = true;

if (DEBUG_FULLSCREEN) {
  console.log('Fullscreen state:', isFullscreen);
  console.log('Browser support:', isSupported);
}
```

## 演示页面

访问 `/fullscreen-demo` 路由可以体验完整的全屏功能演示，包括：

- 全屏切换演示
- 快捷键功能展示
- 布局变化效果
- 指示器行为演示
- 兼容性信息显示

## 未来改进

1. **多屏幕支持**: 支持多显示器环境下的全屏显示
2. **自定义布局**: 允许用户自定义全屏模式下的布局
3. **手势支持**: 在触摸设备上支持手势操作
4. **画中画模式**: 支持画中画显示模式
5. **VR/AR支持**: 为虚拟现实和增强现实设备优化
