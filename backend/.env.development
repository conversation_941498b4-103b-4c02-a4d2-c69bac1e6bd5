# 开发环境配置
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/weibo_sentiment_dev
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=dev-jwt-secret-key
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:3001

# 日志配置
LOG_LEVEL=debug
LOG_DIR=logs

# WebSocket 配置
WS_CORS_ORIGIN=http://localhost:3001

# 数据刷新间隔（毫秒）
DATA_REFRESH_INTERVAL=10000

# 系统监控间隔（毫秒）
SYSTEM_MONITOR_INTERVAL=3000

# 模拟数据配置
ENABLE_MOCK_DATA=true
MOCK_DATA_INTERVAL=5000
