2025-08-04 21:38:15 [ERROR]: uncaughtException: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
{
  "error": {
    "diagnosticCodes": [
      2339
    ]
  },
  "exception": true,
  "date": "Mon Aug 04 2025 21:38:15 GMT+0800 (China Standard Time)",
  "process": {
    "pid": 603444,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/ideas/weibo-v2/backend",
    "execPath": "/home/<USER>/.nvm/versions/node/v20.15.1/bin/node",
    "version": "v20.15.1",
    "argv": [
      "/home/<USER>/ideas/weibo-v2/backend/node_modules/ts-node/dist/bin.js",
      "/home/<USER>/ideas/weibo-v2/backend/src/server.ts"
    ],
    "memoryUsage": {
      "rss": 329261056,
      "heapTotal": 244633600,
      "heapUsed": 222106528,
      "external": 23523063,
      "arrayBuffers": 20604745
    }
  },
  "os": {
    "loadavg": [
      0.87,
      0.71,
      0.6
    ],
    "uptime": 77407.08
  },
  "trace": [
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "createTSError",
      "line": 859,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "reportTSError",
      "line": 863,
      "method": null,
      "native": false
    },
    {
      "column": 36,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "getOutput",
      "line": 1077,
      "method": null,
      "native": false
    },
    {
      "column": 41,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.compile",
      "line": 1433,
      "method": "compile",
      "native": false
    },
    {
      "column": 30,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Module.m._compile",
      "line": 1617,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.require.extensions.<computed> [as .ts]",
      "line": 1621,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-04 21:38:27 [ERROR]: uncaughtException: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
{
  "error": {
    "diagnosticCodes": [
      2339
    ]
  },
  "exception": true,
  "date": "Mon Aug 04 2025 21:38:27 GMT+0800 (China Standard Time)",
  "process": {
    "pid": 603619,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/ideas/weibo-v2/backend",
    "execPath": "/home/<USER>/.nvm/versions/node/v20.15.1/bin/node",
    "version": "v20.15.1",
    "argv": [
      "/home/<USER>/ideas/weibo-v2/backend/node_modules/ts-node/dist/bin.js",
      "/home/<USER>/ideas/weibo-v2/backend/src/server.ts"
    ],
    "memoryUsage": {
      "rss": 322654208,
      "heapTotal": 246669312,
      "heapUsed": 216637872,
      "external": 25844223,
      "arrayBuffers": 22912795
    }
  },
  "os": {
    "loadavg": [
      1.5,
      0.85,
      0.65
    ],
    "uptime": 77418.18
  },
  "trace": [
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "createTSError",
      "line": 859,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "reportTSError",
      "line": 863,
      "method": null,
      "native": false
    },
    {
      "column": 36,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "getOutput",
      "line": 1077,
      "method": null,
      "native": false
    },
    {
      "column": 41,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.compile",
      "line": 1433,
      "method": "compile",
      "native": false
    },
    {
      "column": 30,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Module.m._compile",
      "line": 1617,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.require.extensions.<computed> [as .ts]",
      "line": 1621,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-04 21:38:47 [ERROR]: uncaughtException: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
{
  "error": {
    "diagnosticCodes": [
      2339
    ]
  },
  "exception": true,
  "date": "Mon Aug 04 2025 21:38:47 GMT+0800 (China Standard Time)",
  "process": {
    "pid": 603737,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/ideas/weibo-v2/backend",
    "execPath": "/home/<USER>/.nvm/versions/node/v20.15.1/bin/node",
    "version": "v20.15.1",
    "argv": [
      "/home/<USER>/ideas/weibo-v2/backend/node_modules/ts-node/dist/bin.js",
      "/home/<USER>/ideas/weibo-v2/backend/src/server.ts"
    ],
    "memoryUsage": {
      "rss": 322408448,
      "heapTotal": 246984704,
      "heapUsed": 212794056,
      "external": 25844439,
      "arrayBuffers": 22913011
    }
  },
  "os": {
    "loadavg": [
      2.05,
      1.01,
      0.7
    ],
    "uptime": 77438.73
  },
  "trace": [
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "createTSError",
      "line": 859,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "reportTSError",
      "line": 863,
      "method": null,
      "native": false
    },
    {
      "column": 36,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "getOutput",
      "line": 1077,
      "method": null,
      "native": false
    },
    {
      "column": 41,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.compile",
      "line": 1433,
      "method": "compile",
      "native": false
    },
    {
      "column": 30,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Module.m._compile",
      "line": 1617,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.require.extensions.<computed> [as .ts]",
      "line": 1621,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-04 21:39:01 [ERROR]: uncaughtException: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
{
  "error": {
    "diagnosticCodes": [
      2339
    ]
  },
  "exception": true,
  "date": "Mon Aug 04 2025 21:39:01 GMT+0800 (China Standard Time)",
  "process": {
    "pid": 603855,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/ideas/weibo-v2/backend",
    "execPath": "/home/<USER>/.nvm/versions/node/v20.15.1/bin/node",
    "version": "v20.15.1",
    "argv": [
      "/home/<USER>/ideas/weibo-v2/backend/node_modules/ts-node/dist/bin.js",
      "/home/<USER>/ideas/weibo-v2/backend/src/server.ts"
    ],
    "memoryUsage": {
      "rss": 321441792,
      "heapTotal": 246722560,
      "heapUsed": 212802984,
      "external": 25844415,
      "arrayBuffers": 22912987
    }
  },
  "os": {
    "loadavg": [
      2.12,
      1.08,
      0.73
    ],
    "uptime": 77452.97
  },
  "trace": [
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "createTSError",
      "line": 859,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "reportTSError",
      "line": 863,
      "method": null,
      "native": false
    },
    {
      "column": 36,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "getOutput",
      "line": 1077,
      "method": null,
      "native": false
    },
    {
      "column": 41,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.compile",
      "line": 1433,
      "method": "compile",
      "native": false
    },
    {
      "column": 30,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Module.m._compile",
      "line": 1617,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.require.extensions.<computed> [as .ts]",
      "line": 1621,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-04 21:40:27 [ERROR]: uncaughtException: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
TSError: ⨯ Unable to compile TypeScript:
[96msrc/services/dataGeneratorService.ts[0m:[93m58[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'destroy' does not exist on type 'ScheduledTask'.

[7m58[0m     this.cronJobs.forEach(job => job.destroy());
[7m  [0m [91m                                     ~~~~~~~[0m

    at createTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:859:12)
    at reportTSError (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:863:19)
    at getOutput (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1077:36)
    at Object.compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1433:41)
    at Module.m._compile (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Object.require.extensions.<computed> [as .ts] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
{
  "error": {
    "diagnosticCodes": [
      2339
    ]
  },
  "exception": true,
  "date": "Mon Aug 04 2025 21:40:27 GMT+0800 (China Standard Time)",
  "process": {
    "pid": 604658,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/ideas/weibo-v2/backend",
    "execPath": "/home/<USER>/.nvm/versions/node/v20.15.1/bin/node",
    "version": "v20.15.1",
    "argv": [
      "/home/<USER>/ideas/weibo-v2/backend/node_modules/ts-node/dist/bin.js",
      "/home/<USER>/ideas/weibo-v2/backend/src/server.ts"
    ],
    "memoryUsage": {
      "rss": 321933312,
      "heapTotal": 244633600,
      "heapUsed": 222331568,
      "external": 25844279,
      "arrayBuffers": 22925961
    }
  },
  "os": {
    "loadavg": [
      1.28,
      1,
      0.73
    ],
    "uptime": 77538.7
  },
  "trace": [
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "createTSError",
      "line": 859,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "reportTSError",
      "line": 863,
      "method": null,
      "native": false
    },
    {
      "column": 36,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "getOutput",
      "line": 1077,
      "method": null,
      "native": false
    },
    {
      "column": 41,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.compile",
      "line": 1433,
      "method": "compile",
      "native": false
    },
    {
      "column": 30,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Module.m._compile",
      "line": 1617,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 12,
      "file": "/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.19.9_typescript@5.9.2/node_modules/ts-node/src/index.ts",
      "function": "Object.require.extensions.<computed> [as .ts]",
      "line": 1621,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
