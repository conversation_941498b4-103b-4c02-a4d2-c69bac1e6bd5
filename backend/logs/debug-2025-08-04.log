2025-08-04 21:54:48 [INFO]: WebSocket 服务初始化完成
2025-08-04 21:54:48 [INFO]: 正在连接 MongoDB...
{
  "uri": "mongodb://localhost:27017/weibo_sentiment_dev"
}
2025-08-04 21:54:48 [INFO]: 正在连接 Redis...
{
  "url": "redis://localhost:6379"
}
2025-08-04 21:54:48 [ERROR]: MongoDB 连接失败
{
  "error": {
    "errorLabelSet": {}
  }
}
2025-08-04 21:54:48 [ERROR]: 数据库连接失败
{
  "error": {
    "errorLabelSet": {}
  }
}
2025-08-04 21:54:48 [WARN]: 服务器将使用模拟数据运行
2025-08-04 21:54:48 [INFO]: 启动数据生成器
2025-08-04 21:54:48 [INFO]: 启动模拟数据生成
{
  "interval": 5000
}
2025-08-04 21:54:48 [INFO]: 启动系统监控
{
  "interval": 3000
}
2025-08-04 21:54:48 [INFO]: 启动心跳服务
2025-08-04 21:54:48 [INFO]: 服务器启动成功
{
  "host": "0.0.0.0",
  "port": 8080,
  "env": "development",
  "websocket": "已启用",
  "mockData": "已启用"
}
2025-08-04 21:54:48 [INFO]: Redis 连接成功
2025-08-04 21:54:50 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:54:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:54:50 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:54:51 [DEBUG]: 获取系统状态
2025-08-04 21:54:51 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 33,
  "network": 16.988790342670974,
  "connectedClients": 0
}
2025-08-04 21:54:52 [WARN]: 客户端错误 路由 GET / 不存在
CustomError: 路由 GET / 不存在
    at Object.notFound (/home/<USER>/ideas/weibo-v2/backend/src/middleware/errorHandler.ts:36:5)
    at notFoundHandler (/home/<USER>/ideas/weibo-v2/backend/src/middleware/errorHandler.ts:146:29)
    at Layer.handle [as handle_request] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:280:10)
    at logger (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/morgan@1.10.1/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:328:13)
{
  "statusCode": 404,
  "code": "NOT_FOUND",
  "path": "/",
  "method": "GET",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "body": {},
  "query": {},
  "params": {}
}
2025-08-04 21:54:52 [WARN]: 客户端错误 路由 GET /favicon.ico 不存在
CustomError: 路由 GET /favicon.ico 不存在
    at Object.notFound (/home/<USER>/ideas/weibo-v2/backend/src/middleware/errorHandler.ts:36:5)
    at notFoundHandler (/home/<USER>/ideas/weibo-v2/backend/src/middleware/errorHandler.ts:146:29)
    at Layer.handle [as handle_request] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:280:10)
    at logger (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/morgan@1.10.1/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/ideas/weibo-v2/backend/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:328:13)
{
  "statusCode": 404,
  "code": "NOT_FOUND",
  "path": "/favicon.ico",
  "method": "GET",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "body": {},
  "query": {},
  "params": {}
}
2025-08-04 21:54:54 [DEBUG]: 获取系统状态
2025-08-04 21:54:54 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 13.905445228746833,
  "connectedClients": 0
}
2025-08-04 21:54:55 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:54:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:54:55 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:54:57 [DEBUG]: 获取系统状态
2025-08-04 21:54:57 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 33.32182901951137,
  "connectedClients": 0
}
2025-08-04 21:55:00 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:55:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:00 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:55:00 [DEBUG]: 获取系统状态
2025-08-04 21:55:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 21:55:00 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 33,
  "network": 50.2945393724011,
  "connectedClients": 0
}
2025-08-04 21:55:03 [DEBUG]: 获取系统状态
2025-08-04 21:55:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 33,
  "network": 7.554359434836906,
  "connectedClients": 0
}
2025-08-04 21:55:05 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:55:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:05 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:55:06 [DEBUG]: 获取系统状态
2025-08-04 21:55:06 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 29.427188602500127,
  "connectedClients": 0
}
2025-08-04 21:55:09 [DEBUG]: 获取系统状态
2025-08-04 21:55:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 15.233878104802674,
  "connectedClients": 0
}
2025-08-04 21:55:10 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:55:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:10 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:55:12 [DEBUG]: 获取系统状态
2025-08-04 21:55:12 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 34,
  "network": 22.068513092774808,
  "connectedClients": 0
}
2025-08-04 21:55:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:55:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:55:15 [DEBUG]: 获取系统状态
2025-08-04 21:55:15 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 34,
  "network": 29.122280996287415,
  "connectedClients": 0
}
2025-08-04 21:55:18 [DEBUG]: 获取系统状态
2025-08-04 21:55:18 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 34,
  "network": 8.778336773841051,
  "connectedClients": 0
}
2025-08-04 21:55:20 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:55:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:20 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:55:21 [DEBUG]: 获取系统状态
2025-08-04 21:55:21 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 34,
  "network": 17.866977834755247,
  "connectedClients": 0
}
2025-08-04 21:55:24 [DEBUG]: 获取系统状态
2025-08-04 21:55:24 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 34,
  "network": 23.253251225408672,
  "connectedClients": 0
}
2025-08-04 21:55:25 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:55:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:25 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:55:27 [DEBUG]: 获取系统状态
2025-08-04 21:55:27 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 34,
  "network": 18.991772205296584,
  "connectedClients": 0
}
2025-08-04 21:55:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:55:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 21:55:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:55:30 [DEBUG]: 获取系统状态
2025-08-04 21:55:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 21:55:30 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 34,
  "network": 11.272367412861968,
  "connectedClients": 0
}
2025-08-04 21:55:30 [INFO]: 客户端连接
{
  "clientId": "smfyXJSa-DQ_xmJXAAAB",
  "totalClients": 1
}
2025-08-04 21:55:30 [INFO]: 客户端连接
{
  "clientId": "pD8n1sfTazPT5OMnAAAD",
  "totalClients": 2
}
2025-08-04 21:55:33 [DEBUG]: 获取系统状态
2025-08-04 21:55:33 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 34,
  "network": 24.480665422493438,
  "connectedClients": 2
}
2025-08-04 21:55:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:55:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:55:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:55:36 [DEBUG]: 获取系统状态
2025-08-04 21:55:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 34,
  "network": 38.01791615470925,
  "connectedClients": 2
}
2025-08-04 21:55:39 [DEBUG]: 获取系统状态
2025-08-04 21:55:39 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 34,
  "network": 46.36141550673403,
  "connectedClients": 2
}
2025-08-04 21:55:40 [DEBUG]: 获取地理位置数据
2025-08-04 21:55:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:55:40 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:55:42 [DEBUG]: 获取系统状态
2025-08-04 21:55:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 34,
  "network": 43.40706290924626,
  "connectedClients": 2
}
2025-08-04 21:55:45 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:55:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:55:45 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:55:45 [DEBUG]: 获取系统状态
2025-08-04 21:55:45 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 33,
  "network": 46.22253786045314,
  "connectedClients": 2
}
2025-08-04 21:55:45 [INFO]: 获取统计数据
{
  "range": "24h"
}
2025-08-04 21:55:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:55:48 [DEBUG]: 获取系统状态
2025-08-04 21:55:48 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 33,
  "network": 18.351716392730427,
  "connectedClients": 2
}
2025-08-04 21:55:50 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:55:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:55:50 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:55:51 [DEBUG]: 获取系统状态
2025-08-04 21:55:51 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 52.31799906747699,
  "connectedClients": 2
}
2025-08-04 21:55:54 [DEBUG]: 获取系统状态
2025-08-04 21:55:54 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 38.56644245857233,
  "connectedClients": 2
}
2025-08-04 21:55:54 [INFO]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:55:54 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:55:55 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:55:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:55:55 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:55:57 [DEBUG]: 获取系统状态
2025-08-04 21:55:57 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 33,
  "network": 15.18561360095126,
  "connectedClients": 2
}
2025-08-04 21:56:00 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:56:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:00 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:56:00 [DEBUG]: 获取系统状态
2025-08-04 21:56:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:56:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 46.54097939842676,
  "connectedClients": 2
}
2025-08-04 21:56:03 [DEBUG]: 获取系统状态
2025-08-04 21:56:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 33,
  "network": 5.082944746750124,
  "connectedClients": 2
}
2025-08-04 21:56:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:56:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:56:06 [DEBUG]: 获取系统状态
2025-08-04 21:56:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 21.192222071931383,
  "connectedClients": 2
}
2025-08-04 21:56:09 [DEBUG]: 获取系统状态
2025-08-04 21:56:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 30.400828606487757,
  "connectedClients": 2
}
2025-08-04 21:56:10 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:56:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:10 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:56:12 [DEBUG]: 获取系统状态
2025-08-04 21:56:12 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 51.27886590364712,
  "connectedClients": 2
}
2025-08-04 21:56:15 [DEBUG]: 获取地理位置数据
2025-08-04 21:56:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:15 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:56:15 [DEBUG]: 获取系统状态
2025-08-04 21:56:15 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 7.623439788284987,
  "connectedClients": 2
}
2025-08-04 21:56:18 [DEBUG]: 获取系统状态
2025-08-04 21:56:18 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 24.594734330694074,
  "connectedClients": 2
}
2025-08-04 21:56:20 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:56:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:20 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:56:21 [DEBUG]: 获取系统状态
2025-08-04 21:56:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 41.03993792576819,
  "connectedClients": 2
}
2025-08-04 21:56:24 [DEBUG]: 获取系统状态
2025-08-04 21:56:24 [DEBUG]: 系统指标
{
  "cpu": 12,
  "memory": 34,
  "network": 38.69061644775191,
  "connectedClients": 2
}
2025-08-04 21:56:25 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:56:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:25 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:56:27 [DEBUG]: 获取系统状态
2025-08-04 21:56:27 [DEBUG]: 系统指标
{
  "cpu": 17,
  "memory": 34,
  "network": 54.263093633574194,
  "connectedClients": 2
}
2025-08-04 21:56:30 [DEBUG]: 获取地理位置数据
2025-08-04 21:56:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:30 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:56:30 [DEBUG]: 获取系统状态
2025-08-04 21:56:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:56:30 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 33,
  "network": 54.82759036509735,
  "connectedClients": 2
}
2025-08-04 21:56:33 [DEBUG]: 获取系统状态
2025-08-04 21:56:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 33.580377165813154,
  "connectedClients": 2
}
2025-08-04 21:56:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:56:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:56:36 [DEBUG]: 获取系统状态
2025-08-04 21:56:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 33,
  "network": 38.91664390663331,
  "connectedClients": 2
}
2025-08-04 21:56:39 [DEBUG]: 获取系统状态
2025-08-04 21:56:39 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 8.405981006173697,
  "connectedClients": 2
}
2025-08-04 21:56:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:56:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:56:42 [DEBUG]: 获取系统状态
2025-08-04 21:56:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 23.715783337854973,
  "connectedClients": 2
}
2025-08-04 21:56:45 [DEBUG]: 获取地理位置数据
2025-08-04 21:56:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:45 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:56:45 [DEBUG]: 获取系统状态
2025-08-04 21:56:45 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 33,
  "network": 32.73951935606543,
  "connectedClients": 2
}
2025-08-04 21:56:48 [DEBUG]: 获取系统状态
2025-08-04 21:56:48 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 41.68035198248462,
  "connectedClients": 2
}
2025-08-04 21:56:50 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:56:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:50 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:56:51 [DEBUG]: 获取系统状态
2025-08-04 21:56:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 33,
  "network": 9.019172214934008,
  "connectedClients": 2
}
2025-08-04 21:56:54 [DEBUG]: 获取系统状态
2025-08-04 21:56:54 [DEBUG]: 系统指标
{
  "cpu": 18,
  "memory": 34,
  "network": 54.47973894558304,
  "connectedClients": 2
}
2025-08-04 21:56:55 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:56:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:56:55 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:56:57 [DEBUG]: 获取系统状态
2025-08-04 21:56:57 [DEBUG]: 系统指标
{
  "cpu": 30,
  "memory": 34,
  "network": 10.266849756745383,
  "connectedClients": 2
}
2025-08-04 21:57:00 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:57:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:00 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:57:00 [DEBUG]: 获取系统状态
2025-08-04 21:57:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:57:00 [DEBUG]: 系统指标
{
  "cpu": 21,
  "memory": 34,
  "network": 11.281619604970079,
  "connectedClients": 2
}
2025-08-04 21:57:03 [DEBUG]: 获取系统状态
2025-08-04 21:57:03 [DEBUG]: 系统指标
{
  "cpu": 17,
  "memory": 34,
  "network": 28.975133782560466,
  "connectedClients": 2
}
2025-08-04 21:57:05 [DEBUG]: 获取地理位置数据
2025-08-04 21:57:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:57:06 [DEBUG]: 获取系统状态
2025-08-04 21:57:06 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 35,
  "network": 25.972369515232657,
  "connectedClients": 2
}
2025-08-04 21:57:09 [DEBUG]: 获取系统状态
2025-08-04 21:57:09 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 35,
  "network": 36.547449315730816,
  "connectedClients": 2
}
2025-08-04 21:57:10 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:57:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:10 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:57:12 [DEBUG]: 获取系统状态
2025-08-04 21:57:12 [DEBUG]: 系统指标
{
  "cpu": 24,
  "memory": 36,
  "network": 34.99459192610252,
  "connectedClients": 2
}
2025-08-04 21:57:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:57:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:57:15 [DEBUG]: 获取系统状态
2025-08-04 21:57:15 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 36,
  "network": 16.765367027804352,
  "connectedClients": 2
}
2025-08-04 21:57:18 [DEBUG]: 获取系统状态
2025-08-04 21:57:18 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 35,
  "network": 47.58879365051309,
  "connectedClients": 2
}
2025-08-04 21:57:20 [DEBUG]: 获取地理位置数据
2025-08-04 21:57:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:20 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:57:21 [DEBUG]: 获取系统状态
2025-08-04 21:57:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 6.1802564791910335,
  "connectedClients": 2
}
2025-08-04 21:57:24 [DEBUG]: 获取系统状态
2025-08-04 21:57:24 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 16.685182106867465,
  "connectedClients": 2
}
2025-08-04 21:57:25 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:57:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:25 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:57:27 [DEBUG]: 获取系统状态
2025-08-04 21:57:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 20.059894813049734,
  "connectedClients": 2
}
2025-08-04 21:57:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:57:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:57:30 [DEBUG]: 获取系统状态
2025-08-04 21:57:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:57:30 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 24.072295086651458,
  "connectedClients": 2
}
2025-08-04 21:57:33 [DEBUG]: 获取系统状态
2025-08-04 21:57:33 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 34.709939918686885,
  "connectedClients": 2
}
2025-08-04 21:57:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:57:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:57:36 [DEBUG]: 获取系统状态
2025-08-04 21:57:36 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 35,
  "network": 36.60365724886273,
  "connectedClients": 2
}
2025-08-04 21:57:39 [DEBUG]: 获取系统状态
2025-08-04 21:57:39 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 31.66049000652464,
  "connectedClients": 2
}
2025-08-04 21:57:40 [DEBUG]: 获取地理位置数据
2025-08-04 21:57:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:40 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:57:42 [DEBUG]: 获取系统状态
2025-08-04 21:57:42 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 51.600114943390146,
  "connectedClients": 2
}
2025-08-04 21:57:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:57:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:57:45 [DEBUG]: 获取系统状态
2025-08-04 21:57:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 13.534073278329812,
  "connectedClients": 2
}
2025-08-04 21:57:48 [DEBUG]: 获取系统状态
2025-08-04 21:57:48 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 49.02063753432692,
  "connectedClients": 2
}
2025-08-04 21:57:50 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:57:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:50 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:57:51 [DEBUG]: 获取系统状态
2025-08-04 21:57:51 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 35,
  "network": 18.849523265003292,
  "connectedClients": 2
}
2025-08-04 21:57:54 [DEBUG]: 获取系统状态
2025-08-04 21:57:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 21.77814895471591,
  "connectedClients": 2
}
2025-08-04 21:57:55 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:57:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:57:55 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:57:57 [DEBUG]: 获取系统状态
2025-08-04 21:57:57 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 9.259302840466495,
  "connectedClients": 2
}
2025-08-04 21:58:00 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:58:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:00 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:58:00 [DEBUG]: 获取系统状态
2025-08-04 21:58:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:58:00 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 14.398332953199862,
  "connectedClients": 2
}
2025-08-04 21:58:03 [DEBUG]: 获取系统状态
2025-08-04 21:58:03 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 35,
  "network": 26.22602340458065,
  "connectedClients": 2
}
2025-08-04 21:58:05 [DEBUG]: 获取地理位置数据
2025-08-04 21:58:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:58:06 [DEBUG]: 获取系统状态
2025-08-04 21:58:06 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 35,
  "network": 52.38243771373111,
  "connectedClients": 2
}
2025-08-04 21:58:09 [DEBUG]: 获取系统状态
2025-08-04 21:58:09 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 24.106268974365726,
  "connectedClients": 2
}
2025-08-04 21:58:10 [DEBUG]: 获取地理位置数据
2025-08-04 21:58:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:10 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:58:12 [DEBUG]: 获取系统状态
2025-08-04 21:58:12 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 33.93264188876648,
  "connectedClients": 2
}
2025-08-04 21:58:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:58:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:58:15 [DEBUG]: 获取系统状态
2025-08-04 21:58:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 9.890783922524044,
  "connectedClients": 2
}
2025-08-04 21:58:18 [DEBUG]: 获取系统状态
2025-08-04 21:58:18 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 49.40307854121013,
  "connectedClients": 2
}
2025-08-04 21:58:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:58:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:58:21 [DEBUG]: 获取系统状态
2025-08-04 21:58:21 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 21.908997711245476,
  "connectedClients": 2
}
2025-08-04 21:58:24 [DEBUG]: 获取系统状态
2025-08-04 21:58:24 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 35,
  "network": 49.96806717814623,
  "connectedClients": 2
}
2025-08-04 21:58:25 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:58:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:25 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:58:27 [DEBUG]: 获取系统状态
2025-08-04 21:58:27 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 35,
  "network": 39.31544093510895,
  "connectedClients": 2
}
2025-08-04 21:58:30 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:58:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:30 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:58:30 [DEBUG]: 获取系统状态
2025-08-04 21:58:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:58:30 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 54.85403772601341,
  "connectedClients": 2
}
2025-08-04 21:58:33 [DEBUG]: 获取系统状态
2025-08-04 21:58:33 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 44.44422577843878,
  "connectedClients": 2
}
2025-08-04 21:58:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:58:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:58:36 [DEBUG]: 获取系统状态
2025-08-04 21:58:36 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 50.20210607650025,
  "connectedClients": 2
}
2025-08-04 21:58:39 [DEBUG]: 获取系统状态
2025-08-04 21:58:39 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 6.2279432151619085,
  "connectedClients": 2
}
2025-08-04 21:58:40 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:58:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:40 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:58:42 [DEBUG]: 获取系统状态
2025-08-04 21:58:42 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 13.76991247219092,
  "connectedClients": 2
}
2025-08-04 21:58:45 [DEBUG]: 获取地理位置数据
2025-08-04 21:58:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:45 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:58:45 [DEBUG]: 获取系统状态
2025-08-04 21:58:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 46.109154974767016,
  "connectedClients": 2
}
2025-08-04 21:58:48 [DEBUG]: 获取系统状态
2025-08-04 21:58:48 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 20.274006592727407,
  "connectedClients": 2
}
2025-08-04 21:58:50 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:58:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:50 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:58:51 [DEBUG]: 获取系统状态
2025-08-04 21:58:51 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 35,
  "network": 27.63644102203618,
  "connectedClients": 2
}
2025-08-04 21:58:54 [DEBUG]: 获取系统状态
2025-08-04 21:58:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 32.34332801117798,
  "connectedClients": 2
}
2025-08-04 21:58:55 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:58:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:58:55 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:58:57 [DEBUG]: 获取系统状态
2025-08-04 21:58:57 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 44.03462682813675,
  "connectedClients": 2
}
2025-08-04 21:59:00 [DEBUG]: 获取地理位置数据
2025-08-04 21:59:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:00 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:59:00 [DEBUG]: 获取系统状态
2025-08-04 21:59:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:59:00 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 35,
  "network": 35.059508472267304,
  "connectedClients": 2
}
2025-08-04 21:59:03 [DEBUG]: 获取系统状态
2025-08-04 21:59:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 39.20531110689639,
  "connectedClients": 2
}
2025-08-04 21:59:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:59:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:59:06 [DEBUG]: 获取系统状态
2025-08-04 21:59:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 20.69440224013161,
  "connectedClients": 2
}
2025-08-04 21:59:09 [DEBUG]: 获取系统状态
2025-08-04 21:59:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 48.624625404836124,
  "connectedClients": 2
}
2025-08-04 21:59:10 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 21:59:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:10 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 21:59:12 [DEBUG]: 获取系统状态
2025-08-04 21:59:12 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 28.71904790746262,
  "connectedClients": 2
}
2025-08-04 21:59:15 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:59:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:15 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:59:15 [DEBUG]: 获取系统状态
2025-08-04 21:59:15 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 35,
  "network": 45.35130558493829,
  "connectedClients": 2
}
2025-08-04 21:59:18 [DEBUG]: 获取系统状态
2025-08-04 21:59:18 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 35,
  "network": 49.14548393733266,
  "connectedClients": 2
}
2025-08-04 21:59:20 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 21:59:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:20 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 21:59:21 [DEBUG]: 获取系统状态
2025-08-04 21:59:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 48.380798910562554,
  "connectedClients": 2
}
2025-08-04 21:59:24 [DEBUG]: 获取系统状态
2025-08-04 21:59:24 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 41.46457471983629,
  "connectedClients": 2
}
2025-08-04 21:59:25 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:59:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:25 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:59:27 [DEBUG]: 获取系统状态
2025-08-04 21:59:27 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 38.72544252492406,
  "connectedClients": 2
}
2025-08-04 21:59:30 [DEBUG]: 获取地理位置数据
2025-08-04 21:59:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:30 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:59:30 [DEBUG]: 获取系统状态
2025-08-04 21:59:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 21:59:30 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 32.882762459098146,
  "connectedClients": 2
}
2025-08-04 21:59:33 [DEBUG]: 获取系统状态
2025-08-04 21:59:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 32.49421231966437,
  "connectedClients": 2
}
2025-08-04 21:59:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 21:59:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 21:59:36 [DEBUG]: 获取系统状态
2025-08-04 21:59:36 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 35,
  "network": 41.32929411419972,
  "connectedClients": 2
}
2025-08-04 21:59:39 [DEBUG]: 获取系统状态
2025-08-04 21:59:39 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 35,
  "network": 34.801765927607846,
  "connectedClients": 2
}
2025-08-04 21:59:40 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:59:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:40 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:59:42 [DEBUG]: 获取系统状态
2025-08-04 21:59:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 47.56335042243597,
  "connectedClients": 2
}
2025-08-04 21:59:45 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 21:59:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:45 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 21:59:45 [DEBUG]: 获取系统状态
2025-08-04 21:59:45 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 10.251547304201543,
  "connectedClients": 2
}
2025-08-04 21:59:48 [DEBUG]: 获取系统状态
2025-08-04 21:59:48 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 25.113542957625043,
  "connectedClients": 2
}
2025-08-04 21:59:50 [DEBUG]: 获取地理位置数据
2025-08-04 21:59:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 21:59:51 [DEBUG]: 获取系统状态
2025-08-04 21:59:51 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 38.877874138418164,
  "connectedClients": 2
}
2025-08-04 21:59:54 [DEBUG]: 获取系统状态
2025-08-04 21:59:54 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 22.50565322687575,
  "connectedClients": 2
}
2025-08-04 21:59:55 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 21:59:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 21:59:55 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 21:59:57 [DEBUG]: 获取系统状态
2025-08-04 21:59:57 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 16.104378108126625,
  "connectedClients": 2
}
2025-08-04 22:00:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:00:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:00:00 [DEBUG]: 获取系统状态
2025-08-04 22:00:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:00:00 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 49.6937729246044,
  "connectedClients": 2
}
2025-08-04 22:00:03 [DEBUG]: 获取系统状态
2025-08-04 22:00:03 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 35,
  "network": 9.977362828424525,
  "connectedClients": 2
}
2025-08-04 22:00:05 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:00:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:05 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:00:06 [DEBUG]: 获取系统状态
2025-08-04 22:00:06 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 35,
  "network": 7.284531807910161,
  "connectedClients": 2
}
2025-08-04 22:00:09 [DEBUG]: 获取系统状态
2025-08-04 22:00:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 19.81472127385969,
  "connectedClients": 2
}
2025-08-04 22:00:10 [DEBUG]: 获取地理位置数据
2025-08-04 22:00:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:10 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:00:12 [DEBUG]: 获取系统状态
2025-08-04 22:00:12 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 31.73319618329293,
  "connectedClients": 2
}
2025-08-04 22:00:15 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:00:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:15 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:00:15 [DEBUG]: 获取系统状态
2025-08-04 22:00:15 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 34.03203578675304,
  "connectedClients": 2
}
2025-08-04 22:00:18 [DEBUG]: 获取系统状态
2025-08-04 22:00:18 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 20.410633723374822,
  "connectedClients": 2
}
2025-08-04 22:00:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:00:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:00:21 [DEBUG]: 获取系统状态
2025-08-04 22:00:21 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 35,
  "network": 44.592452793128444,
  "connectedClients": 2
}
2025-08-04 22:00:24 [DEBUG]: 获取系统状态
2025-08-04 22:00:24 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 35,
  "network": 53.093230715410094,
  "connectedClients": 2
}
2025-08-04 22:00:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:00:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:00:27 [DEBUG]: 获取系统状态
2025-08-04 22:00:27 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 35,
  "network": 48.53481983609965,
  "connectedClients": 2
}
2025-08-04 22:00:30 [DEBUG]: 获取地理位置数据
2025-08-04 22:00:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:30 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:00:30 [DEBUG]: 获取系统状态
2025-08-04 22:00:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:00:30 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 35,
  "network": 35.43777545323193,
  "connectedClients": 2
}
2025-08-04 22:00:33 [DEBUG]: 获取系统状态
2025-08-04 22:00:33 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 35,
  "network": 26.110237789863884,
  "connectedClients": 2
}
2025-08-04 22:00:35 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:00:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:35 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:00:36 [DEBUG]: 获取系统状态
2025-08-04 22:00:36 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 35,
  "network": 34.128379369136894,
  "connectedClients": 2
}
2025-08-04 22:00:39 [DEBUG]: 获取系统状态
2025-08-04 22:00:39 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 35,
  "network": 13.726734838090543,
  "connectedClients": 2
}
2025-08-04 22:00:40 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:00:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:40 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:00:42 [DEBUG]: 获取系统状态
2025-08-04 22:00:42 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 35,
  "network": 50.50325420206143,
  "connectedClients": 2
}
2025-08-04 22:00:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:00:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:00:45 [DEBUG]: 获取系统状态
2025-08-04 22:00:45 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 26.70280435598371,
  "connectedClients": 2
}
2025-08-04 22:00:48 [DEBUG]: 获取系统状态
2025-08-04 22:00:48 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 36,
  "network": 24.21533743520921,
  "connectedClients": 2
}
2025-08-04 22:00:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:00:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:00:51 [DEBUG]: 获取系统状态
2025-08-04 22:00:51 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 28.398487152024614,
  "connectedClients": 2
}
2025-08-04 22:00:54 [DEBUG]: 获取系统状态
2025-08-04 22:00:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 43.93141744698908,
  "connectedClients": 2
}
2025-08-04 22:00:55 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:00:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:00:55 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:00:57 [DEBUG]: 获取系统状态
2025-08-04 22:00:57 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 24.972888756858108,
  "connectedClients": 2
}
2025-08-04 22:01:00 [DEBUG]: 获取地理位置数据
2025-08-04 22:01:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:00 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:01:00 [DEBUG]: 获取系统状态
2025-08-04 22:01:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:01:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 43.83757420983971,
  "connectedClients": 2
}
2025-08-04 22:01:03 [DEBUG]: 获取系统状态
2025-08-04 22:01:03 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 6.490536772637707,
  "connectedClients": 2
}
2025-08-04 22:01:05 [DEBUG]: 获取地理位置数据
2025-08-04 22:01:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:01:06 [DEBUG]: 获取系统状态
2025-08-04 22:01:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 35.99249125508938,
  "connectedClients": 2
}
2025-08-04 22:01:09 [DEBUG]: 获取系统状态
2025-08-04 22:01:09 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 35,
  "network": 37.768425220217004,
  "connectedClients": 2
}
2025-08-04 22:01:10 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:01:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:10 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:01:12 [DEBUG]: 获取系统状态
2025-08-04 22:01:12 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 35,
  "network": 37.013854184397005,
  "connectedClients": 2
}
2025-08-04 22:01:15 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:01:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:15 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:01:15 [DEBUG]: 获取系统状态
2025-08-04 22:01:15 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 35,
  "network": 6.581432595711174,
  "connectedClients": 2
}
2025-08-04 22:01:18 [DEBUG]: 获取系统状态
2025-08-04 22:01:18 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 36,
  "network": 37.48545983399177,
  "connectedClients": 2
}
2025-08-04 22:01:19 [INFO]: 客户端断开连接
{
  "clientId": "smfyXJSa-DQ_xmJXAAAB",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:01:19 [INFO]: 客户端断开连接
{
  "clientId": "pD8n1sfTazPT5OMnAAAD",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:01:20 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:01:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:01:20 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:01:21 [DEBUG]: 获取系统状态
2025-08-04 22:01:21 [DEBUG]: 系统指标
{
  "cpu": 21,
  "memory": 37,
  "network": 51.98686790945988,
  "connectedClients": 0
}
2025-08-04 22:01:21 [INFO]: 客户端连接
{
  "clientId": "qU29q15yW7BfwzMHAAAF",
  "totalClients": 1
}
2025-08-04 22:01:21 [INFO]: 客户端连接
{
  "clientId": "UuIw9IPccMQl8jaOAAAH",
  "totalClients": 2
}
2025-08-04 22:01:24 [DEBUG]: 获取系统状态
2025-08-04 22:01:24 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 16.509723348396207,
  "connectedClients": 2
}
2025-08-04 22:01:25 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:01:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:25 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:01:27 [DEBUG]: 获取系统状态
2025-08-04 22:01:27 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 50.85834915901369,
  "connectedClients": 2
}
2025-08-04 22:01:30 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:01:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:30 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:01:30 [DEBUG]: 获取系统状态
2025-08-04 22:01:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:01:30 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 47.89363096233462,
  "connectedClients": 2
}
2025-08-04 22:01:33 [DEBUG]: 获取系统状态
2025-08-04 22:01:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 26.996308545891164,
  "connectedClients": 2
}
2025-08-04 22:01:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:01:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:01:36 [DEBUG]: 获取系统状态
2025-08-04 22:01:36 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 42.038132898172435,
  "connectedClients": 2
}
2025-08-04 22:01:39 [DEBUG]: 获取系统状态
2025-08-04 22:01:39 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 44.67356148351412,
  "connectedClients": 2
}
2025-08-04 22:01:40 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:01:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:40 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:01:42 [DEBUG]: 获取系统状态
2025-08-04 22:01:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 35.97749417170783,
  "connectedClients": 2
}
2025-08-04 22:01:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:01:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:01:45 [DEBUG]: 获取系统状态
2025-08-04 22:01:45 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 39.820378104921545,
  "connectedClients": 2
}
2025-08-04 22:01:48 [DEBUG]: 获取系统状态
2025-08-04 22:01:48 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 25.157978210443396,
  "connectedClients": 2
}
2025-08-04 22:01:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:01:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:01:51 [DEBUG]: 获取系统状态
2025-08-04 22:01:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 50.468096759812994,
  "connectedClients": 2
}
2025-08-04 22:01:54 [DEBUG]: 获取系统状态
2025-08-04 22:01:54 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 16.3429742841765,
  "connectedClients": 2
}
2025-08-04 22:01:55 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:01:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:01:55 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:01:57 [DEBUG]: 获取系统状态
2025-08-04 22:01:57 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 15.64325835011184,
  "connectedClients": 2
}
2025-08-04 22:02:00 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:02:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:00 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:02:00 [DEBUG]: 获取系统状态
2025-08-04 22:02:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:02:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 30.834464438702643,
  "connectedClients": 2
}
2025-08-04 22:02:03 [DEBUG]: 获取系统状态
2025-08-04 22:02:03 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 20.66763973769918,
  "connectedClients": 2
}
2025-08-04 22:02:05 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:02:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:05 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:02:06 [DEBUG]: 获取系统状态
2025-08-04 22:02:06 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 49.03589464460912,
  "connectedClients": 2
}
2025-08-04 22:02:09 [DEBUG]: 获取系统状态
2025-08-04 22:02:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 43.1780709442492,
  "connectedClients": 2
}
2025-08-04 22:02:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:02:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:02:12 [DEBUG]: 获取系统状态
2025-08-04 22:02:12 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 29.48167095464029,
  "connectedClients": 2
}
2025-08-04 22:02:15 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:02:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:15 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:02:15 [DEBUG]: 获取系统状态
2025-08-04 22:02:15 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 39.261324780988666,
  "connectedClients": 2
}
2025-08-04 22:02:18 [DEBUG]: 获取系统状态
2025-08-04 22:02:18 [DEBUG]: 系统指标
{
  "cpu": 1,
  "memory": 37,
  "network": 41.08439106354096,
  "connectedClients": 2
}
2025-08-04 22:02:20 [DEBUG]: 获取地理位置数据
2025-08-04 22:02:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:20 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:02:21 [DEBUG]: 获取系统状态
2025-08-04 22:02:21 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 6.098215504300903,
  "connectedClients": 2
}
2025-08-04 22:02:24 [DEBUG]: 获取系统状态
2025-08-04 22:02:24 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 21.385910742295128,
  "connectedClients": 2
}
2025-08-04 22:02:25 [DEBUG]: 获取地理位置数据
2025-08-04 22:02:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:25 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:02:27 [DEBUG]: 获取系统状态
2025-08-04 22:02:27 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 41.59391191408936,
  "connectedClients": 2
}
2025-08-04 22:02:30 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:02:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:30 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:02:30 [DEBUG]: 获取系统状态
2025-08-04 22:02:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:02:30 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 8.83938246259049,
  "connectedClients": 2
}
2025-08-04 22:02:33 [DEBUG]: 获取系统状态
2025-08-04 22:02:33 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 41.62775367307923,
  "connectedClients": 2
}
2025-08-04 22:02:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:02:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:02:36 [DEBUG]: 获取系统状态
2025-08-04 22:02:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 34.53104036331834,
  "connectedClients": 2
}
2025-08-04 22:02:39 [DEBUG]: 获取系统状态
2025-08-04 22:02:39 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 31.879209729123954,
  "connectedClients": 2
}
2025-08-04 22:02:40 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:02:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:40 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:02:42 [DEBUG]: 获取系统状态
2025-08-04 22:02:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 11.92259481041595,
  "connectedClients": 2
}
2025-08-04 22:02:45 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:02:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:45 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:02:45 [DEBUG]: 获取系统状态
2025-08-04 22:02:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 9.466492593516602,
  "connectedClients": 2
}
2025-08-04 22:02:48 [DEBUG]: 获取系统状态
2025-08-04 22:02:48 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 10.1081154423936,
  "connectedClients": 2
}
2025-08-04 22:02:50 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:02:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:50 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:02:51 [DEBUG]: 获取系统状态
2025-08-04 22:02:51 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 37,
  "network": 26.130765458572597,
  "connectedClients": 2
}
2025-08-04 22:02:54 [DEBUG]: 获取系统状态
2025-08-04 22:02:54 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 28.57496445538286,
  "connectedClients": 2
}
2025-08-04 22:02:55 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:02:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:02:55 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:02:57 [DEBUG]: 获取系统状态
2025-08-04 22:02:57 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 53.836400799439495,
  "connectedClients": 2
}
2025-08-04 22:03:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:03:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:03:00 [DEBUG]: 获取系统状态
2025-08-04 22:03:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:03:00 [DEBUG]: 系统指标
{
  "cpu": 1,
  "memory": 37,
  "network": 11.726568367233298,
  "connectedClients": 2
}
2025-08-04 22:03:03 [DEBUG]: 获取系统状态
2025-08-04 22:03:03 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 10.424949314730988,
  "connectedClients": 2
}
2025-08-04 22:03:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:03:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:03:06 [DEBUG]: 获取系统状态
2025-08-04 22:03:06 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 14.00558217949018,
  "connectedClients": 2
}
2025-08-04 22:03:09 [DEBUG]: 获取系统状态
2025-08-04 22:03:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 5.537275488816613,
  "connectedClients": 2
}
2025-08-04 22:03:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:03:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:03:12 [DEBUG]: 获取系统状态
2025-08-04 22:03:12 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 25.132473965695674,
  "connectedClients": 2
}
2025-08-04 22:03:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:03:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:03:15 [DEBUG]: 获取系统状态
2025-08-04 22:03:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 24.757933369157165,
  "connectedClients": 2
}
2025-08-04 22:03:18 [DEBUG]: 获取系统状态
2025-08-04 22:03:18 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 37,
  "network": 25.580655138444016,
  "connectedClients": 2
}
2025-08-04 22:03:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:03:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:03:21 [DEBUG]: 获取系统状态
2025-08-04 22:03:21 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 36,
  "network": 52.27477014539748,
  "connectedClients": 2
}
2025-08-04 22:03:24 [DEBUG]: 获取系统状态
2025-08-04 22:03:24 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 53.92648633050497,
  "connectedClients": 2
}
2025-08-04 22:03:25 [DEBUG]: 获取地理位置数据
2025-08-04 22:03:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:25 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:03:27 [DEBUG]: 获取系统状态
2025-08-04 22:03:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 6.563569275065646,
  "connectedClients": 2
}
2025-08-04 22:03:30 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:03:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:30 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:03:30 [DEBUG]: 获取系统状态
2025-08-04 22:03:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:03:30 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 37,
  "network": 10.50284280475023,
  "connectedClients": 2
}
2025-08-04 22:03:33 [DEBUG]: 获取系统状态
2025-08-04 22:03:33 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 36,
  "network": 49.22061222518118,
  "connectedClients": 2
}
2025-08-04 22:03:35 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:03:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:35 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:03:36 [DEBUG]: 获取系统状态
2025-08-04 22:03:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 44.14794928125238,
  "connectedClients": 2
}
2025-08-04 22:03:39 [DEBUG]: 获取系统状态
2025-08-04 22:03:39 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 18.133603867052667,
  "connectedClients": 2
}
2025-08-04 22:03:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:03:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:03:42 [DEBUG]: 获取系统状态
2025-08-04 22:03:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 37.11049606064418,
  "connectedClients": 2
}
2025-08-04 22:03:45 [INFO]: 客户端断开连接
{
  "clientId": "qU29q15yW7BfwzMHAAAF",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:03:45 [INFO]: 客户端断开连接
{
  "clientId": "UuIw9IPccMQl8jaOAAAH",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:03:45 [DEBUG]: 获取地理位置数据
2025-08-04 22:03:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:03:45 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:03:45 [DEBUG]: 获取系统状态
2025-08-04 22:03:45 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 10.516440291645342,
  "connectedClients": 0
}
2025-08-04 22:03:47 [INFO]: 客户端连接
{
  "clientId": "v6lQjKG6xSLvAIEnAAAJ",
  "totalClients": 1
}
2025-08-04 22:03:47 [INFO]: 客户端连接
{
  "clientId": "nRWe0ImNbmpR_3oPAAAL",
  "totalClients": 2
}
2025-08-04 22:03:48 [DEBUG]: 获取系统状态
2025-08-04 22:03:48 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 6.979681497985775,
  "connectedClients": 2
}
2025-08-04 22:03:50 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:03:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:50 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:03:51 [DEBUG]: 获取系统状态
2025-08-04 22:03:51 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 25.24808723314402,
  "connectedClients": 2
}
2025-08-04 22:03:54 [DEBUG]: 获取系统状态
2025-08-04 22:03:54 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 47.61520891304178,
  "connectedClients": 2
}
2025-08-04 22:03:55 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:03:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:03:55 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:03:57 [DEBUG]: 获取系统状态
2025-08-04 22:03:58 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 7.469546356359409,
  "connectedClients": 2
}
2025-08-04 22:04:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:04:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:04:00 [DEBUG]: 获取系统状态
2025-08-04 22:04:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:04:01 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 24.038198923867977,
  "connectedClients": 2
}
2025-08-04 22:04:03 [DEBUG]: 获取系统状态
2025-08-04 22:04:04 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 33.859052470546786,
  "connectedClients": 2
}
2025-08-04 22:04:05 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:04:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:05 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:04:06 [DEBUG]: 获取系统状态
2025-08-04 22:04:07 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 36,
  "network": 45.99798835639689,
  "connectedClients": 2
}
2025-08-04 22:04:09 [DEBUG]: 获取系统状态
2025-08-04 22:04:10 [DEBUG]: 系统指标
{
  "cpu": 12,
  "memory": 36,
  "network": 10.80130103698544,
  "connectedClients": 2
}
2025-08-04 22:04:10 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:04:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:10 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:04:12 [DEBUG]: 获取系统状态
2025-08-04 22:04:13 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 48.74751610020152,
  "connectedClients": 2
}
2025-08-04 22:04:15 [DEBUG]: 获取地理位置数据
2025-08-04 22:04:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:15 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:04:15 [DEBUG]: 获取系统状态
2025-08-04 22:04:16 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 5.111709497184409,
  "connectedClients": 2
}
2025-08-04 22:04:18 [DEBUG]: 获取系统状态
2025-08-04 22:04:19 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 8.389717978218943,
  "connectedClients": 2
}
2025-08-04 22:04:20 [DEBUG]: 获取地理位置数据
2025-08-04 22:04:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:20 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:04:21 [DEBUG]: 获取系统状态
2025-08-04 22:04:22 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 6.2664659981593385,
  "connectedClients": 2
}
2025-08-04 22:04:24 [DEBUG]: 获取系统状态
2025-08-04 22:04:25 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 42.57092509167018,
  "connectedClients": 2
}
2025-08-04 22:04:25 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:04:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:25 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:04:27 [DEBUG]: 获取系统状态
2025-08-04 22:04:28 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 11.874289273334744,
  "connectedClients": 2
}
2025-08-04 22:04:30 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:04:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:30 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:04:30 [DEBUG]: 获取系统状态
2025-08-04 22:04:31 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 6.863559976307149,
  "connectedClients": 2
}
2025-08-04 22:04:33 [DEBUG]: 获取系统状态
2025-08-04 22:04:34 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 25.284250777751666,
  "connectedClients": 2
}
2025-08-04 22:04:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:04:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:04:36 [DEBUG]: 获取系统状态
2025-08-04 22:04:37 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 26.620093813266205,
  "connectedClients": 2
}
2025-08-04 22:04:39 [DEBUG]: 获取系统状态
2025-08-04 22:04:40 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 7.817202476857084,
  "connectedClients": 2
}
2025-08-04 22:04:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:04:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:04:42 [DEBUG]: 获取系统状态
2025-08-04 22:04:43 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 38.86811241075313,
  "connectedClients": 2
}
2025-08-04 22:04:48 [DEBUG]: 获取系统状态
2025-08-04 22:04:48 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 15.953056307069698,
  "connectedClients": 2
}
2025-08-04 22:04:50 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:04:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:50 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:04:51 [DEBUG]: 获取系统状态
2025-08-04 22:04:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 27.699611757972782,
  "connectedClients": 2
}
2025-08-04 22:04:54 [DEBUG]: 获取系统状态
2025-08-04 22:04:54 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 12.226106774067198,
  "connectedClients": 2
}
2025-08-04 22:04:55 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:04:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:04:55 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:04:57 [DEBUG]: 获取系统状态
2025-08-04 22:04:57 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 41.3252225060839,
  "connectedClients": 2
}
2025-08-04 22:05:00 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:05:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:00 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:05:00 [DEBUG]: 获取系统状态
2025-08-04 22:05:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:05:00 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 44.57536525278357,
  "connectedClients": 2
}
2025-08-04 22:05:03 [DEBUG]: 获取系统状态
2025-08-04 22:05:03 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 32.86571761312804,
  "connectedClients": 2
}
2025-08-04 22:05:05 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:05:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:05 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:05:06 [DEBUG]: 获取系统状态
2025-08-04 22:05:06 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 35.010184565328856,
  "connectedClients": 2
}
2025-08-04 22:05:09 [DEBUG]: 获取系统状态
2025-08-04 22:05:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 40.27639678192175,
  "connectedClients": 2
}
2025-08-04 22:05:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:05:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:05:12 [DEBUG]: 获取系统状态
2025-08-04 22:05:12 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 48.21793714692929,
  "connectedClients": 2
}
2025-08-04 22:05:15 [DEBUG]: 获取地理位置数据
2025-08-04 22:05:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:15 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:05:15 [DEBUG]: 获取系统状态
2025-08-04 22:05:15 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 36,
  "network": 35.279124506507756,
  "connectedClients": 2
}
2025-08-04 22:05:18 [DEBUG]: 获取系统状态
2025-08-04 22:05:18 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 36,
  "network": 29.60957341041525,
  "connectedClients": 2
}
2025-08-04 22:05:20 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:05:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:20 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:05:21 [DEBUG]: 获取系统状态
2025-08-04 22:05:21 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 36,
  "network": 8.23735637243081,
  "connectedClients": 2
}
2025-08-04 22:05:24 [DEBUG]: 获取系统状态
2025-08-04 22:05:24 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 49.80939300543588,
  "connectedClients": 2
}
2025-08-04 22:05:25 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:05:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:25 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:05:27 [DEBUG]: 获取系统状态
2025-08-04 22:05:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 5.363330105037218,
  "connectedClients": 2
}
2025-08-04 22:05:30 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:05:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:30 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:05:30 [DEBUG]: 获取系统状态
2025-08-04 22:05:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:05:30 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 24.303416231064784,
  "connectedClients": 2
}
2025-08-04 22:05:33 [DEBUG]: 获取系统状态
2025-08-04 22:05:33 [DEBUG]: 系统指标
{
  "cpu": 17,
  "memory": 36,
  "network": 24.262298018892132,
  "connectedClients": 2
}
2025-08-04 22:05:35 [DEBUG]: 获取地理位置数据
2025-08-04 22:05:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:35 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:05:36 [DEBUG]: 获取系统状态
2025-08-04 22:05:36 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 37,
  "network": 14.914810078505266,
  "connectedClients": 2
}
2025-08-04 22:05:39 [DEBUG]: 获取系统状态
2025-08-04 22:05:39 [DEBUG]: 系统指标
{
  "cpu": 19,
  "memory": 36,
  "network": 54.939936267678306,
  "connectedClients": 2
}
2025-08-04 22:05:40 [DEBUG]: 获取地理位置数据
2025-08-04 22:05:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:40 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:05:40 [INFO]: 客户端断开连接
{
  "clientId": "v6lQjKG6xSLvAIEnAAAJ",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:05:40 [INFO]: 客户端断开连接
{
  "clientId": "nRWe0ImNbmpR_3oPAAAL",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:05:41 [INFO]: 客户端连接
{
  "clientId": "AuHWu77WYzS9jyknAAAN",
  "totalClients": 1
}
2025-08-04 22:05:41 [INFO]: 客户端连接
{
  "clientId": "NPHSJloWx-cKwSX3AAAP",
  "totalClients": 2
}
2025-08-04 22:05:42 [DEBUG]: 获取系统状态
2025-08-04 22:05:42 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 37,
  "network": 34.78160635099761,
  "connectedClients": 2
}
2025-08-04 22:05:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:05:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:45 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:05:45 [DEBUG]: 获取系统状态
2025-08-04 22:05:45 [DEBUG]: 系统指标
{
  "cpu": 17,
  "memory": 37,
  "network": 15.475585014687827,
  "connectedClients": 2
}
2025-08-04 22:05:48 [DEBUG]: 获取系统状态
2025-08-04 22:05:48 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 39,
  "network": 40.73043862261901,
  "connectedClients": 2
}
2025-08-04 22:05:50 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:05:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:50 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:05:51 [DEBUG]: 获取系统状态
2025-08-04 22:05:51 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 39,
  "network": 20.53229933852852,
  "connectedClients": 2
}
2025-08-04 22:05:54 [DEBUG]: 获取系统状态
2025-08-04 22:05:54 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 39,
  "network": 49.26594102227862,
  "connectedClients": 2
}
2025-08-04 22:05:55 [DEBUG]: 获取地理位置数据
2025-08-04 22:05:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:05:55 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:05:57 [DEBUG]: 获取系统状态
2025-08-04 22:05:57 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 37,
  "network": 46.90149514165549,
  "connectedClients": 2
}
2025-08-04 22:05:57 [INFO]: 客户端断开连接
{
  "clientId": "AuHWu77WYzS9jyknAAAN",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:05:57 [INFO]: 客户端断开连接
{
  "clientId": "NPHSJloWx-cKwSX3AAAP",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:05:59 [INFO]: 客户端连接
{
  "clientId": "vFKc1sL_E_ebJwTkAAAR",
  "totalClients": 1
}
2025-08-04 22:05:59 [INFO]: 客户端连接
{
  "clientId": "vtj3XFLvOrV5cLPnAAAT",
  "totalClients": 2
}
2025-08-04 22:06:00 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:06:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:00 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:06:00 [DEBUG]: 获取系统状态
2025-08-04 22:06:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:06:00 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 9.19237955979439,
  "connectedClients": 2
}
2025-08-04 22:06:03 [DEBUG]: 获取系统状态
2025-08-04 22:06:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 6.026793521100777,
  "connectedClients": 2
}
2025-08-04 22:06:05 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:06:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:05 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:06:06 [DEBUG]: 获取系统状态
2025-08-04 22:06:06 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 17.906380284795315,
  "connectedClients": 2
}
2025-08-04 22:06:09 [DEBUG]: 获取系统状态
2025-08-04 22:06:10 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:06:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:10 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:06:10 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 36,
  "network": 31.627253717631106,
  "connectedClients": 2
}
2025-08-04 22:06:12 [DEBUG]: 获取系统状态
2025-08-04 22:06:13 [DEBUG]: 系统指标
{
  "cpu": 28,
  "memory": 36,
  "network": 16.1800608878033,
  "connectedClients": 2
}
2025-08-04 22:06:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:06:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:06:15 [DEBUG]: 获取系统状态
2025-08-04 22:06:15 [DEBUG]: 系统指标
{
  "cpu": 22,
  "memory": 36,
  "network": 39.47213721380011,
  "connectedClients": 2
}
2025-08-04 22:06:18 [DEBUG]: 获取系统状态
2025-08-04 22:06:18 [DEBUG]: 系统指标
{
  "cpu": 18,
  "memory": 36,
  "network": 53.1812667104217,
  "connectedClients": 2
}
2025-08-04 22:06:20 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:06:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:20 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:06:21 [DEBUG]: 获取系统状态
2025-08-04 22:06:21 [DEBUG]: 系统指标
{
  "cpu": 18,
  "memory": 36,
  "network": 7.378952691219116,
  "connectedClients": 2
}
2025-08-04 22:06:24 [DEBUG]: 获取系统状态
2025-08-04 22:06:24 [DEBUG]: 系统指标
{
  "cpu": 26,
  "memory": 36,
  "network": 39.03636131303108,
  "connectedClients": 2
}
2025-08-04 22:06:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:06:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:06:27 [DEBUG]: 获取系统状态
2025-08-04 22:06:27 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 30.640524200517422,
  "connectedClients": 2
}
2025-08-04 22:06:30 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:06:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:30 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:06:30 [DEBUG]: 获取系统状态
2025-08-04 22:06:30 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 26.43101092474439,
  "connectedClients": 2
}
2025-08-04 22:06:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:06:33 [DEBUG]: 获取系统状态
2025-08-04 22:06:33 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 38.59539610270056,
  "connectedClients": 2
}
2025-08-04 22:06:35 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:06:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:35 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:06:36 [DEBUG]: 获取系统状态
2025-08-04 22:06:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 9.47131469208395,
  "connectedClients": 2
}
2025-08-04 22:06:39 [DEBUG]: 获取系统状态
2025-08-04 22:06:39 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 48.08418118414449,
  "connectedClients": 2
}
2025-08-04 22:06:40 [DEBUG]: 获取地理位置数据
2025-08-04 22:06:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:40 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:06:42 [DEBUG]: 获取系统状态
2025-08-04 22:06:42 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 36.190423850209896,
  "connectedClients": 2
}
2025-08-04 22:06:45 [DEBUG]: 获取地理位置数据
2025-08-04 22:06:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:45 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:06:45 [DEBUG]: 获取系统状态
2025-08-04 22:06:45 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 7.927887391005413,
  "connectedClients": 2
}
2025-08-04 22:06:48 [DEBUG]: 获取系统状态
2025-08-04 22:06:48 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 36,
  "network": 40.98303236875958,
  "connectedClients": 2
}
2025-08-04 22:06:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:06:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:06:51 [DEBUG]: 获取系统状态
2025-08-04 22:06:51 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 37.131424617702606,
  "connectedClients": 2
}
2025-08-04 22:06:54 [DEBUG]: 获取系统状态
2025-08-04 22:06:54 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 36,
  "network": 36.33097259849511,
  "connectedClients": 2
}
2025-08-04 22:06:55 [DEBUG]: 获取地理位置数据
2025-08-04 22:06:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:06:55 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:06:57 [DEBUG]: 获取系统状态
2025-08-04 22:06:57 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 36,
  "network": 52.61205689015564,
  "connectedClients": 2
}
2025-08-04 22:07:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:07:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:07:00 [DEBUG]: 获取系统状态
2025-08-04 22:07:00 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 22.070644434529548,
  "connectedClients": 2
}
2025-08-04 22:07:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:07:03 [DEBUG]: 获取系统状态
2025-08-04 22:07:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 40.58471088140605,
  "connectedClients": 2
}
2025-08-04 22:07:05 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:07:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:05 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:07:06 [DEBUG]: 获取系统状态
2025-08-04 22:07:06 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 13.85751793972136,
  "connectedClients": 2
}
2025-08-04 22:07:09 [DEBUG]: 获取系统状态
2025-08-04 22:07:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 54.06738786601321,
  "connectedClients": 2
}
2025-08-04 22:07:10 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:07:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:10 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:07:11 [INFO]: 客户端断开连接
{
  "clientId": "vFKc1sL_E_ebJwTkAAAR",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:07:11 [INFO]: 客户端断开连接
{
  "clientId": "vtj3XFLvOrV5cLPnAAAT",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:07:12 [DEBUG]: 获取系统状态
2025-08-04 22:07:12 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 40.36848125111567,
  "connectedClients": 0
}
2025-08-04 22:07:13 [INFO]: 客户端连接
{
  "clientId": "hNChXvtrLZglKJFYAAAV",
  "totalClients": 1
}
2025-08-04 22:07:13 [INFO]: 客户端连接
{
  "clientId": "AAlpMu3m8G3b-B53AAAX",
  "totalClients": 2
}
2025-08-04 22:07:15 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:07:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:15 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:07:15 [DEBUG]: 获取系统状态
2025-08-04 22:07:15 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 36,
  "network": 19.340981629184025,
  "connectedClients": 2
}
2025-08-04 22:07:18 [DEBUG]: 获取系统状态
2025-08-04 22:07:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 9.512782058679644,
  "connectedClients": 2
}
2025-08-04 22:07:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:07:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:07:21 [DEBUG]: 获取系统状态
2025-08-04 22:07:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 27.89939334524933,
  "connectedClients": 2
}
2025-08-04 22:07:24 [DEBUG]: 获取系统状态
2025-08-04 22:07:24 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 47.141149565012306,
  "connectedClients": 2
}
2025-08-04 22:07:25 [DEBUG]: 获取地理位置数据
2025-08-04 22:07:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:25 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:07:27 [DEBUG]: 获取系统状态
2025-08-04 22:07:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 27.350545123473253,
  "connectedClients": 2
}
2025-08-04 22:07:30 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:07:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:30 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:07:30 [DEBUG]: 获取系统状态
2025-08-04 22:07:30 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 37,
  "network": 40.18336986864069,
  "connectedClients": 2
}
2025-08-04 22:07:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:07:33 [DEBUG]: 获取系统状态
2025-08-04 22:07:33 [DEBUG]: 系统指标
{
  "cpu": 28,
  "memory": 37,
  "network": 8.69372859982825,
  "connectedClients": 2
}
2025-08-04 22:07:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:07:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:07:36 [DEBUG]: 获取系统状态
2025-08-04 22:07:36 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 53.59972611601185,
  "connectedClients": 2
}
2025-08-04 22:07:39 [DEBUG]: 获取系统状态
2025-08-04 22:07:39 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 38,
  "network": 44.76900797083014,
  "connectedClients": 2
}
2025-08-04 22:07:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:07:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:07:42 [DEBUG]: 获取系统状态
2025-08-04 22:07:42 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 37,
  "network": 49.740910467583106,
  "connectedClients": 2
}
2025-08-04 22:07:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:07:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:07:45 [DEBUG]: 获取系统状态
2025-08-04 22:07:45 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 52.41670600123628,
  "connectedClients": 2
}
2025-08-04 22:07:48 [DEBUG]: 获取系统状态
2025-08-04 22:07:48 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 41.99133625036467,
  "connectedClients": 2
}
2025-08-04 22:07:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:07:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:07:51 [DEBUG]: 获取系统状态
2025-08-04 22:07:51 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 14.157976260288459,
  "connectedClients": 2
}
2025-08-04 22:07:54 [DEBUG]: 获取系统状态
2025-08-04 22:07:54 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 52.549960203574884,
  "connectedClients": 2
}
2025-08-04 22:07:55 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:07:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:07:55 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:07:57 [DEBUG]: 获取系统状态
2025-08-04 22:07:57 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 16.28488281355203,
  "connectedClients": 2
}
2025-08-04 22:08:00 [DEBUG]: 获取地理位置数据
2025-08-04 22:08:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:00 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:08:00 [DEBUG]: 获取系统状态
2025-08-04 22:08:00 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 20.348957477213112,
  "connectedClients": 2
}
2025-08-04 22:08:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:08:03 [DEBUG]: 获取系统状态
2025-08-04 22:08:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 52.7126823943568,
  "connectedClients": 2
}
2025-08-04 22:08:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:08:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:08:06 [DEBUG]: 获取系统状态
2025-08-04 22:08:06 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 46.39600466349989,
  "connectedClients": 2
}
2025-08-04 22:08:09 [DEBUG]: 获取系统状态
2025-08-04 22:08:09 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 38,
  "network": 14.400016239294938,
  "connectedClients": 2
}
2025-08-04 22:08:10 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:08:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:10 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:08:12 [DEBUG]: 获取系统状态
2025-08-04 22:08:12 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 18.5622175177601,
  "connectedClients": 2
}
2025-08-04 22:08:15 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:08:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:15 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:08:15 [DEBUG]: 获取系统状态
2025-08-04 22:08:15 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 36,
  "network": 43.33003345535509,
  "connectedClients": 2
}
2025-08-04 22:08:18 [DEBUG]: 获取系统状态
2025-08-04 22:08:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 16.961939387505403,
  "connectedClients": 2
}
2025-08-04 22:08:20 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:08:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:20 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:08:21 [DEBUG]: 获取系统状态
2025-08-04 22:08:21 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 23.300662107260536,
  "connectedClients": 2
}
2025-08-04 22:08:24 [DEBUG]: 获取系统状态
2025-08-04 22:08:24 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 31.91519542474842,
  "connectedClients": 2
}
2025-08-04 22:08:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:08:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:08:27 [DEBUG]: 获取系统状态
2025-08-04 22:08:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 17.38235004041135,
  "connectedClients": 2
}
2025-08-04 22:08:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:08:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:08:30 [DEBUG]: 获取系统状态
2025-08-04 22:08:30 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 50.28978084508911,
  "connectedClients": 2
}
2025-08-04 22:08:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:08:33 [DEBUG]: 获取系统状态
2025-08-04 22:08:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 12.788627107738543,
  "connectedClients": 2
}
2025-08-04 22:08:35 [DEBUG]: 获取地理位置数据
2025-08-04 22:08:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:35 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:08:36 [DEBUG]: 获取系统状态
2025-08-04 22:08:36 [DEBUG]: 系统指标
{
  "cpu": 19,
  "memory": 36,
  "network": 48.487960261476374,
  "connectedClients": 2
}
2025-08-04 22:08:39 [DEBUG]: 获取系统状态
2025-08-04 22:08:39 [DEBUG]: 系统指标
{
  "cpu": 19,
  "memory": 36,
  "network": 48.63395118455019,
  "connectedClients": 2
}
2025-08-04 22:08:40 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:08:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:40 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:08:42 [DEBUG]: 获取系统状态
2025-08-04 22:08:42 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 51.68804531139962,
  "connectedClients": 2
}
2025-08-04 22:08:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:08:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:45 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:08:45 [DEBUG]: 获取系统状态
2025-08-04 22:08:45 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 48.88294268991363,
  "connectedClients": 2
}
2025-08-04 22:08:48 [DEBUG]: 获取系统状态
2025-08-04 22:08:48 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 39.82762850730721,
  "connectedClients": 2
}
2025-08-04 22:08:50 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:08:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:50 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:08:51 [DEBUG]: 获取系统状态
2025-08-04 22:08:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 15.163927792280047,
  "connectedClients": 2
}
2025-08-04 22:08:54 [DEBUG]: 获取系统状态
2025-08-04 22:08:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 38.894324438051584,
  "connectedClients": 2
}
2025-08-04 22:08:55 [DEBUG]: 获取地理位置数据
2025-08-04 22:08:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:08:55 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:08:57 [DEBUG]: 获取系统状态
2025-08-04 22:08:57 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 31.10213600545872,
  "connectedClients": 2
}
2025-08-04 22:09:00 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:09:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:09:00 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:09:00 [DEBUG]: 获取系统状态
2025-08-04 22:09:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 24.603910371162215,
  "connectedClients": 2
}
2025-08-04 22:09:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:09:03 [DEBUG]: 获取系统状态
2025-08-04 22:09:03 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 53.48393011636048,
  "connectedClients": 2
}
2025-08-04 22:09:05 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:09:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:09:05 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:09:06 [DEBUG]: 获取系统状态
2025-08-04 22:09:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 27.886695642325893,
  "connectedClients": 2
}
2025-08-04 22:09:09 [DEBUG]: 获取系统状态
2025-08-04 22:09:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 54.74447417016502,
  "connectedClients": 2
}
2025-08-04 22:09:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:09:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:09:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:09:12 [DEBUG]: 获取系统状态
2025-08-04 22:09:12 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 36,
  "network": 32.39649425526576,
  "connectedClients": 2
}
2025-08-04 22:09:13 [INFO]: 客户端断开连接
{
  "clientId": "hNChXvtrLZglKJFYAAAV",
  "reason": "transport close",
  "totalClients": 1
}
2025-08-04 22:09:13 [INFO]: 客户端断开连接
{
  "clientId": "AAlpMu3m8G3b-B53AAAX",
  "reason": "transport close",
  "totalClients": 0
}
2025-08-04 22:09:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:09:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:09:15 [DEBUG]: 获取系统状态
2025-08-04 22:09:15 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 36,
  "network": 48.66796355099799,
  "connectedClients": 0
}
2025-08-04 22:09:18 [DEBUG]: 获取系统状态
2025-08-04 22:09:18 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 46.02745273754203,
  "connectedClients": 0
}
2025-08-04 22:09:20 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:09:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:20 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:09:21 [DEBUG]: 获取系统状态
2025-08-04 22:09:21 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 30.073470551513477,
  "connectedClients": 0
}
2025-08-04 22:09:24 [DEBUG]: 获取系统状态
2025-08-04 22:09:24 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 36.646439156684394,
  "connectedClients": 0
}
2025-08-04 22:09:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:09:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:09:27 [DEBUG]: 获取系统状态
2025-08-04 22:09:27 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 18.11764309081803,
  "connectedClients": 0
}
2025-08-04 22:09:30 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:09:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:30 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:09:30 [DEBUG]: 获取系统状态
2025-08-04 22:09:30 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 8.20610485510664,
  "connectedClients": 0
}
2025-08-04 22:09:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:09:33 [DEBUG]: 获取系统状态
2025-08-04 22:09:33 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 15.917293391322858,
  "connectedClients": 0
}
2025-08-04 22:09:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:09:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:09:36 [DEBUG]: 获取系统状态
2025-08-04 22:09:36 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 32.521447271913146,
  "connectedClients": 0
}
2025-08-04 22:09:39 [DEBUG]: 获取系统状态
2025-08-04 22:09:39 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 39.407964580449644,
  "connectedClients": 0
}
2025-08-04 22:09:40 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:09:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:40 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:09:42 [DEBUG]: 获取系统状态
2025-08-04 22:09:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 13.269211287127998,
  "connectedClients": 0
}
2025-08-04 22:09:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:09:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:45 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:09:45 [DEBUG]: 获取系统状态
2025-08-04 22:09:45 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 45.94024693527945,
  "connectedClients": 0
}
2025-08-04 22:09:48 [DEBUG]: 获取系统状态
2025-08-04 22:09:48 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 11.989257915704295,
  "connectedClients": 0
}
2025-08-04 22:09:50 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:09:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:50 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:09:51 [DEBUG]: 获取系统状态
2025-08-04 22:09:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 45.693736261817634,
  "connectedClients": 0
}
2025-08-04 22:09:54 [DEBUG]: 获取系统状态
2025-08-04 22:09:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 12.056523226506883,
  "connectedClients": 0
}
2025-08-04 22:09:55 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:09:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:09:55 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:09:57 [DEBUG]: 获取系统状态
2025-08-04 22:09:57 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 36,
  "network": 29.240329994158706,
  "connectedClients": 0
}
2025-08-04 22:10:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:10:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:10:00 [DEBUG]: 获取系统状态
2025-08-04 22:10:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 36,
  "network": 24.337107261201137,
  "connectedClients": 0
}
2025-08-04 22:10:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:10:03 [DEBUG]: 获取系统状态
2025-08-04 22:10:03 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 37.624755614563625,
  "connectedClients": 0
}
2025-08-04 22:10:05 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:10:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:05 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:10:06 [DEBUG]: 获取系统状态
2025-08-04 22:10:06 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 12.107254901927773,
  "connectedClients": 0
}
2025-08-04 22:10:09 [DEBUG]: 获取系统状态
2025-08-04 22:10:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 7.08049431610905,
  "connectedClients": 0
}
2025-08-04 22:10:10 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:10:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:10 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:10:12 [DEBUG]: 获取系统状态
2025-08-04 22:10:12 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 17.712425054688858,
  "connectedClients": 0
}
2025-08-04 22:10:15 [DEBUG]: 获取地理位置数据
2025-08-04 22:10:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:15 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:10:15 [DEBUG]: 获取系统状态
2025-08-04 22:10:15 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 18.5030882427141,
  "connectedClients": 0
}
2025-08-04 22:10:18 [DEBUG]: 获取系统状态
2025-08-04 22:10:18 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 37,
  "network": 36.93780322605913,
  "connectedClients": 0
}
2025-08-04 22:10:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:10:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:10:21 [DEBUG]: 获取系统状态
2025-08-04 22:10:21 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 37,
  "network": 42.53131059759587,
  "connectedClients": 0
}
2025-08-04 22:10:24 [DEBUG]: 获取系统状态
2025-08-04 22:10:24 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 38.732769231448245,
  "connectedClients": 0
}
2025-08-04 22:10:25 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:10:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:25 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:10:27 [DEBUG]: 获取系统状态
2025-08-04 22:10:27 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 24.93142561753052,
  "connectedClients": 0
}
2025-08-04 22:10:30 [DEBUG]: 获取地理位置数据
2025-08-04 22:10:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:30 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:10:30 [DEBUG]: 获取系统状态
2025-08-04 22:10:30 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 54.40163499758394,
  "connectedClients": 0
}
2025-08-04 22:10:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:10:33 [DEBUG]: 获取系统状态
2025-08-04 22:10:33 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 36,
  "network": 21.505537374537283,
  "connectedClients": 0
}
2025-08-04 22:10:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:10:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:10:36 [DEBUG]: 获取系统状态
2025-08-04 22:10:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 41.225515162182184,
  "connectedClients": 0
}
2025-08-04 22:10:39 [DEBUG]: 获取系统状态
2025-08-04 22:10:39 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 10.822973319438972,
  "connectedClients": 0
}
2025-08-04 22:10:40 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:10:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:40 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:10:42 [DEBUG]: 获取系统状态
2025-08-04 22:10:42 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 36,
  "network": 17.996772816478586,
  "connectedClients": 0
}
2025-08-04 22:10:45 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:10:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:45 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:10:45 [DEBUG]: 获取系统状态
2025-08-04 22:10:45 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 36,
  "network": 30.677549828110845,
  "connectedClients": 0
}
2025-08-04 22:10:48 [DEBUG]: 获取系统状态
2025-08-04 22:10:48 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 37,
  "network": 24.98314754991053,
  "connectedClients": 0
}
2025-08-04 22:10:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:10:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:10:51 [DEBUG]: 获取系统状态
2025-08-04 22:10:51 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 48.34427466298193,
  "connectedClients": 0
}
2025-08-04 22:10:54 [DEBUG]: 获取系统状态
2025-08-04 22:10:54 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 36,
  "network": 13.876941628329272,
  "connectedClients": 0
}
2025-08-04 22:10:55 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:10:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:10:55 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:10:57 [DEBUG]: 获取系统状态
2025-08-04 22:10:57 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 43.90002859868446,
  "connectedClients": 0
}
2025-08-04 22:11:00 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:00 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:11:00 [DEBUG]: 获取系统状态
2025-08-04 22:11:00 [DEBUG]: 系统指标
{
  "cpu": 22,
  "memory": 37,
  "network": 54.22340205267185,
  "connectedClients": 0
}
2025-08-04 22:11:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:11:03 [DEBUG]: 获取系统状态
2025-08-04 22:11:03 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 14.286834499281426,
  "connectedClients": 0
}
2025-08-04 22:11:05 [DEBUG]: 获取地理位置数据
2025-08-04 22:11:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:11:06 [DEBUG]: 获取系统状态
2025-08-04 22:11:06 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 37,
  "network": 48.99862789526793,
  "connectedClients": 0
}
2025-08-04 22:11:09 [DEBUG]: 获取系统状态
2025-08-04 22:11:09 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 30.17422032295078,
  "connectedClients": 0
}
2025-08-04 22:11:10 [DEBUG]: 获取地理位置数据
2025-08-04 22:11:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:10 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:11:12 [DEBUG]: 获取系统状态
2025-08-04 22:11:12 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 23.929061408782243,
  "connectedClients": 0
}
2025-08-04 22:11:15 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:15 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:11:15 [DEBUG]: 获取系统状态
2025-08-04 22:11:15 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 36.9501766425106,
  "connectedClients": 0
}
2025-08-04 22:11:18 [DEBUG]: 获取系统状态
2025-08-04 22:11:18 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 8.847362328524573,
  "connectedClients": 0
}
2025-08-04 22:11:20 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:20 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:11:21 [DEBUG]: 获取系统状态
2025-08-04 22:11:21 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 50.7480893982563,
  "connectedClients": 0
}
2025-08-04 22:11:24 [DEBUG]: 获取系统状态
2025-08-04 22:11:24 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 15.628428289002922,
  "connectedClients": 0
}
2025-08-04 22:11:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:11:27 [DEBUG]: 获取系统状态
2025-08-04 22:11:27 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 32.216751774216085,
  "connectedClients": 0
}
2025-08-04 22:11:30 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:11:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:30 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:11:30 [DEBUG]: 获取系统状态
2025-08-04 22:11:30 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 32.67685511384693,
  "connectedClients": 0
}
2025-08-04 22:11:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:11:33 [DEBUG]: 获取系统状态
2025-08-04 22:11:33 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 9.75611642703444,
  "connectedClients": 0
}
2025-08-04 22:11:35 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:35 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:11:36 [DEBUG]: 获取系统状态
2025-08-04 22:11:36 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 21.360625342175005,
  "connectedClients": 0
}
2025-08-04 22:11:39 [DEBUG]: 获取系统状态
2025-08-04 22:11:39 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 36,
  "network": 47.15330891313271,
  "connectedClients": 0
}
2025-08-04 22:11:40 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:11:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:40 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:11:42 [DEBUG]: 获取系统状态
2025-08-04 22:11:42 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 5.787188095256908,
  "connectedClients": 0
}
2025-08-04 22:11:45 [DEBUG]: 获取地理位置数据
2025-08-04 22:11:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:45 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:11:45 [DEBUG]: 获取系统状态
2025-08-04 22:11:45 [DEBUG]: 系统指标
{
  "cpu": 12,
  "memory": 37,
  "network": 13.047888981727668,
  "connectedClients": 0
}
2025-08-04 22:11:48 [DEBUG]: 获取系统状态
2025-08-04 22:11:48 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 37,
  "network": 33.94690076442438,
  "connectedClients": 0
}
2025-08-04 22:11:50 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:50 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:11:51 [DEBUG]: 获取系统状态
2025-08-04 22:11:51 [DEBUG]: 系统指标
{
  "cpu": 14,
  "memory": 37,
  "network": 30.874180898504672,
  "connectedClients": 0
}
2025-08-04 22:11:54 [DEBUG]: 获取系统状态
2025-08-04 22:11:54 [DEBUG]: 系统指标
{
  "cpu": 22,
  "memory": 37,
  "network": 30.30567631085208,
  "connectedClients": 0
}
2025-08-04 22:11:55 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:11:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:11:55 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:11:57 [DEBUG]: 获取系统状态
2025-08-04 22:11:57 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 37,
  "network": 32.338749370454686,
  "connectedClients": 0
}
2025-08-04 22:12:00 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:12:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:12:00 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:12:00 [DEBUG]: 获取系统状态
2025-08-04 22:12:00 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 10.686191190909963,
  "connectedClients": 0
}
2025-08-04 22:12:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 0
}
2025-08-04 22:12:03 [DEBUG]: 获取系统状态
2025-08-04 22:12:03 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 20.51059369235277,
  "connectedClients": 0
}
2025-08-04 22:12:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:12:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:12:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:12:06 [DEBUG]: 获取系统状态
2025-08-04 22:12:06 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 18.806000291785878,
  "connectedClients": 0
}
2025-08-04 22:12:09 [DEBUG]: 获取系统状态
2025-08-04 22:12:09 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 34.171942021027995,
  "connectedClients": 0
}
2025-08-04 22:12:10 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:12:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:12:10 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:12:12 [DEBUG]: 获取系统状态
2025-08-04 22:12:12 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 37,
  "network": 23.723527649982962,
  "connectedClients": 0
}
2025-08-04 22:12:15 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:12:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 0
}
2025-08-04 22:12:15 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:12:15 [DEBUG]: 获取系统状态
2025-08-04 22:12:15 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 37,
  "network": 7.716706253885337,
  "connectedClients": 0
}
2025-08-04 22:12:18 [DEBUG]: 获取系统状态
2025-08-04 22:12:18 [DEBUG]: 系统指标
{
  "cpu": 50,
  "memory": 39,
  "network": 48.3099627150201,
  "connectedClients": 0
}
2025-08-04 22:12:20 [INFO]: 客户端连接
{
  "clientId": "FP1H_w8Pm8At-dATAAAZ",
  "totalClients": 1
}
2025-08-04 22:12:20 [INFO]: 客户端连接
{
  "clientId": "lc9gQ5batheKU1QKAAAb",
  "totalClients": 2
}
2025-08-04 22:12:20 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:12:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:20 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:12:21 [DEBUG]: 获取系统状态
2025-08-04 22:12:21 [DEBUG]: 系统指标
{
  "cpu": 18,
  "memory": 39,
  "network": 21.292415979052265,
  "connectedClients": 2
}
2025-08-04 22:12:24 [DEBUG]: 获取系统状态
2025-08-04 22:12:24 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 39,
  "network": 32.387792406556954,
  "connectedClients": 2
}
2025-08-04 22:12:25 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:12:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:25 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:12:27 [DEBUG]: 获取系统状态
2025-08-04 22:12:27 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 39,
  "network": 35.848048735339795,
  "connectedClients": 2
}
2025-08-04 22:12:30 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:12:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:30 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:12:30 [DEBUG]: 获取系统状态
2025-08-04 22:12:30 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 39,
  "network": 35.022586447505944,
  "connectedClients": 2
}
2025-08-04 22:12:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:12:33 [DEBUG]: 获取系统状态
2025-08-04 22:12:33 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 39,
  "network": 32.16665406842901,
  "connectedClients": 2
}
2025-08-04 22:12:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:12:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:12:36 [DEBUG]: 获取系统状态
2025-08-04 22:12:36 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 39,
  "network": 28.0622162295112,
  "connectedClients": 2
}
2025-08-04 22:12:39 [DEBUG]: 获取系统状态
2025-08-04 22:12:39 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 51.048922554735064,
  "connectedClients": 2
}
2025-08-04 22:12:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:12:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:12:42 [DEBUG]: 获取系统状态
2025-08-04 22:12:42 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 47.24568250973694,
  "connectedClients": 2
}
2025-08-04 22:12:45 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:12:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:45 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:12:45 [DEBUG]: 获取系统状态
2025-08-04 22:12:45 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 39,
  "network": 43.88987770581879,
  "connectedClients": 2
}
2025-08-04 22:12:48 [DEBUG]: 获取系统状态
2025-08-04 22:12:48 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 10.412130789409208,
  "connectedClients": 2
}
2025-08-04 22:12:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:12:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:12:51 [DEBUG]: 获取系统状态
2025-08-04 22:12:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 49.31373635231331,
  "connectedClients": 2
}
2025-08-04 22:12:54 [DEBUG]: 获取系统状态
2025-08-04 22:12:54 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 39,
  "network": 21.649290713137,
  "connectedClients": 2
}
2025-08-04 22:12:55 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:12:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:12:55 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:12:57 [DEBUG]: 获取系统状态
2025-08-04 22:12:57 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 37,
  "network": 24.548071148285107,
  "connectedClients": 2
}
2025-08-04 22:13:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 2
}
2025-08-04 22:13:00 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:13:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:13:00 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:13:00 [DEBUG]: 获取系统状态
2025-08-04 22:13:00 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 6.108129915910016,
  "connectedClients": 2
}
2025-08-04 22:13:03 [DEBUG]: 获取系统状态
2025-08-04 22:13:03 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 52.11388324522851,
  "connectedClients": 2
}
2025-08-04 22:13:05 [DEBUG]: 获取地理位置数据
2025-08-04 22:13:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:13:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:13:06 [INFO]: 客户端连接
{
  "clientId": "jKB521uj2BxA4PhwAAAd",
  "totalClients": 3
}
2025-08-04 22:13:06 [INFO]: 客户端连接
{
  "clientId": "NeTKFWR6ej2gZCjdAAAf",
  "totalClients": 4
}
2025-08-04 22:13:06 [DEBUG]: 获取系统状态
2025-08-04 22:13:06 [DEBUG]: 系统指标
{
  "cpu": 13,
  "memory": 37,
  "network": 10.139599058086908,
  "connectedClients": 4
}
2025-08-04 22:13:09 [INFO]: 客户端断开连接
{
  "clientId": "jKB521uj2BxA4PhwAAAd",
  "reason": "transport close",
  "totalClients": 3
}
2025-08-04 22:13:09 [INFO]: 客户端断开连接
{
  "clientId": "NeTKFWR6ej2gZCjdAAAf",
  "reason": "transport close",
  "totalClients": 2
}
2025-08-04 22:13:09 [DEBUG]: 获取系统状态
2025-08-04 22:13:09 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 11.955165235251748,
  "connectedClients": 2
}
2025-08-04 22:13:10 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:13:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:13:10 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:13:11 [INFO]: 客户端连接
{
  "clientId": "aQnmsTtHBGBbYy1CAAAh",
  "totalClients": 3
}
2025-08-04 22:13:11 [INFO]: 客户端连接
{
  "clientId": "MAVL-JGShXA9iWGpAAAj",
  "totalClients": 4
}
2025-08-04 22:13:12 [DEBUG]: 获取系统状态
2025-08-04 22:13:12 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 17.4077557748258,
  "connectedClients": 4
}
2025-08-04 22:13:15 [DEBUG]: 获取地理位置数据
2025-08-04 22:13:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 4
}
2025-08-04 22:13:15 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:13:15 [DEBUG]: 获取系统状态
2025-08-04 22:13:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 14.668039195277556,
  "connectedClients": 4
}
2025-08-04 22:13:18 [DEBUG]: 获取系统状态
2025-08-04 22:13:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 31.017630974215866,
  "connectedClients": 4
}
2025-08-04 22:13:20 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:13:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 4
}
2025-08-04 22:13:20 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:13:21 [DEBUG]: 获取系统状态
2025-08-04 22:13:21 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 6.323781309414662,
  "connectedClients": 4
}
2025-08-04 22:13:24 [DEBUG]: 获取系统状态
2025-08-04 22:13:24 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 18.281506483656592,
  "connectedClients": 4
}
2025-08-04 22:13:25 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:13:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 4
}
2025-08-04 22:13:25 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:13:27 [DEBUG]: 获取系统状态
2025-08-04 22:13:27 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 28.21722509622678,
  "connectedClients": 4
}
2025-08-04 22:13:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 4
}
2025-08-04 22:13:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:13:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 4
}
2025-08-04 22:13:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:13:30 [DEBUG]: 获取系统状态
2025-08-04 22:13:30 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 28.742895352315298,
  "connectedClients": 4
}
2025-08-04 22:13:31 [INFO]: 客户端连接
{
  "clientId": "4hknvGCXXa4QkJc8AAAl",
  "totalClients": 5
}
2025-08-04 22:13:31 [INFO]: 客户端连接
{
  "clientId": "IvsLBMCMop11lhijAAAn",
  "totalClients": 6
}
2025-08-04 22:13:33 [DEBUG]: 获取系统状态
2025-08-04 22:13:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 46.89740569428244,
  "connectedClients": 6
}
2025-08-04 22:13:35 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:13:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:13:35 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:13:36 [DEBUG]: 获取系统状态
2025-08-04 22:13:36 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 19.281396150087676,
  "connectedClients": 6
}
2025-08-04 22:13:39 [DEBUG]: 获取系统状态
2025-08-04 22:13:39 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 22.851272186921165,
  "connectedClients": 6
}
2025-08-04 22:13:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:13:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:13:40 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:13:42 [DEBUG]: 获取系统状态
2025-08-04 22:13:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 16.261980341298326,
  "connectedClients": 6
}
2025-08-04 22:13:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:13:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:13:45 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:13:45 [DEBUG]: 获取系统状态
2025-08-04 22:13:45 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 23.418281975058758,
  "connectedClients": 6
}
2025-08-04 22:13:48 [DEBUG]: 获取系统状态
2025-08-04 22:13:48 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 51.818552166915865,
  "connectedClients": 6
}
2025-08-04 22:13:50 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:13:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:13:50 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:13:51 [DEBUG]: 获取系统状态
2025-08-04 22:13:51 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 44.41408288985129,
  "connectedClients": 6
}
2025-08-04 22:13:54 [DEBUG]: 获取系统状态
2025-08-04 22:13:54 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 9.133626708073434,
  "connectedClients": 6
}
2025-08-04 22:13:55 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:13:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:13:55 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:13:57 [DEBUG]: 获取系统状态
2025-08-04 22:13:57 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 20.738176180244523,
  "connectedClients": 6
}
2025-08-04 22:14:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:14:00 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:14:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:00 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:14:00 [DEBUG]: 获取系统状态
2025-08-04 22:14:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 35.77167490850441,
  "connectedClients": 6
}
2025-08-04 22:14:03 [DEBUG]: 获取系统状态
2025-08-04 22:14:03 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 24.713355150929214,
  "connectedClients": 6
}
2025-08-04 22:14:05 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:14:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:05 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:14:06 [DEBUG]: 获取系统状态
2025-08-04 22:14:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 46.42746441561288,
  "connectedClients": 6
}
2025-08-04 22:14:09 [DEBUG]: 获取系统状态
2025-08-04 22:14:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 23.95514216995403,
  "connectedClients": 6
}
2025-08-04 22:14:10 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:14:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:10 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:14:12 [DEBUG]: 获取系统状态
2025-08-04 22:14:12 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 52.25480453098404,
  "connectedClients": 6
}
2025-08-04 22:14:15 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:14:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:15 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:14:15 [DEBUG]: 获取系统状态
2025-08-04 22:14:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 15.013128256056847,
  "connectedClients": 6
}
2025-08-04 22:14:18 [DEBUG]: 获取系统状态
2025-08-04 22:14:18 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 33.62041567126303,
  "connectedClients": 6
}
2025-08-04 22:14:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:14:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:14:21 [DEBUG]: 获取系统状态
2025-08-04 22:14:21 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 19.261621806819356,
  "connectedClients": 6
}
2025-08-04 22:14:24 [DEBUG]: 获取系统状态
2025-08-04 22:14:24 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 37,
  "network": 15.896698814609863,
  "connectedClients": 6
}
2025-08-04 22:14:25 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:14:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:25 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:14:27 [DEBUG]: 获取系统状态
2025-08-04 22:14:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 23.509006167163843,
  "connectedClients": 6
}
2025-08-04 22:14:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:14:30 [DEBUG]: 获取地理位置数据
2025-08-04 22:14:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:30 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:14:30 [DEBUG]: 获取系统状态
2025-08-04 22:14:30 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 21.010029536337257,
  "connectedClients": 6
}
2025-08-04 22:14:33 [DEBUG]: 获取系统状态
2025-08-04 22:14:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 53.611938121973175,
  "connectedClients": 6
}
2025-08-04 22:14:35 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:14:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:35 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:14:36 [DEBUG]: 获取系统状态
2025-08-04 22:14:36 [DEBUG]: 系统指标
{
  "cpu": 18,
  "memory": 37,
  "network": 42.734987427260776,
  "connectedClients": 6
}
2025-08-04 22:14:38 [INFO]: 客户端断开连接
{
  "clientId": "IvsLBMCMop11lhijAAAn",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:14:38 [INFO]: 客户端断开连接
{
  "clientId": "4hknvGCXXa4QkJc8AAAl",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:14:38 [INFO]: 客户端断开连接
{
  "clientId": "aQnmsTtHBGBbYy1CAAAh",
  "reason": "transport close",
  "totalClients": 3
}
2025-08-04 22:14:38 [INFO]: 客户端断开连接
{
  "clientId": "MAVL-JGShXA9iWGpAAAj",
  "reason": "transport close",
  "totalClients": 2
}
2025-08-04 22:14:39 [DEBUG]: 获取系统状态
2025-08-04 22:14:39 [DEBUG]: 系统指标
{
  "cpu": 41,
  "memory": 39,
  "network": 18.307805277592287,
  "connectedClients": 2
}
2025-08-04 22:14:40 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:14:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:14:40 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:14:40 [INFO]: 客户端连接
{
  "clientId": "duhmguP3i8tdTHqRAAAp",
  "totalClients": 3
}
2025-08-04 22:14:40 [INFO]: 客户端连接
{
  "clientId": "ZCDMrY0xyGPaiT8jAAAr",
  "totalClients": 4
}
2025-08-04 22:14:41 [INFO]: 客户端连接
{
  "clientId": "MqvSU-s3xqMuTUNuAAAt",
  "totalClients": 5
}
2025-08-04 22:14:41 [INFO]: 客户端连接
{
  "clientId": "2sKJ2Zy6dsDO-BejAAAv",
  "totalClients": 6
}
2025-08-04 22:14:42 [DEBUG]: 获取系统状态
2025-08-04 22:14:42 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 39,
  "network": 34.65341873057831,
  "connectedClients": 6
}
2025-08-04 22:14:45 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:14:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:45 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:14:45 [DEBUG]: 获取系统状态
2025-08-04 22:14:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 26.678559742905357,
  "connectedClients": 6
}
2025-08-04 22:14:48 [DEBUG]: 获取系统状态
2025-08-04 22:14:48 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 23.403338901926723,
  "connectedClients": 6
}
2025-08-04 22:14:49 [INFO]: 客户端断开连接
{
  "clientId": "ZCDMrY0xyGPaiT8jAAAr",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:14:49 [INFO]: 客户端断开连接
{
  "clientId": "duhmguP3i8tdTHqRAAAp",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:14:50 [INFO]: 客户端连接
{
  "clientId": "UZNZF5sdibkwI7O8AAAx",
  "totalClients": 5
}
2025-08-04 22:14:50 [INFO]: 客户端连接
{
  "clientId": "hYG5ScIRKAO7e-iSAAAz",
  "totalClients": 6
}
2025-08-04 22:14:50 [DEBUG]: 获取地理位置数据
2025-08-04 22:14:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:50 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:14:51 [DEBUG]: 获取系统状态
2025-08-04 22:14:51 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 8.627579321809282,
  "connectedClients": 6
}
2025-08-04 22:14:54 [DEBUG]: 获取系统状态
2025-08-04 22:14:54 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 19.27642943871349,
  "connectedClients": 6
}
2025-08-04 22:14:55 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:14:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:14:55 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:14:57 [DEBUG]: 获取系统状态
2025-08-04 22:14:57 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 39,
  "network": 12.049852100589815,
  "connectedClients": 6
}
2025-08-04 22:15:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:15:00 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:15:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:00 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:15:00 [DEBUG]: 获取系统状态
2025-08-04 22:15:00 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 39,
  "network": 42.79987159079712,
  "connectedClients": 6
}
2025-08-04 22:15:03 [DEBUG]: 获取系统状态
2025-08-04 22:15:03 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 45.78283312293568,
  "connectedClients": 6
}
2025-08-04 22:15:05 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:15:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:05 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:15:06 [DEBUG]: 获取系统状态
2025-08-04 22:15:06 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 29.068844646369346,
  "connectedClients": 6
}
2025-08-04 22:15:09 [DEBUG]: 获取系统状态
2025-08-04 22:15:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 7.262930245104256,
  "connectedClients": 6
}
2025-08-04 22:15:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:15:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:15:12 [DEBUG]: 获取系统状态
2025-08-04 22:15:12 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 21.77855967787847,
  "connectedClients": 6
}
2025-08-04 22:15:14 [INFO]: 客户端断开连接
{
  "clientId": "UZNZF5sdibkwI7O8AAAx",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:15:14 [INFO]: 客户端断开连接
{
  "clientId": "hYG5ScIRKAO7e-iSAAAz",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:15:15 [INFO]: 客户端连接
{
  "clientId": "N1Q-5I2IGAczNfpBAAA1",
  "totalClients": 5
}
2025-08-04 22:15:15 [INFO]: 客户端连接
{
  "clientId": "0opGdrghXGCiTuyDAAA3",
  "totalClients": 6
}
2025-08-04 22:15:15 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:15:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:15 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:15:15 [DEBUG]: 获取系统状态
2025-08-04 22:15:15 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 39,
  "network": 20.57820278005199,
  "connectedClients": 6
}
2025-08-04 22:15:18 [DEBUG]: 获取系统状态
2025-08-04 22:15:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 20.418959228278585,
  "connectedClients": 6
}
2025-08-04 22:15:20 [DEBUG]: 获取地理位置数据
2025-08-04 22:15:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:20 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:15:21 [DEBUG]: 获取系统状态
2025-08-04 22:15:21 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 10.925121024917102,
  "connectedClients": 6
}
2025-08-04 22:15:24 [DEBUG]: 获取系统状态
2025-08-04 22:15:24 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 39,
  "network": 19.932740494961784,
  "connectedClients": 6
}
2025-08-04 22:15:25 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:15:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:25 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:15:27 [DEBUG]: 获取系统状态
2025-08-04 22:15:27 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 38,
  "network": 36.90642879941128,
  "connectedClients": 6
}
2025-08-04 22:15:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:15:30 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:15:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:30 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:15:30 [DEBUG]: 获取系统状态
2025-08-04 22:15:30 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 38,
  "network": 21.931670398256305,
  "connectedClients": 6
}
2025-08-04 22:15:33 [DEBUG]: 获取系统状态
2025-08-04 22:15:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 52.05432296528158,
  "connectedClients": 6
}
2025-08-04 22:15:35 [DEBUG]: 获取地理位置数据
2025-08-04 22:15:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:35 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:15:36 [DEBUG]: 获取系统状态
2025-08-04 22:15:36 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 38,
  "network": 37.63144638964797,
  "connectedClients": 6
}
2025-08-04 22:15:39 [DEBUG]: 获取系统状态
2025-08-04 22:15:39 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 14.652436736474733,
  "connectedClients": 6
}
2025-08-04 22:15:40 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:15:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:40 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:15:42 [DEBUG]: 获取系统状态
2025-08-04 22:15:42 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 47.238244741218445,
  "connectedClients": 6
}
2025-08-04 22:15:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:15:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:15:45 [DEBUG]: 获取系统状态
2025-08-04 22:15:45 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 38,
  "network": 53.17095412813618,
  "connectedClients": 6
}
2025-08-04 22:15:48 [DEBUG]: 获取系统状态
2025-08-04 22:15:48 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 38,
  "network": 26.37146937407315,
  "connectedClients": 6
}
2025-08-04 22:15:50 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:15:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:50 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:15:51 [DEBUG]: 获取系统状态
2025-08-04 22:15:51 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 38,
  "network": 16.617871065338512,
  "connectedClients": 6
}
2025-08-04 22:15:54 [DEBUG]: 获取系统状态
2025-08-04 22:15:54 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 38,
  "network": 10.612525505628463,
  "connectedClients": 6
}
2025-08-04 22:15:55 [DEBUG]: 获取地理位置数据
2025-08-04 22:15:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:15:55 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:15:57 [DEBUG]: 获取系统状态
2025-08-04 22:15:57 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 38,
  "network": 11.255363045025186,
  "connectedClients": 6
}
2025-08-04 22:16:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:16:00 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:16:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:00 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:16:00 [DEBUG]: 获取系统状态
2025-08-04 22:16:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 38.88091629396121,
  "connectedClients": 6
}
2025-08-04 22:16:03 [DEBUG]: 获取系统状态
2025-08-04 22:16:03 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 38,
  "network": 22.137798585516396,
  "connectedClients": 6
}
2025-08-04 22:16:05 [DEBUG]: 获取地理位置数据
2025-08-04 22:16:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:05 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:16:06 [DEBUG]: 获取系统状态
2025-08-04 22:16:06 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 38,
  "network": 49.3023718993352,
  "connectedClients": 6
}
2025-08-04 22:16:09 [DEBUG]: 获取系统状态
2025-08-04 22:16:09 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 38,
  "network": 26.2653512131484,
  "connectedClients": 6
}
2025-08-04 22:16:10 [DEBUG]: 获取地理位置数据
2025-08-04 22:16:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:10 [DEBUG]: 数据更新完成
{
  "type": "locations"
}
2025-08-04 22:16:12 [DEBUG]: 获取系统状态
2025-08-04 22:16:12 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 38,
  "network": 38.244564745100924,
  "connectedClients": 6
}
2025-08-04 22:16:15 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:16:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:15 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:16:15 [DEBUG]: 获取系统状态
2025-08-04 22:16:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 38,
  "network": 44.743044864980405,
  "connectedClients": 6
}
2025-08-04 22:16:18 [DEBUG]: 获取系统状态
2025-08-04 22:16:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 29.16167783779457,
  "connectedClients": 6
}
2025-08-04 22:16:20 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:16:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:20 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:16:21 [DEBUG]: 获取系统状态
2025-08-04 22:16:21 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 38,
  "network": 48.23309704334354,
  "connectedClients": 6
}
2025-08-04 22:16:24 [DEBUG]: 获取系统状态
2025-08-04 22:16:24 [DEBUG]: 系统指标
{
  "cpu": 8,
  "memory": 39,
  "network": 32.031479289614026,
  "connectedClients": 6
}
2025-08-04 22:16:25 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:16:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:25 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:16:27 [DEBUG]: 获取系统状态
2025-08-04 22:16:27 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 5.727654602770366,
  "connectedClients": 6
}
2025-08-04 22:16:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:16:30 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:16:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:30 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:16:30 [DEBUG]: 获取系统状态
2025-08-04 22:16:30 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 51.61480231597383,
  "connectedClients": 6
}
2025-08-04 22:16:33 [DEBUG]: 获取系统状态
2025-08-04 22:16:33 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 39,
  "network": 7.098744141570775,
  "connectedClients": 6
}
2025-08-04 22:16:35 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:16:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:35 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:16:36 [DEBUG]: 获取系统状态
2025-08-04 22:16:36 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 39,
  "network": 23.9781860187767,
  "connectedClients": 6
}
2025-08-04 22:16:39 [DEBUG]: 获取系统状态
2025-08-04 22:16:40 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 38,
  "network": 47.1478470056818,
  "connectedClients": 6
}
2025-08-04 22:16:40 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:16:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:40 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:16:42 [DEBUG]: 获取系统状态
2025-08-04 22:16:42 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 33.84037399475916,
  "connectedClients": 6
}
2025-08-04 22:16:45 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:16:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:45 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:16:45 [DEBUG]: 获取系统状态
2025-08-04 22:16:46 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 8.139853374203938,
  "connectedClients": 6
}
2025-08-04 22:16:48 [DEBUG]: 获取系统状态
2025-08-04 22:16:49 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 8.506939817372356,
  "connectedClients": 6
}
2025-08-04 22:16:50 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:16:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:50 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:16:51 [DEBUG]: 获取系统状态
2025-08-04 22:16:52 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 22.465719146075042,
  "connectedClients": 6
}
2025-08-04 22:16:54 [DEBUG]: 获取系统状态
2025-08-04 22:16:55 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 5.014499012152025,
  "connectedClients": 6
}
2025-08-04 22:16:55 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:16:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:16:55 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:16:57 [DEBUG]: 获取系统状态
2025-08-04 22:16:58 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 38.620611240289136,
  "connectedClients": 6
}
2025-08-04 22:17:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:17:00 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:17:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:00 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:17:00 [DEBUG]: 获取系统状态
2025-08-04 22:17:01 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 26.91340404542069,
  "connectedClients": 6
}
2025-08-04 22:17:03 [DEBUG]: 获取系统状态
2025-08-04 22:17:04 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 35.6527865783211,
  "connectedClients": 6
}
2025-08-04 22:17:05 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:17:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:05 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:17:06 [DEBUG]: 获取系统状态
2025-08-04 22:17:07 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 29.93260676210808,
  "connectedClients": 6
}
2025-08-04 22:17:07 [INFO]: 客户端断开连接
{
  "clientId": "N1Q-5I2IGAczNfpBAAA1",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:17:07 [INFO]: 客户端断开连接
{
  "clientId": "0opGdrghXGCiTuyDAAA3",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:17:07 [INFO]: 客户端连接
{
  "clientId": "FCApc6uMTp0sN22SAAA5",
  "totalClients": 5
}
2025-08-04 22:17:07 [INFO]: 客户端连接
{
  "clientId": "KVGxibCkp4EX41ncAAA7",
  "totalClients": 6
}
2025-08-04 22:17:09 [DEBUG]: 获取系统状态
2025-08-04 22:17:10 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 9.495484448879338,
  "connectedClients": 6
}
2025-08-04 22:17:10 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:17:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:10 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:17:12 [DEBUG]: 获取系统状态
2025-08-04 22:17:13 [DEBUG]: 系统指标
{
  "cpu": 10,
  "memory": 37,
  "network": 23.533050860994294,
  "connectedClients": 6
}
2025-08-04 22:17:15 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:17:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:15 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:17:15 [DEBUG]: 获取系统状态
2025-08-04 22:17:16 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 37,
  "network": 9.950533362543174,
  "connectedClients": 6
}
2025-08-04 22:17:18 [DEBUG]: 获取系统状态
2025-08-04 22:17:19 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 43.8271331373946,
  "connectedClients": 6
}
2025-08-04 22:17:20 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:17:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:20 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:17:21 [DEBUG]: 获取系统状态
2025-08-04 22:17:22 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 37,
  "network": 42.89924281840563,
  "connectedClients": 6
}
2025-08-04 22:17:24 [DEBUG]: 获取系统状态
2025-08-04 22:17:25 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 48.706324901863724,
  "connectedClients": 6
}
2025-08-04 22:17:25 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:17:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:25 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:17:27 [DEBUG]: 获取系统状态
2025-08-04 22:17:28 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 32.80648163752644,
  "connectedClients": 6
}
2025-08-04 22:17:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:17:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:17:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:17:30 [DEBUG]: 获取系统状态
2025-08-04 22:17:31 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 7.348935441689637,
  "connectedClients": 6
}
2025-08-04 22:17:33 [DEBUG]: 获取系统状态
2025-08-04 22:17:34 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 46.81227922903458,
  "connectedClients": 6
}
2025-08-04 22:17:35 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:17:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:35 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:17:36 [DEBUG]: 获取系统状态
2025-08-04 22:17:37 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 37,
  "network": 10.790505210010199,
  "connectedClients": 6
}
2025-08-04 22:17:39 [DEBUG]: 获取系统状态
2025-08-04 22:17:40 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 36.77284491857195,
  "connectedClients": 6
}
2025-08-04 22:17:40 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:17:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:40 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:17:42 [DEBUG]: 获取系统状态
2025-08-04 22:17:43 [DEBUG]: 系统指标
{
  "cpu": 16,
  "memory": 37,
  "network": 25.25352176591585,
  "connectedClients": 6
}
2025-08-04 22:17:45 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:17:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:45 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:17:45 [DEBUG]: 获取系统状态
2025-08-04 22:17:46 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 37,
  "network": 12.110944699093459,
  "connectedClients": 6
}
2025-08-04 22:17:48 [DEBUG]: 获取系统状态
2025-08-04 22:17:49 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 37,
  "network": 44.51812902730575,
  "connectedClients": 6
}
2025-08-04 22:17:50 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:17:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:50 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:17:51 [DEBUG]: 获取系统状态
2025-08-04 22:17:52 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 37,
  "network": 25.87096360053534,
  "connectedClients": 6
}
2025-08-04 22:17:54 [DEBUG]: 获取系统状态
2025-08-04 22:17:55 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 13.342189115398435,
  "connectedClients": 6
}
2025-08-04 22:17:55 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:17:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:17:55 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:17:57 [DEBUG]: 获取系统状态
2025-08-04 22:17:58 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 27.147593625735883,
  "connectedClients": 6
}
2025-08-04 22:18:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:18:00 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:18:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:00 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:18:00 [DEBUG]: 获取系统状态
2025-08-04 22:18:01 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 37,
  "network": 15.946814971677842,
  "connectedClients": 6
}
2025-08-04 22:18:03 [DEBUG]: 获取系统状态
2025-08-04 22:18:04 [DEBUG]: 系统指标
{
  "cpu": 27,
  "memory": 39,
  "network": 12.513877310053019,
  "connectedClients": 6
}
2025-08-04 22:18:05 [INFO]: 客户端断开连接
{
  "clientId": "MqvSU-s3xqMuTUNuAAAt",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:18:05 [INFO]: 客户端断开连接
{
  "clientId": "2sKJ2Zy6dsDO-BejAAAv",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:18:05 [INFO]: 客户端断开连接
{
  "clientId": "FCApc6uMTp0sN22SAAA5",
  "reason": "transport close",
  "totalClients": 3
}
2025-08-04 22:18:05 [INFO]: 客户端断开连接
{
  "clientId": "KVGxibCkp4EX41ncAAA7",
  "reason": "transport close",
  "totalClients": 2
}
2025-08-04 22:18:05 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:18:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 2
}
2025-08-04 22:18:05 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:18:08 [INFO]: 客户端连接
{
  "clientId": "0S7ugOUi46rfyzzvAAA9",
  "totalClients": 3
}
2025-08-04 22:18:08 [INFO]: 客户端连接
{
  "clientId": "ds-QPiqmyMz95QXFAABA",
  "totalClients": 4
}
2025-08-04 22:18:08 [INFO]: 客户端连接
{
  "clientId": "loCA-ECTcxJgJeORAABB",
  "totalClients": 5
}
2025-08-04 22:18:08 [INFO]: 客户端连接
{
  "clientId": "pU0_Z8i4XJIMwtYYAABD",
  "totalClients": 6
}
2025-08-04 22:18:09 [DEBUG]: 获取系统状态
2025-08-04 22:18:09 [DEBUG]: 系统指标
{
  "cpu": 31,
  "memory": 39,
  "network": 48.03316599811916,
  "connectedClients": 6
}
2025-08-04 22:18:10 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:18:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:10 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:18:12 [DEBUG]: 获取系统状态
2025-08-04 22:18:12 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 39,
  "network": 5.3190088621428,
  "connectedClients": 6
}
2025-08-04 22:18:15 [DEBUG]: 获取系统状态
2025-08-04 22:18:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 7.358816562052068,
  "connectedClients": 6
}
2025-08-04 22:18:15 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:15 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:18:18 [DEBUG]: 获取系统状态
2025-08-04 22:18:18 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 32.8465502853929,
  "connectedClients": 6
}
2025-08-04 22:18:20 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:18:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:20 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:18:21 [DEBUG]: 获取系统状态
2025-08-04 22:18:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 17.121033361517597,
  "connectedClients": 6
}
2025-08-04 22:18:24 [DEBUG]: 获取系统状态
2025-08-04 22:18:24 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 32.1022027871919,
  "connectedClients": 6
}
2025-08-04 22:18:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:18:27 [DEBUG]: 获取系统状态
2025-08-04 22:18:27 [DEBUG]: 系统指标
{
  "cpu": 15,
  "memory": 39,
  "network": 26.48547058300857,
  "connectedClients": 6
}
2025-08-04 22:18:28 [INFO]: 客户端断开连接
{
  "clientId": "loCA-ECTcxJgJeORAABB",
  "reason": "transport close",
  "totalClients": 5
}
2025-08-04 22:18:28 [INFO]: 客户端断开连接
{
  "clientId": "pU0_Z8i4XJIMwtYYAABD",
  "reason": "transport close",
  "totalClients": 4
}
2025-08-04 22:18:30 [DEBUG]: 获取系统状态
2025-08-04 22:18:30 [DEBUG]: 系统指标
{
  "cpu": 11,
  "memory": 39,
  "network": 9.047986674432458,
  "connectedClients": 4
}
2025-08-04 22:18:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 4
}
2025-08-04 22:18:30 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 4
}
2025-08-04 22:18:30 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:18:32 [INFO]: 客户端连接
{
  "clientId": "PfcBqj9qx16DTyvBAABF",
  "totalClients": 5
}
2025-08-04 22:18:32 [INFO]: 客户端连接
{
  "clientId": "SMh7z8_2pXQkA1p8AABH",
  "totalClients": 6
}
2025-08-04 22:18:33 [DEBUG]: 获取系统状态
2025-08-04 22:18:33 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 35.09070029217084,
  "connectedClients": 6
}
2025-08-04 22:18:35 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:35 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:18:36 [DEBUG]: 获取系统状态
2025-08-04 22:18:36 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 40.851350415587866,
  "connectedClients": 6
}
2025-08-04 22:18:39 [DEBUG]: 获取系统状态
2025-08-04 22:18:39 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 39,
  "network": 28.169451946722553,
  "connectedClients": 6
}
2025-08-04 22:18:40 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:18:41 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:41 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:18:42 [DEBUG]: 获取系统状态
2025-08-04 22:18:42 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 5.918430719173435,
  "connectedClients": 6
}
2025-08-04 22:18:45 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:45 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:18:45 [DEBUG]: 获取系统状态
2025-08-04 22:18:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 47.574793504067564,
  "connectedClients": 6
}
2025-08-04 22:18:48 [DEBUG]: 获取系统状态
2025-08-04 22:18:48 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 37.52958152905566,
  "connectedClients": 6
}
2025-08-04 22:18:50 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:50 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:50 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:18:51 [DEBUG]: 获取系统状态
2025-08-04 22:18:51 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 15.714903393128594,
  "connectedClients": 6
}
2025-08-04 22:18:54 [DEBUG]: 获取系统状态
2025-08-04 22:18:54 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 39,
  "network": 33.30237329572225,
  "connectedClients": 6
}
2025-08-04 22:18:55 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:18:55 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:18:55 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:18:57 [DEBUG]: 获取系统状态
2025-08-04 22:18:57 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 42.6905421102286,
  "connectedClients": 6
}
2025-08-04 22:19:00 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:19:00 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:00 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:19:00 [DEBUG]: 获取系统状态
2025-08-04 22:19:00 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 37.24775444076854,
  "connectedClients": 6
}
2025-08-04 22:19:00 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:19:03 [DEBUG]: 获取系统状态
2025-08-04 22:19:03 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 27.33831186658192,
  "connectedClients": 6
}
2025-08-04 22:19:05 [DEBUG]: 获取时间序列数据
{
  "timeRange": "24h"
}
2025-08-04 22:19:05 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:05 [DEBUG]: 数据更新完成
{
  "type": "timeSeries"
}
2025-08-04 22:19:06 [DEBUG]: 获取系统状态
2025-08-04 22:19:06 [DEBUG]: 系统指标
{
  "cpu": 7,
  "memory": 39,
  "network": 30.296361536758127,
  "connectedClients": 6
}
2025-08-04 22:19:09 [DEBUG]: 获取系统状态
2025-08-04 22:19:09 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 49.164885889248225,
  "connectedClients": 6
}
2025-08-04 22:19:10 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:19:10 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:10 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:19:12 [DEBUG]: 获取系统状态
2025-08-04 22:19:12 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 39,
  "network": 24.09941109220805,
  "connectedClients": 6
}
2025-08-04 22:19:15 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:19:15 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:15 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:19:15 [DEBUG]: 获取系统状态
2025-08-04 22:19:15 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 41.590107737384194,
  "connectedClients": 6
}
2025-08-04 22:19:18 [DEBUG]: 获取系统状态
2025-08-04 22:19:18 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 19.093758135463574,
  "connectedClients": 6
}
2025-08-04 22:19:20 [DEBUG]: 获取最新帖子
{
  "limit": 1
}
2025-08-04 22:19:20 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:20 [DEBUG]: 数据更新完成
{
  "type": "newPost"
}
2025-08-04 22:19:21 [DEBUG]: 获取系统状态
2025-08-04 22:19:21 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 16.603314457630475,
  "connectedClients": 6
}
2025-08-04 22:19:24 [DEBUG]: 获取系统状态
2025-08-04 22:19:24 [DEBUG]: 系统指标
{
  "cpu": 6,
  "memory": 39,
  "network": 46.613524382417886,
  "connectedClients": 6
}
2025-08-04 22:19:25 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:19:25 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:25 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:19:27 [DEBUG]: 获取系统状态
2025-08-04 22:19:27 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 39.647698989057275,
  "connectedClients": 6
}
2025-08-04 22:19:30 [DEBUG]: 获取关键词
{
  "limit": 50
}
2025-08-04 22:19:30 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:30 [DEBUG]: 数据更新完成
{
  "type": "keywords"
}
2025-08-04 22:19:30 [DEBUG]: 获取系统状态
2025-08-04 22:19:30 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 35.2709227668029,
  "connectedClients": 6
}
2025-08-04 22:19:30 [DEBUG]: 广播消息
{
  "type": "heartbeat",
  "clients": 6
}
2025-08-04 22:19:33 [DEBUG]: 获取系统状态
2025-08-04 22:19:33 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 12.154567545933988,
  "connectedClients": 6
}
2025-08-04 22:19:35 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:19:35 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:35 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:19:36 [DEBUG]: 获取系统状态
2025-08-04 22:19:36 [DEBUG]: 系统指标
{
  "cpu": 9,
  "memory": 39,
  "network": 35.09685130981332,
  "connectedClients": 6
}
2025-08-04 22:19:39 [DEBUG]: 获取系统状态
2025-08-04 22:19:39 [DEBUG]: 系统指标
{
  "cpu": 2,
  "memory": 39,
  "network": 25.41155548363087,
  "connectedClients": 6
}
2025-08-04 22:19:40 [DEBUG]: 获取统计数据
{
  "timeRange": "24h"
}
2025-08-04 22:19:40 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:40 [DEBUG]: 数据更新完成
{
  "type": "statistics"
}
2025-08-04 22:19:42 [DEBUG]: 获取系统状态
2025-08-04 22:19:42 [DEBUG]: 系统指标
{
  "cpu": 4,
  "memory": 39,
  "network": 49.54835625572964,
  "connectedClients": 6
}
2025-08-04 22:19:45 [DEBUG]: 获取热点话题
{
  "limit": 10
}
2025-08-04 22:19:45 [DEBUG]: 广播消息
{
  "type": "update",
  "clients": 6
}
2025-08-04 22:19:45 [DEBUG]: 数据更新完成
{
  "type": "hotTopics"
}
2025-08-04 22:19:45 [DEBUG]: 获取系统状态
2025-08-04 22:19:45 [DEBUG]: 系统指标
{
  "cpu": 3,
  "memory": 39,
  "network": 50.55093512461291,
  "connectedClients": 6
}
2025-08-04 22:19:48 [DEBUG]: 获取系统状态
2025-08-04 22:19:48 [DEBUG]: 系统指标
{
  "cpu": 5,
  "memory": 39,
  "network": 8.933439622231372,
  "connectedClients": 6
}
