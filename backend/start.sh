#!/bin/bash

# 舆情监控大屏幕系统后端启动脚本

echo "🚀 启动舆情监控大屏幕系统后端服务..."

# 检查 Node.js 版本
node_version=$(node -v 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js (>= 16)"
    exit 1
fi

echo "✅ Node.js 版本: $node_version"

# 检查 npm 版本
npm_version=$(npm -v 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到 npm"
    exit 1
fi

echo "✅ npm 版本: $npm_version"

# 检查是否存在 node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在"
fi

# 创建日志目录
if [ ! -d "logs" ]; then
    mkdir -p logs
    echo "✅ 创建日志目录"
fi

# 检查 TypeScript 编译
echo "🔨 编译 TypeScript..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ TypeScript 编译失败"
    exit 1
fi
echo "✅ TypeScript 编译完成"

# 启动开发服务器
echo "🌟 启动开发服务器..."
echo "📡 HTTP 服务: http://localhost:8080"
echo "🔌 WebSocket 服务: ws://localhost:8080"
echo "🏥 健康检查: http://localhost:8080/health"
echo "📊 API 接口: http://localhost:8080/api"
echo "🔄 按 Ctrl+C 停止服务器"
echo ""

npm run dev
