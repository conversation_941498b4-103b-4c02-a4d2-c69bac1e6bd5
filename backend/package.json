{"name": "weibo-sentiment-backend", "version": "1.0.0", "description": "舆情监控大屏幕系统后端服务", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "install:pnpm": "pnpm install", "dev:pnpm": "pnpm run dev"}, "keywords": ["sentiment", "monitoring", "dashboard", "websocket", "api"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "node-cron": "^3.0.3", "redis": "^4.6.10", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "systeminformation": "^5.21.20"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/joi": "^17.2.3", "@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1"}, "engines": {"node": ">=16.0.0"}}