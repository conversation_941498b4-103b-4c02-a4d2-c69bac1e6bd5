import { Request, Response, NextFunction } from 'express';
import { log } from '../config/logger';

interface RateLimitOptions {
  windowMs: number; // 时间窗口（毫秒）
  max: number; // 最大请求数
  message?: string; // 错误消息
  skipSuccessfulRequests?: boolean; // 是否跳过成功请求
  skipFailedRequests?: boolean; // 是否跳过失败请求
  keyGenerator?: (req: Request) => string; // 键生成器
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// 内存存储（生产环境建议使用 Redis）
const store: RateLimitStore = {};

// 清理过期记录
setInterval(() => {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });
}, 60000); // 每分钟清理一次

export const rateLimit = (options: RateLimitOptions) => {
  const {
    windowMs,
    max,
    message = '请求过于频繁，请稍后再试',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator = (req: Request) => req.ip || 'unknown',
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    const key = keyGenerator(req);
    const now = Date.now();
    const resetTime = now + windowMs;

    // 获取或创建记录
    if (!store[key] || store[key].resetTime < now) {
      store[key] = {
        count: 0,
        resetTime,
      };
    }

    const record = store[key];

    // 检查是否超过限制
    if (record.count >= max) {
      log.warn('速率限制触发', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        count: record.count,
        max,
        resetTime: new Date(record.resetTime).toISOString(),
      });

      res.status(429).json({
        success: false,
        message,
        retryAfter: Math.ceil((record.resetTime - now) / 1000),
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // 增加计数
    record.count++;

    // 设置响应头
    res.set({
      'X-RateLimit-Limit': max.toString(),
      'X-RateLimit-Remaining': Math.max(0, max - record.count).toString(),
      'X-RateLimit-Reset': new Date(record.resetTime).toISOString(),
    });

    // 响应后处理
    const originalSend = res.send;
    res.send = function(body) {
      const statusCode = res.statusCode;
      
      // 根据配置决定是否计数
      if (
        (skipSuccessfulRequests && statusCode < 400) ||
        (skipFailedRequests && statusCode >= 400)
      ) {
        record.count--;
      }

      return originalSend.call(this, body);
    };

    next();
  };
};

// 创建不同级别的速率限制
export const createRateLimit = {
  // 严格限制
  strict: () => rateLimit({
    windowMs: 15 * 60 * 1000, // 15 分钟
    max: 50, // 50 个请求
    message: '请求过于频繁，请稍后再试',
  }),

  // 中等限制
  moderate: () => rateLimit({
    windowMs: 15 * 60 * 1000, // 15 分钟
    max: 100, // 100 个请求
    message: '请求过于频繁，请稍后再试',
  }),

  // 宽松限制
  lenient: () => rateLimit({
    windowMs: 15 * 60 * 1000, // 15 分钟
    max: 200, // 200 个请求
    message: '请求过于频繁，请稍后再试',
  }),

  // API 限制
  api: () => rateLimit({
    windowMs: 15 * 60 * 1000, // 15 分钟
    max: 1000, // 1000 个请求
    message: 'API 请求过于频繁，请稍后再试',
    skipSuccessfulRequests: true,
  }),
};
