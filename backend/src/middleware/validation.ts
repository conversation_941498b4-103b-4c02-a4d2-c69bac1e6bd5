import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { log } from '../config/logger';

// 验证请求体
export const validateBody = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      log.warn('请求体验证失败', { 
        path: req.path, 
        method: req.method, 
        error: errorMessage,
        body: req.body 
      });

      res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
        })),
        timestamp: new Date().toISOString(),
      });
      return;
    }

    req.body = value;
    next();
  };
};

// 验证查询参数
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      log.warn('查询参数验证失败', { 
        path: req.path, 
        method: req.method, 
        error: errorMessage,
        query: req.query 
      });

      res.status(400).json({
        success: false,
        message: '查询参数验证失败',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
        })),
        timestamp: new Date().toISOString(),
      });
      return;
    }

    req.query = value;
    next();
  };
};

// 验证路径参数
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      log.warn('路径参数验证失败', { 
        path: req.path, 
        method: req.method, 
        error: errorMessage,
        params: req.params 
      });

      res.status(400).json({
        success: false,
        message: '路径参数验证失败',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
        })),
        timestamp: new Date().toISOString(),
      });
      return;
    }

    req.params = value;
    next();
  };
};
