import { Request, Response, NextFunction } from 'express';
import { AppError } from '../types';
import { log } from '../config/logger';
import config from '../config';

// 自定义错误类
export class CustomError extends Error implements AppError {
  public statusCode: number;
  public code?: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'CustomError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;

    // 确保堆栈跟踪正确
    Error.captureStackTrace(this, this.constructor);
  }
}

// 创建错误的便捷方法
export const createError = {
  badRequest: (message: string, details?: any) => 
    new CustomError(message, 400, 'BAD_REQUEST', details),
  
  unauthorized: (message: string = '未授权访问', details?: any) => 
    new CustomError(message, 401, 'UNAUTHORIZED', details),
  
  forbidden: (message: string = '禁止访问', details?: any) => 
    new CustomError(message, 403, 'FORBIDDEN', details),
  
  notFound: (message: string = '资源不存在', details?: any) => 
    new CustomError(message, 404, 'NOT_FOUND', details),
  
  conflict: (message: string, details?: any) => 
    new CustomError(message, 409, 'CONFLICT', details),
  
  unprocessableEntity: (message: string, details?: any) => 
    new CustomError(message, 422, 'UNPROCESSABLE_ENTITY', details),
  
  tooManyRequests: (message: string = '请求过于频繁', details?: any) => 
    new CustomError(message, 429, 'TOO_MANY_REQUESTS', details),
  
  internalServerError: (message: string = '服务器内部错误', details?: any) => 
    new CustomError(message, 500, 'INTERNAL_SERVER_ERROR', details),
  
  serviceUnavailable: (message: string = '服务不可用', details?: any) => 
    new CustomError(message, 503, 'SERVICE_UNAVAILABLE', details),
};

// 错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = '服务器内部错误';
  let code = 'INTERNAL_SERVER_ERROR';
  let details: any = undefined;

  // 处理自定义错误
  if (error instanceof CustomError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'CUSTOM_ERROR';
    details = error.details;
  }
  // 处理 Joi 验证错误
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '请求参数验证失败';
    code = 'VALIDATION_ERROR';
    details = error.message;
  }
  // 处理 MongoDB 错误
  else if (error.name === 'MongoError' || error.name === 'MongooseError') {
    statusCode = 500;
    message = '数据库操作失败';
    code = 'DATABASE_ERROR';
    details = config.server.env === 'development' ? error.message : undefined;
  }
  // 处理 JWT 错误
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的访问令牌';
    code = 'INVALID_TOKEN';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '访问令牌已过期';
    code = 'TOKEN_EXPIRED';
  }
  // 处理其他已知错误
  else if (error.message) {
    message = error.message;
  }

  // 记录错误日志
  const errorLog = {
    message: error.message,
    stack: error.stack,
    statusCode,
    code,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    query: req.query,
    params: req.params,
    timestamp: new Date().toISOString(),
  };

  if (statusCode >= 500) {
    log.error('服务器错误', errorLog);
  } else {
    log.warn('客户端错误', errorLog);
  }

  // 构建响应
  const response: any = {
    success: false,
    message,
    code,
    timestamp: new Date().toISOString(),
  };

  // 开发环境包含更多错误信息
  if (config.server.env === 'development') {
    response.stack = error.stack;
    if (details) {
      response.details = details;
    }
  }

  res.status(statusCode).json(response);
};

// 404 处理中间件
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = createError.notFound(`路由 ${req.method} ${req.path} 不存在`);
  next(error);
};

// 异步错误捕获包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
