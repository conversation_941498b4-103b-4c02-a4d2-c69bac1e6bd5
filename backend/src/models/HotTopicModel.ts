import mongoose, { Schema, Document } from 'mongoose';
import { SentimentType, TrendType } from '../types';

// 扩展 Document 接口
export interface IHotTopicDocument extends Document {
  title: string;
  count: number;
  sentiment: SentimentType;
  keywords: string[];
  trend: TrendType;
  trendValue: number;
}

// 热点话题 Schema
const HotTopicSchema = new Schema<IHotTopicDocument>({
  title: {
    type: String,
    required: true,
    maxlength: 200,
    index: 'text',
  },
  count: {
    type: Number,
    required: true,
    min: 0,
    index: true,
  },
  sentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral'],
    required: true,
    index: true,
  },
  keywords: [{
    type: String,
    maxlength: 50,
  }],
  trend: {
    type: String,
    enum: ['up', 'down', 'stable'],
    required: true,
    default: 'stable',
  },
  trendValue: {
    type: Number,
    required: true,
    default: 0,
  },
}, {
  timestamps: true,
  collection: 'hot_topics',
});

// 索引
HotTopicSchema.index({ count: -1, createdAt: -1 });
HotTopicSchema.index({ sentiment: 1, count: -1 });
HotTopicSchema.index({ trend: 1, trendValue: -1 });

// 虚拟字段
HotTopicSchema.virtual('id').get(function(this: any) {
  return this._id.toHexString();
});

// 序列化设置
HotTopicSchema.set('toJSON', {
  virtuals: true,
  transform: function(_doc: any, ret: any) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 创建模型
export const HotTopicModel = mongoose.model<IHotTopicDocument>('HotTopic', HotTopicSchema);
