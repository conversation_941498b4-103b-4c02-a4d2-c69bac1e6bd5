import mongoose, { Schema, Document } from 'mongoose';
import { SentimentType, DataSourceType } from '../types';

// 扩展 Document 接口
export interface ISentimentDocument extends Document {
  content: string;
  sentiment: SentimentType;
  score: number;
  source: DataSourceType;
  author: string;
  platform: string;
  url?: string;
  tags: string[];
  location?: string;
  timestamp: Date;
}

// 舆情数据 Schema
const SentimentSchema = new Schema<ISentimentDocument>({
  content: {
    type: String,
    required: true,
    maxlength: 2000,
    index: 'text', // 文本索引用于搜索
  },
  sentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral'],
    required: true,
    index: true,
  },
  score: {
    type: Number,
    required: true,
    min: 0,
    max: 1,
  },
  source: {
    type: String,
    enum: ['weibo', 'zhihu', 'news', 'other'],
    required: true,
    index: true,
  },
  author: {
    type: String,
    required: true,
    maxlength: 100,
  },
  platform: {
    type: String,
    required: true,
    maxlength: 50,
  },
  url: {
    type: String,
    maxlength: 500,
  },
  tags: [{
    type: String,
    maxlength: 50,
  }],
  location: {
    type: String,
    maxlength: 100,
    index: true,
  },
  timestamp: {
    type: Date,
    required: true,
    index: true,
  },
}, {
  timestamps: true, // 自动添加 createdAt 和 updatedAt
  collection: 'sentiments',
});

// 复合索引
SentimentSchema.index({ timestamp: -1, sentiment: 1 });
SentimentSchema.index({ source: 1, timestamp: -1 });
SentimentSchema.index({ location: 1, sentiment: 1 });
SentimentSchema.index({ tags: 1, sentiment: 1 });

// 虚拟字段
SentimentSchema.virtual('id').get(function(this: any) {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
SentimentSchema.set('toJSON', {
  virtuals: true,
  transform: function(_doc: any, ret: any) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 创建模型
export const SentimentModel = mongoose.model<ISentimentDocument>('Sentiment', SentimentSchema);
