import mongoose from 'mongoose';
import { createClient } from 'redis';
import { log } from './logger';
import config from './index';

// MongoDB 连接
export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private mongoConnection: typeof mongoose | null = null;
  private redisClient: any = null;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  // 连接 MongoDB
  public async connectMongoDB(): Promise<void> {
    try {
      if (this.mongoConnection) {
        log.info('MongoDB 已连接');
        return;
      }

      log.info('正在连接 MongoDB...', { uri: config.database.mongodb.uri });

      const options = {
        maxPoolSize: 10, // 最大连接池大小
        serverSelectionTimeoutMS: 5000, // 服务器选择超时
        socketTimeoutMS: 45000, // Socket 超时
        bufferMaxEntries: 0, // 禁用缓冲
        bufferCommands: false, // 禁用命令缓冲
      };

      this.mongoConnection = await mongoose.connect(config.database.mongodb.uri, options);

      // 监听连接事件
      mongoose.connection.on('connected', () => {
        log.info('MongoDB 连接成功');
      });

      mongoose.connection.on('error', (error) => {
        log.error('MongoDB 连接错误', { error });
      });

      mongoose.connection.on('disconnected', () => {
        log.warn('MongoDB 连接断开');
      });

      // 优雅关闭
      process.on('SIGINT', async () => {
        await this.disconnectMongoDB();
        process.exit(0);
      });

    } catch (error) {
      log.error('MongoDB 连接失败', { error });
      throw error;
    }
  }

  // 断开 MongoDB 连接
  public async disconnectMongoDB(): Promise<void> {
    try {
      if (this.mongoConnection) {
        await mongoose.disconnect();
        this.mongoConnection = null;
        log.info('MongoDB 连接已断开');
      }
    } catch (error) {
      log.error('MongoDB 断开连接失败', { error });
      throw error;
    }
  }

  // 连接 Redis
  public async connectRedis(): Promise<void> {
    try {
      if (this.redisClient && this.redisClient.isOpen) {
        log.info('Redis 已连接');
        return;
      }

      log.info('正在连接 Redis...', { url: config.database.redis.url });

      this.redisClient = createClient({
        url: config.database.redis.url,
        socket: {
          reconnectStrategy: (retries) => Math.min(retries * 50, 500),
        },
      });

      // 监听连接事件
      this.redisClient.on('connect', () => {
        log.info('Redis 连接成功');
      });

      this.redisClient.on('error', (error: Error) => {
        log.error('Redis 连接错误', { error });
      });

      this.redisClient.on('end', () => {
        log.warn('Redis 连接断开');
      });

      await this.redisClient.connect();

    } catch (error) {
      log.error('Redis 连接失败', { error });
      // Redis 连接失败不应该阻止应用启动
      log.warn('应用将在没有 Redis 的情况下运行');
    }
  }

  // 断开 Redis 连接
  public async disconnectRedis(): Promise<void> {
    try {
      if (this.redisClient && this.redisClient.isOpen) {
        await this.redisClient.quit();
        this.redisClient = null;
        log.info('Redis 连接已断开');
      }
    } catch (error) {
      log.error('Redis 断开连接失败', { error });
    }
  }

  // 获取 MongoDB 连接
  public getMongoConnection(): typeof mongoose | null {
    return this.mongoConnection;
  }

  // 获取 Redis 客户端
  public getRedisClient(): any {
    return this.redisClient;
  }

  // 检查 MongoDB 连接状态
  public isMongoConnected(): boolean {
    return mongoose.connection.readyState === 1;
  }

  // 检查 Redis 连接状态
  public isRedisConnected(): boolean {
    return this.redisClient && this.redisClient.isOpen;
  }

  // 连接所有数据库
  public async connectAll(): Promise<void> {
    await Promise.all([
      this.connectMongoDB(),
      this.connectRedis(),
    ]);
  }

  // 断开所有数据库连接
  public async disconnectAll(): Promise<void> {
    await Promise.all([
      this.disconnectMongoDB(),
      this.disconnectRedis(),
    ]);
  }
}

// 导出单例实例
export const database = DatabaseConnection.getInstance();
