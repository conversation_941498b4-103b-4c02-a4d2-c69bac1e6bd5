import dotenv from 'dotenv';
import path from 'path';

// 根据环境加载对应的配置文件
const envFile = process.env.NODE_ENV === 'production' 
  ? '.env.production' 
  : process.env.NODE_ENV === 'test' 
    ? '.env.test' 
    : '.env.development';

dotenv.config({ path: path.resolve(process.cwd(), envFile) });

// 配置接口定义
interface Config {
  // 服务器配置
  server: {
    port: number;
    host: string;
    env: string;
  };
  
  // 数据库配置
  database: {
    mongodb: {
      uri: string;
    };
    redis: {
      url: string;
    };
  };
  
  // JWT 配置
  jwt: {
    secret: string;
    expiresIn: string;
  };
  
  // CORS 配置
  cors: {
    origin: string;
  };
  
  // 日志配置
  logging: {
    level: string;
    dir: string;
  };
  
  // WebSocket 配置
  websocket: {
    corsOrigin: string;
  };
  
  // 数据刷新配置
  dataRefresh: {
    interval: number;
  };
  
  // 系统监控配置
  systemMonitor: {
    interval: number;
  };
  
  // 模拟数据配置
  mockData: {
    enabled: boolean;
    interval: number;
  };
}

// 配置对象
const config: Config = {
  server: {
    port: parseInt(process.env.PORT || '8080', 10),
    host: process.env.HOST || '0.0.0.0',
    env: process.env.NODE_ENV || 'development',
  },
  
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/weibo_sentiment',
    },
    redis: {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
    },
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    dir: process.env.LOG_DIR || 'logs',
  },
  
  websocket: {
    corsOrigin: process.env.WS_CORS_ORIGIN || 'http://localhost:3001',
  },
  
  dataRefresh: {
    interval: parseInt(process.env.DATA_REFRESH_INTERVAL || '30000', 10),
  },
  
  systemMonitor: {
    interval: parseInt(process.env.SYSTEM_MONITOR_INTERVAL || '5000', 10),
  },
  
  mockData: {
    enabled: process.env.ENABLE_MOCK_DATA === 'true',
    interval: parseInt(process.env.MOCK_DATA_INTERVAL || '10000', 10),
  },
};

// 配置验证
const validateConfig = (): void => {
  const requiredFields = [
    'server.port',
    'database.mongodb.uri',
    'jwt.secret',
  ];
  
  for (const field of requiredFields) {
    const keys = field.split('.');
    let value: any = config;
    
    for (const key of keys) {
      value = value[key];
    }
    
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
  
  // 端口范围验证
  if (config.server.port < 1 || config.server.port > 65535) {
    throw new Error('Port must be between 1 and 65535');
  }
  
  // JWT 密钥长度验证
  if (config.jwt.secret.length < 8) {
    throw new Error('JWT secret must be at least 8 characters long');
  }
};

// 验证配置
validateConfig();

export default config;
