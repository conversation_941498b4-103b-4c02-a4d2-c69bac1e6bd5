import { Router } from 'express';
import sentimentRoutes from './sentimentRoutes';
import systemRoutes from './systemRoutes';

const router: Router = Router();

// API 版本信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '舆情监控大屏幕系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      sentiment: '/api/sentiment',
      system: '/api/system',
    },
  });
});

// 注册路由
router.use('/sentiment', sentimentRoutes);
router.use('/system', systemRoutes);

export default router;
