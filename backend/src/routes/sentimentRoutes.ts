import { Router } from 'express';
import { SentimentController } from '../controllers/sentimentController';
import { validateQuery, validateBody } from '../middleware/validation';
import { rateLimit } from '../middleware/rateLimit';
import Joi from 'joi';

const router: Router = Router();
const sentimentController = new SentimentController();

// 查询参数验证模式
const timeRangeSchema = Joi.object({
  range: Joi.string().valid('1h', '6h', '24h', '7d', '30d').optional(),
});

const limitSchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).optional(),
});

const searchSchema = Joi.object({
  query: Joi.string().required().min(1).max(200),
  filters: Joi.object({
    sentiment: Joi.string().valid('positive', 'negative', 'neutral').optional(),
    source: Joi.string().valid('weibo', 'zhihu', 'news', 'other').optional(),
    dateRange: Joi.object({
      start: Joi.date().optional(),
      end: Joi.date().optional(),
    }).optional(),
    location: Joi.string().optional(),
  }).optional(),
});

const sentimentListSchema = Joi.object({
  page: Joi.number().integer().min(1).optional(),
  limit: Joi.number().integer().min(1).max(100).optional(),
  timeRange: Joi.string().valid('1h', '6h', '24h', '7d', '30d').optional(),
  sentiment: Joi.string().valid('positive', 'negative', 'neutral').optional(),
  source: Joi.string().valid('weibo', 'zhihu', 'news', 'other').optional(),
  keyword: Joi.string().optional(),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').optional(),
});

// 应用速率限制
router.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 限制每个 IP 15 分钟内最多 100 个请求
  message: '请求过于频繁，请稍后再试',
}));

// 路由定义
router.get(
  '/realtime',
  validateQuery(timeRangeSchema),
  sentimentController.getRealTimeData
);

router.get(
  '/statistics',
  validateQuery(timeRangeSchema),
  sentimentController.getStatistics
);

router.get(
  '/hot-topics',
  validateQuery(limitSchema),
  sentimentController.getHotTopics
);

router.get(
  '/keywords',
  validateQuery(limitSchema),
  sentimentController.getKeywords
);

router.get(
  '/time-series',
  validateQuery(timeRangeSchema),
  sentimentController.getTimeSeries
);

router.get(
  '/locations',
  sentimentController.getLocationData
);

router.get(
  '/recent-posts',
  validateQuery(limitSchema),
  sentimentController.getRecentPosts
);

router.get(
  '/list',
  validateQuery(sentimentListSchema),
  sentimentController.getSentimentList
);

router.post(
  '/search',
  validateBody(searchSchema),
  sentimentController.search
);

export default router;
