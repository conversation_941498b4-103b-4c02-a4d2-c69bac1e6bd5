// 基础响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// 分页接口
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 情感类型
export type SentimentType = 'positive' | 'negative' | 'neutral';

// 趋势类型
export type TrendType = 'up' | 'down' | 'stable';

// 数据源类型
export type DataSourceType = 'weibo' | 'zhihu' | 'news' | 'other';

// 舆情数据接口
export interface SentimentData {
  id: string;
  content: string;
  sentiment: SentimentType;
  score: number;
  source: DataSourceType;
  author: string;
  platform: string;
  url?: string;
  tags: string[];
  location?: string;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 统计数据接口
export interface StatisticsData {
  total: number;
  positive: number;
  negative: number;
  neutral: number;
  growth: number;
  growthRate: number;
}

// 热点话题接口
export interface HotTopic {
  id: string;
  title: string;
  count: number;
  sentiment: SentimentType;
  keywords: string[];
  trend: TrendType;
  trendValue: number;
  createdAt: Date;
  updatedAt: Date;
}

// 关键词数据接口
export interface KeywordData {
  name: string;
  value: number;
  sentiment: SentimentType;
}

// 时间序列数据接口
export interface TimeSeriesData {
  timestamp: Date;
  value: number;
  positive?: number;
  negative?: number;
  neutral?: number;
}

// 地理位置数据接口
export interface LocationData {
  name: string;
  value: number;
  sentiment: SentimentType;
  coordinates: [number, number];
}

// 实时数据接口
export interface RealTimeData {
  statistics: StatisticsData;
  hotTopics: HotTopic[];
  keywords: KeywordData[];
  timeSeries: TimeSeriesData[];
  locations: LocationData[];
  recentPosts: SentimentData[];
}

// WebSocket 消息接口
export interface WebSocketMessage {
  type: 'update' | 'alert' | 'heartbeat';
  data: any;
  timestamp: Date;
}

// 系统状态接口
export interface SystemStatus {
  isOnline: boolean;
  lastUpdate: Date;
  dataSource: {
    weibo: boolean;
    zhihu: boolean;
    news: boolean;
  };
  performance: {
    cpu: number;
    memory: number;
    network: number;
  };
}

// 系统性能指标接口
export interface SystemPerformance {
  cpu: {
    usage: number;
    cores: number;
    model: string;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    rx: number;
    tx: number;
  };
  uptime: number;
}

// 查询参数接口
export interface SentimentQuery extends PaginationQuery {
  timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
  sentiment?: SentimentType;
  source?: DataSourceType;
  keyword?: string;
  startDate?: Date;
  endDate?: Date;
}

// 搜索参数接口
export interface SearchQuery {
  query: string;
  filters?: {
    sentiment?: SentimentType;
    source?: DataSourceType;
    dateRange?: {
      start: Date;
      end: Date;
    };
    location?: string;
  };
}

// 错误接口
export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

// Express 扩展接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        role: string;
      };
    }
  }
}
