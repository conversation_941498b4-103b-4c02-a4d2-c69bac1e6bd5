import { SystemStatus, SystemPerformance } from '../types';
import { log } from '../config/logger';
import * as si from 'systeminformation';

export class SystemService {
  
  // 获取系统状态
  public async getSystemStatus(): Promise<SystemStatus> {
    try {
      log.debug('获取系统状态');

      // 模拟数据源状态检查
      const dataSourceStatus = {
        weibo: Math.random() > 0.1,
        zhihu: Math.random() > 0.1,
        news: Math.random() > 0.1,
      };

      // 获取基本性能指标
      const performance = await this.getBasicPerformance();

      return {
        isOnline: true,
        lastUpdate: new Date(),
        dataSource: dataSourceStatus,
        performance: {
          cpu: performance.cpu,
          memory: performance.memory,
          network: performance.network,
        },
      };
    } catch (error) {
      log.error('获取系统状态失败', { error });
      throw error;
    }
  }

  // 获取性能指标
  public async getPerformanceMetrics(): Promise<SystemPerformance> {
    try {
      log.debug('获取性能指标');

      const [cpu, memory, disk, network, uptime] = await Promise.all([
        this.getCpuInfo(),
        this.getMemoryInfo(),
        this.getDiskInfo(),
        this.getNetworkInfo(),
        this.getUptime(),
      ]);

      return {
        cpu,
        memory,
        disk,
        network,
        uptime,
      };
    } catch (error) {
      log.error('获取性能指标失败', { error });
      
      // 返回模拟数据作为后备
      return this.getMockPerformanceMetrics();
    }
  }

  // 健康检查
  public async healthCheck(): Promise<{ status: string; timestamp: string; details: any }> {
    try {
      log.debug('执行健康检查');

      const checks = {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        disk: await this.checkDiskSpace(),
        memory: await this.checkMemoryUsage(),
      };

      const allHealthy = Object.values(checks).every(check => check.healthy);

      return {
        status: allHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        details: checks,
      };
    } catch (error) {
      log.error('健康检查失败', { error });
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        details: { error: error instanceof Error ? error.message : String(error) },
      };
    }
  }

  // 获取系统信息
  public async getSystemInfo(): Promise<any> {
    try {
      log.debug('获取系统信息');

      const [system, osInfo, cpu, memory] = await Promise.all([
        si.system(),
        si.osInfo(),
        si.cpu(),
        si.mem(),
      ]);

      return {
        system: {
          manufacturer: system.manufacturer,
          model: system.model,
          version: system.version,
        },
        os: {
          platform: osInfo.platform,
          distro: osInfo.distro,
          release: osInfo.release,
          arch: osInfo.arch,
        },
        cpu: {
          manufacturer: cpu.manufacturer,
          brand: cpu.brand,
          cores: cpu.cores,
          physicalCores: cpu.physicalCores,
          speed: cpu.speed,
        },
        memory: {
          total: Math.round(memory.total / 1024 / 1024 / 1024), // GB
        },
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      };
    } catch (error) {
      log.error('获取系统信息失败', { error });
      
      // 返回基本信息作为后备
      return {
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch,
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  // 获取 CPU 信息
  private async getCpuInfo(): Promise<any> {
    try {
      const [cpuLoad, cpuInfo] = await Promise.all([
        si.currentLoad(),
        si.cpu(),
      ]);

      return {
        usage: Math.round(cpuLoad.currentLoad),
        cores: cpuInfo.cores,
        model: cpuInfo.brand,
      };
    } catch (error) {
      return {
        usage: Math.random() * 80 + 10,
        cores: 4,
        model: 'Unknown',
      };
    }
  }

  // 获取内存信息
  private async getMemoryInfo(): Promise<any> {
    try {
      const memory = await si.mem();
      const total = memory.total;
      const used = memory.used;
      const free = memory.free;
      const usage = Math.round((used / total) * 100);

      return {
        total: Math.round(total / 1024 / 1024 / 1024), // GB
        used: Math.round(used / 1024 / 1024 / 1024), // GB
        free: Math.round(free / 1024 / 1024 / 1024), // GB
        usage,
      };
    } catch (error) {
      return {
        total: 8,
        used: 4,
        free: 4,
        usage: 50,
      };
    }
  }

  // 获取磁盘信息
  private async getDiskInfo(): Promise<any> {
    try {
      const disks = await si.fsSize();
      const mainDisk = disks[0] || {};

      return {
        total: Math.round(mainDisk.size / 1024 / 1024 / 1024), // GB
        used: Math.round(mainDisk.used / 1024 / 1024 / 1024), // GB
        free: Math.round((mainDisk.size - mainDisk.used) / 1024 / 1024 / 1024), // GB
        usage: Math.round(mainDisk.use),
      };
    } catch (error) {
      return {
        total: 100,
        used: 50,
        free: 50,
        usage: 50,
      };
    }
  }

  // 获取网络信息
  private async getNetworkInfo(): Promise<any> {
    try {
      const networkStats = await si.networkStats();
      const mainInterface = networkStats[0] || {};

      return {
        rx: mainInterface.rx_bytes || 0,
        tx: mainInterface.tx_bytes || 0,
      };
    } catch (error) {
      return {
        rx: Math.random() * 1000000,
        tx: Math.random() * 1000000,
      };
    }
  }

  // 获取系统运行时间
  private async getUptime(): Promise<number> {
    try {
      const uptime = await si.time();
      return uptime.uptime;
    } catch (error) {
      return process.uptime();
    }
  }

  // 获取基本性能指标
  private async getBasicPerformance(): Promise<{ cpu: number; memory: number; network: number }> {
    try {
      const [cpu, memory] = await Promise.all([
        this.getCpuInfo(),
        this.getMemoryInfo(),
      ]);

      return {
        cpu: cpu.usage,
        memory: memory.usage,
        network: Math.random() * 50 + 5, // 模拟网络使用率
      };
    } catch (error) {
      return {
        cpu: Math.random() * 80 + 10,
        memory: Math.random() * 70 + 20,
        network: Math.random() * 50 + 5,
      };
    }
  }

  // 检查数据库连接
  private async checkDatabase(): Promise<{ healthy: boolean; message: string }> {
    try {
      // 这里应该实际检查数据库连接
      // 目前返回模拟结果
      return {
        healthy: Math.random() > 0.1,
        message: '数据库连接正常',
      };
    } catch (error) {
      return {
        healthy: false,
        message: `数据库连接失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  // 检查 Redis 连接
  private async checkRedis(): Promise<{ healthy: boolean; message: string }> {
    try {
      // 这里应该实际检查 Redis 连接
      // 目前返回模拟结果
      return {
        healthy: Math.random() > 0.1,
        message: 'Redis 连接正常',
      };
    } catch (error) {
      return {
        healthy: false,
        message: `Redis 连接失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  // 检查磁盘空间
  private async checkDiskSpace(): Promise<{ healthy: boolean; message: string }> {
    try {
      const disk = await this.getDiskInfo();
      const healthy = disk.usage < 90;
      
      return {
        healthy,
        message: healthy ? '磁盘空间充足' : '磁盘空间不足',
      };
    } catch (error) {
      return {
        healthy: true,
        message: '无法检查磁盘空间',
      };
    }
  }

  // 检查内存使用率
  private async checkMemoryUsage(): Promise<{ healthy: boolean; message: string }> {
    try {
      const memory = await this.getMemoryInfo();
      const healthy = memory.usage < 90;
      
      return {
        healthy,
        message: healthy ? '内存使用正常' : '内存使用率过高',
      };
    } catch (error) {
      return {
        healthy: true,
        message: '无法检查内存使用率',
      };
    }
  }

  // 获取模拟性能指标
  private getMockPerformanceMetrics(): SystemPerformance {
    return {
      cpu: {
        usage: Math.random() * 80 + 10,
        cores: 4,
        model: 'Mock CPU',
      },
      memory: {
        total: 8,
        used: 4,
        free: 4,
        usage: 50,
      },
      disk: {
        total: 100,
        used: 50,
        free: 50,
        usage: 50,
      },
      network: {
        rx: Math.random() * 1000000,
        tx: Math.random() * 1000000,
      },
      uptime: process.uptime(),
    };
  }
}
