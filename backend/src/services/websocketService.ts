import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HttpServer } from 'http';
import { WebSocketMessage, RealTimeData } from '../types';
import { log } from '../config/logger';
import config from '../config';

export class WebSocketService {
  private io: SocketIOServer;
  private connectedClients: Map<string, Socket> = new Map();

  constructor(httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: config.websocket.corsOrigin,
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupEventHandlers();
    log.info('WebSocket 服务初始化完成');
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      const clientId = socket.id;
      this.connectedClients.set(clientId, socket);
      
      log.info('客户端连接', { clientId, totalClients: this.connectedClients.size });

      // 发送欢迎消息
      socket.emit('connected', {
        message: '连接成功',
        clientId,
        timestamp: new Date().toISOString(),
      });

      // 处理客户端断开连接
      socket.on('disconnect', (reason) => {
        this.connectedClients.delete(clientId);
        log.info('客户端断开连接', { 
          clientId, 
          reason, 
          totalClients: this.connectedClients.size 
        });
      });

      // 处理心跳
      socket.on('ping', () => {
        socket.emit('pong', {
          timestamp: new Date().toISOString(),
        });
      });

      // 处理订阅请求
      socket.on('subscribe', (data) => {
        const { channels } = data;
        if (Array.isArray(channels)) {
          channels.forEach((channel: string) => {
            socket.join(channel);
            log.debug('客户端订阅频道', { clientId, channel });
          });
        }
      });

      // 处理取消订阅请求
      socket.on('unsubscribe', (data) => {
        const { channels } = data;
        if (Array.isArray(channels)) {
          channels.forEach((channel: string) => {
            socket.leave(channel);
            log.debug('客户端取消订阅频道', { clientId, channel });
          });
        }
      });

      // 处理客户端错误
      socket.on('error', (error) => {
        log.error('WebSocket 客户端错误', { clientId, error });
      });
    });

    // 处理服务器错误
    this.io.on('error', (error) => {
      log.error('WebSocket 服务器错误', { error });
    });
  }

  // 广播消息给所有客户端
  public broadcast(message: WebSocketMessage): void {
    try {
      this.io.emit('data:update', message);
      log.debug('广播消息', { 
        type: message.type, 
        clients: this.connectedClients.size 
      });
    } catch (error) {
      log.error('广播消息失败', { error });
    }
  }

  // 发送消息给特定客户端
  public sendToClient(clientId: string, message: WebSocketMessage): void {
    try {
      const socket = this.connectedClients.get(clientId);
      if (socket) {
        socket.emit('data:update', message);
        log.debug('发送消息给客户端', { clientId, type: message.type });
      } else {
        log.warn('客户端不存在', { clientId });
      }
    } catch (error) {
      log.error('发送消息给客户端失败', { clientId, error });
    }
  }

  // 发送消息给特定频道
  public sendToChannel(channel: string, message: WebSocketMessage): void {
    try {
      this.io.to(channel).emit('data:update', message);
      log.debug('发送消息给频道', { channel, type: message.type });
    } catch (error) {
      log.error('发送消息给频道失败', { channel, error });
    }
  }

  // 发送实时数据更新
  public sendRealTimeDataUpdate(data: RealTimeData): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'realtime',
        payload: data,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送统计数据更新
  public sendStatisticsUpdate(statistics: any): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'statistics',
        payload: statistics,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送热点话题更新
  public sendHotTopicsUpdate(hotTopics: any[]): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'hotTopics',
        payload: hotTopics,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送关键词更新
  public sendKeywordsUpdate(keywords: any[]): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'keywords',
        payload: keywords,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送时间序列数据更新
  public sendTimeSeriesUpdate(timeSeries: any[]): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'timeSeries',
        payload: timeSeries,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送地理位置数据更新
  public sendLocationDataUpdate(locations: any[]): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'locations',
        payload: locations,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送新帖子通知
  public sendNewPostNotification(post: any): void {
    const message: WebSocketMessage = {
      type: 'update',
      data: {
        type: 'newPost',
        payload: post,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送系统警告
  public sendAlert(alertData: any): void {
    const message: WebSocketMessage = {
      type: 'alert',
      data: alertData,
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 发送心跳
  public sendHeartbeat(): void {
    const message: WebSocketMessage = {
      type: 'heartbeat',
      data: {
        timestamp: new Date().toISOString(),
        connectedClients: this.connectedClients.size,
      },
      timestamp: new Date(),
    };
    this.broadcast(message);
  }

  // 获取连接的客户端数量
  public getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  // 获取所有连接的客户端 ID
  public getConnectedClientIds(): string[] {
    return Array.from(this.connectedClients.keys());
  }

  // 关闭 WebSocket 服务
  public close(): void {
    this.io.close();
    this.connectedClients.clear();
    log.info('WebSocket 服务已关闭');
  }
}
