import { Request, Response, NextFunction } from 'express';
import { SentimentService } from '../services/sentimentService';
import { ApiResponse, SentimentQuery, SearchQuery } from '../types';
import { log } from '../config/logger';

export class SentimentController {
  private sentimentService: SentimentService;

  constructor() {
    this.sentimentService = new SentimentService();
  }

  // 获取实时数据
  public getRealTimeData = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { range = '24h' } = req.query;
      
      log.info('获取实时数据', { range });
      
      const data = await this.sentimentService.getRealTimeData(range as string);
      
      const response: ApiResponse = {
        success: true,
        data,
        message: '实时数据获取成功',
        timestamp: new Date().toISOString(),
      };
      
      res.json(response);
    } catch (error) {
      log.error('获取实时数据失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取统计数据
  public getStatistics = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { range = '24h' } = req.query;
      
      log.info('获取统计数据', { range });
      
      const statistics = await this.sentimentService.getStatistics(range as string);
      
      const response: ApiResponse = {
        success: true,
        data: statistics,
        message: '统计数据获取成功',
        timestamp: new Date().toISOString(),
      };
      
      res.json(response);
    } catch (error) {
      log.error('获取统计数据失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取热点话题
  public getHotTopics = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { limit = 10 } = req.query;

      log.info('获取热点话题', { limit });

      const hotTopics = await this.sentimentService.getHotTopics(Number(limit));

      const response: ApiResponse = {
        success: true,
        data: hotTopics,
        message: '热点话题获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取热点话题失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取关键词
  public getKeywords = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { limit = 50 } = req.query;

      log.info('获取关键词', { limit });

      const keywords = await this.sentimentService.getKeywords(Number(limit));

      const response: ApiResponse = {
        success: true,
        data: keywords,
        message: '关键词获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取关键词失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取时间序列数据
  public getTimeSeries = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { range = '24h' } = req.query;
      
      log.info('获取时间序列数据', { range });
      
      const timeSeries = await this.sentimentService.getTimeSeries(range as string);
      
      const response: ApiResponse = {
        success: true,
        data: timeSeries,
        message: '时间序列数据获取成功',
        timestamp: new Date().toISOString(),
      };
      
      res.json(response);
    } catch (error) {
      log.error('获取时间序列数据失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取地理位置数据
  public getLocationData = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      log.info('获取地理位置数据');

      const locations = await this.sentimentService.getLocationData();

      const response: ApiResponse = {
        success: true,
        data: locations,
        message: '地理位置数据获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取地理位置数据失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取最新帖子
  public getRecentPosts = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { limit = 20 } = req.query;

      log.info('获取最新帖子', { limit });

      const recentPosts = await this.sentimentService.getRecentPosts(Number(limit));

      const response: ApiResponse = {
        success: true,
        data: recentPosts,
        message: '最新帖子获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取最新帖子失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 搜索内容
  public search = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const searchQuery: SearchQuery = req.body;

      log.info('搜索内容', { query: searchQuery.query });

      const results = await this.sentimentService.search(searchQuery);

      const response: ApiResponse = {
        success: true,
        data: results,
        message: '搜索完成',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('搜索失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取舆情数据列表
  public getSentimentList = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const query: SentimentQuery = req.query;

      log.info('获取舆情数据列表', { query });

      const result = await this.sentimentService.getSentimentList(query);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '舆情数据列表获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取舆情数据列表失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };
}
