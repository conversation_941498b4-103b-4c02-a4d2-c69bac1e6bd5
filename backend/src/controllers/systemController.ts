import { Request, Response, NextFunction } from 'express';
import { SystemService } from '../services/systemService';
import { ApiResponse } from '../types';
import { log } from '../config/logger';

export class SystemController {
  private systemService: SystemService;

  constructor() {
    this.systemService = new SystemService();
  }

  // 获取系统状态
  public getStatus = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      log.info('获取系统状态');
      
      const status = await this.systemService.getSystemStatus();
      
      const response: ApiResponse = {
        success: true,
        data: status,
        message: '系统状态获取成功',
        timestamp: new Date().toISOString(),
      };
      
      res.json(response);
    } catch (error) {
      log.error('获取系统状态失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取性能指标
  public getPerformance = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      log.info('获取性能指标');

      const performance = await this.systemService.getPerformanceMetrics();

      const response: ApiResponse = {
        success: true,
        data: performance,
        message: '性能指标获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取性能指标失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 健康检查
  public healthCheck = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      log.info('健康检查');

      const health = await this.systemService.healthCheck();

      const response: ApiResponse = {
        success: true,
        data: health,
        message: '系统健康',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('健康检查失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };

  // 获取系统信息
  public getSystemInfo = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      log.info('获取系统信息');

      const systemInfo = await this.systemService.getSystemInfo();

      const response: ApiResponse = {
        success: true,
        data: systemInfo,
        message: '系统信息获取成功',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      log.error('获取系统信息失败', { error: error instanceof Error ? error.message : String(error) });
      next(error);
    }
  };
}
