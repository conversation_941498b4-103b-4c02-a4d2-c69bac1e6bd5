import express from 'express';
import http from 'http';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';

import config from './config';
import { log } from './config/logger';
import { database } from './config/database';
import { WebSocketService } from './services/websocketService';
import { DataGeneratorService } from './services/dataGeneratorService';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import routes from './routes';

class Server {
  private app: express.Application;
  private httpServer: http.Server;
  private websocketService: WebSocketService;
  private dataGeneratorService: DataGeneratorService;

  constructor() {
    this.app = express();
    this.httpServer = http.createServer(this.app);
    this.websocketService = new WebSocketService(this.httpServer);
    this.dataGeneratorService = new DataGeneratorService();
    
    // 设置 WebSocket 服务到数据生成器
    this.dataGeneratorService.setWebSocketService(this.websocketService);
  }

  // 初始化中间件
  private initializeMiddleware(): void {
    // 安全中间件
    this.app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS 配置
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    }));

    // 压缩中间件
    this.app.use(compression());

    // 请求解析中间件
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 日志中间件
    if (config.server.env === 'development') {
      this.app.use(morgan('dev'));
    } else {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => {
            log.info(message.trim());
          },
        },
      }));
    }

    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.server.env,
        version: process.env.npm_package_version || '1.0.0',
      });
    });
  }

  // 初始化路由
  private initializeRoutes(): void {
    // API 路由
    this.app.use('/api', routes);

    // 404 处理
    this.app.use(notFoundHandler);

    // 错误处理
    this.app.use(errorHandler);
  }

  // 初始化数据库
  private async initializeDatabase(): Promise<void> {
    try {
      await database.connectAll();
      log.info('数据库连接成功');
    } catch (error) {
      log.error('数据库连接失败', { error });
      // 数据库连接失败不应该阻止服务器启动（使用模拟数据）
      log.warn('服务器将使用模拟数据运行');
    }
  }

  // 启动服务器
  public async start(): Promise<void> {
    try {
      // 初始化数据库
      await this.initializeDatabase();

      // 初始化中间件
      this.initializeMiddleware();

      // 初始化路由
      this.initializeRoutes();

      // 启动数据生成器
      this.dataGeneratorService.start();

      // 启动 HTTP 服务器
      this.httpServer.listen(config.server.port, config.server.host, () => {
        log.info('服务器启动成功', {
          host: config.server.host,
          port: config.server.port,
          env: config.server.env,
          websocket: '已启用',
          mockData: config.mockData.enabled ? '已启用' : '已禁用',
        });

        // 输出访问信息
        console.log(`
🚀 舆情监控大屏幕系统后端服务已启动

📡 HTTP 服务器: http://${config.server.host}:${config.server.port}
🔌 WebSocket 服务: ws://${config.server.host}:${config.server.port}
📊 API 文档: http://${config.server.host}:${config.server.port}/api
🏥 健康检查: http://${config.server.host}:${config.server.port}/health

🌍 环境: ${config.server.env}
📦 模拟数据: ${config.mockData.enabled ? '启用' : '禁用'}
🔄 数据刷新间隔: ${config.dataRefresh.interval}ms
📈 系统监控间隔: ${config.systemMonitor.interval}ms

按 Ctrl+C 停止服务器
        `);
      });

      // 优雅关闭处理
      this.setupGracefulShutdown();

    } catch (error) {
      log.error('服务器启动失败', { error });
      process.exit(1);
    }
  }

  // 设置优雅关闭
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      log.info(`收到 ${signal} 信号，开始优雅关闭...`);

      try {
        // 停止数据生成器
        this.dataGeneratorService.stop();

        // 关闭 WebSocket 服务
        this.websocketService.close();

        // 关闭 HTTP 服务器
        this.httpServer.close(() => {
          log.info('HTTP 服务器已关闭');
        });

        // 断开数据库连接
        await database.disconnectAll();

        log.info('服务器优雅关闭完成');
        process.exit(0);
      } catch (error) {
        log.error('优雅关闭失败', { error });
        process.exit(1);
      }
    };

    // 监听关闭信号
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      log.error('未捕获的异常', { error });
      shutdown('uncaughtException');
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
      log.error('未处理的 Promise 拒绝', { reason, promise });
      shutdown('unhandledRejection');
    });
  }

  // 获取服务器实例
  public getApp(): express.Application {
    return this.app;
  }

  // 获取 HTTP 服务器实例
  public getHttpServer(): http.Server {
    return this.httpServer;
  }

  // 获取 WebSocket 服务实例
  public getWebSocketService(): WebSocketService {
    return this.websocketService;
  }

  // 获取数据生成器服务实例
  public getDataGeneratorService(): DataGeneratorService {
    return this.dataGeneratorService;
  }
}

// 创建并启动服务器
const server = new Server();
server.start().catch((error) => {
  log.error('服务器启动失败', { error });
  process.exit(1);
});

export default server;
