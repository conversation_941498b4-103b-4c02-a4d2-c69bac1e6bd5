"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentimentController = void 0;
const sentimentService_1 = require("../services/sentimentService");
const logger_1 = require("../config/logger");
class SentimentController {
    constructor() {
        this.getRealTimeData = async (req, res, next) => {
            try {
                const { range = '24h' } = req.query;
                logger_1.log.info('获取实时数据', { range });
                const data = await this.sentimentService.getRealTimeData(range);
                const response = {
                    success: true,
                    data,
                    message: '实时数据获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取实时数据失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getStatistics = async (req, res, next) => {
            try {
                const { range = '24h' } = req.query;
                logger_1.log.info('获取统计数据', { range });
                const statistics = await this.sentimentService.getStatistics(range);
                const response = {
                    success: true,
                    data: statistics,
                    message: '统计数据获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取统计数据失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getHotTopics = async (req, res, next) => {
            try {
                const { limit = 10 } = req.query;
                logger_1.log.info('获取热点话题', { limit });
                const hotTopics = await this.sentimentService.getHotTopics(Number(limit));
                const response = {
                    success: true,
                    data: hotTopics,
                    message: '热点话题获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取热点话题失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getKeywords = async (req, res, next) => {
            try {
                const { limit = 50 } = req.query;
                logger_1.log.info('获取关键词', { limit });
                const keywords = await this.sentimentService.getKeywords(Number(limit));
                const response = {
                    success: true,
                    data: keywords,
                    message: '关键词获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取关键词失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getTimeSeries = async (req, res, next) => {
            try {
                const { range = '24h' } = req.query;
                logger_1.log.info('获取时间序列数据', { range });
                const timeSeries = await this.sentimentService.getTimeSeries(range);
                const response = {
                    success: true,
                    data: timeSeries,
                    message: '时间序列数据获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取时间序列数据失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getLocationData = async (req, res, next) => {
            try {
                logger_1.log.info('获取地理位置数据');
                const locations = await this.sentimentService.getLocationData();
                const response = {
                    success: true,
                    data: locations,
                    message: '地理位置数据获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取地理位置数据失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getRecentPosts = async (req, res, next) => {
            try {
                const { limit = 20 } = req.query;
                logger_1.log.info('获取最新帖子', { limit });
                const recentPosts = await this.sentimentService.getRecentPosts(Number(limit));
                const response = {
                    success: true,
                    data: recentPosts,
                    message: '最新帖子获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取最新帖子失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.search = async (req, res, next) => {
            try {
                const searchQuery = req.body;
                logger_1.log.info('搜索内容', { query: searchQuery.query });
                const results = await this.sentimentService.search(searchQuery);
                const response = {
                    success: true,
                    data: results,
                    message: '搜索完成',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('搜索失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getSentimentList = async (req, res, next) => {
            try {
                const query = req.query;
                logger_1.log.info('获取舆情数据列表', { query });
                const result = await this.sentimentService.getSentimentList(query);
                const response = {
                    success: true,
                    data: result,
                    message: '舆情数据列表获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取舆情数据列表失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.sentimentService = new sentimentService_1.SentimentService();
    }
}
exports.SentimentController = SentimentController;
//# sourceMappingURL=sentimentController.js.map