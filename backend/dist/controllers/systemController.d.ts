import { Request, Response, NextFunction } from 'express';
export declare class SystemController {
    private systemService;
    constructor();
    getStatus: (_req: Request, res: Response, next: NextFunction) => Promise<void>;
    getPerformance: (_req: Request, res: Response, next: NextFunction) => Promise<void>;
    healthCheck: (_req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSystemInfo: (_req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=systemController.d.ts.map