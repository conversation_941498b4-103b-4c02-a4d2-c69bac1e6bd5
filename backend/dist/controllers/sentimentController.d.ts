import { Request, Response, NextFunction } from 'express';
export declare class SentimentController {
    private sentimentService;
    constructor();
    getRealTimeData: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getStatistics: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getHotTopics: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getKeywords: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getTimeSeries: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getLocationData: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getRecentPosts: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    search: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSentimentList: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=sentimentController.d.ts.map