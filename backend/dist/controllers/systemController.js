"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemController = void 0;
const systemService_1 = require("../services/systemService");
const logger_1 = require("../config/logger");
class SystemController {
    constructor() {
        this.getStatus = async (_req, res, next) => {
            try {
                logger_1.log.info('获取系统状态');
                const status = await this.systemService.getSystemStatus();
                const response = {
                    success: true,
                    data: status,
                    message: '系统状态获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取系统状态失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getPerformance = async (_req, res, next) => {
            try {
                logger_1.log.info('获取性能指标');
                const performance = await this.systemService.getPerformanceMetrics();
                const response = {
                    success: true,
                    data: performance,
                    message: '性能指标获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取性能指标失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.healthCheck = async (_req, res, next) => {
            try {
                logger_1.log.info('健康检查');
                const health = await this.systemService.healthCheck();
                const response = {
                    success: true,
                    data: health,
                    message: '系统健康',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('健康检查失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.getSystemInfo = async (_req, res, next) => {
            try {
                logger_1.log.info('获取系统信息');
                const systemInfo = await this.systemService.getSystemInfo();
                const response = {
                    success: true,
                    data: systemInfo,
                    message: '系统信息获取成功',
                    timestamp: new Date().toISOString(),
                };
                res.json(response);
            }
            catch (error) {
                logger_1.log.error('获取系统信息失败', { error: error instanceof Error ? error.message : String(error) });
                next(error);
            }
        };
        this.systemService = new systemService_1.SystemService();
    }
}
exports.SystemController = SystemController;
//# sourceMappingURL=systemController.js.map