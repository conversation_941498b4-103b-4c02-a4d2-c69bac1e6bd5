"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const systemController_1 = require("../controllers/systemController");
const rateLimit_1 = require("../middleware/rateLimit");
const router = (0, express_1.Router)();
const systemController = new systemController_1.SystemController();
router.use((0, rateLimit_1.rateLimit)({
    windowMs: 15 * 60 * 1000,
    max: 50,
    message: '请求过于频繁，请稍后再试',
}));
router.get('/status', systemController.getStatus);
router.get('/performance', systemController.getPerformance);
router.get('/health', systemController.healthCheck);
router.get('/info', systemController.getSystemInfo);
exports.default = router;
//# sourceMappingURL=systemRoutes.js.map