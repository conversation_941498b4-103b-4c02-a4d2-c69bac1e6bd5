"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const sentimentRoutes_1 = __importDefault(require("./sentimentRoutes"));
const systemRoutes_1 = __importDefault(require("./systemRoutes"));
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: '舆情监控大屏幕系统 API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            sentiment: '/api/sentiment',
            system: '/api/system',
        },
    });
});
router.use('/sentiment', sentimentRoutes_1.default);
router.use('/system', systemRoutes_1.default);
exports.default = router;
//# sourceMappingURL=index.js.map