"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const sentimentController_1 = require("../controllers/sentimentController");
const validation_1 = require("../middleware/validation");
const rateLimit_1 = require("../middleware/rateLimit");
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
const sentimentController = new sentimentController_1.SentimentController();
const timeRangeSchema = joi_1.default.object({
    range: joi_1.default.string().valid('1h', '6h', '24h', '7d', '30d').optional(),
});
const limitSchema = joi_1.default.object({
    limit: joi_1.default.number().integer().min(1).max(100).optional(),
});
const searchSchema = joi_1.default.object({
    query: joi_1.default.string().required().min(1).max(200),
    filters: joi_1.default.object({
        sentiment: joi_1.default.string().valid('positive', 'negative', 'neutral').optional(),
        source: joi_1.default.string().valid('weibo', 'zhihu', 'news', 'other').optional(),
        dateRange: joi_1.default.object({
            start: joi_1.default.date().optional(),
            end: joi_1.default.date().optional(),
        }).optional(),
        location: joi_1.default.string().optional(),
    }).optional(),
});
const sentimentListSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).optional(),
    limit: joi_1.default.number().integer().min(1).max(100).optional(),
    timeRange: joi_1.default.string().valid('1h', '6h', '24h', '7d', '30d').optional(),
    sentiment: joi_1.default.string().valid('positive', 'negative', 'neutral').optional(),
    source: joi_1.default.string().valid('weibo', 'zhihu', 'news', 'other').optional(),
    keyword: joi_1.default.string().optional(),
    sortBy: joi_1.default.string().optional(),
    sortOrder: joi_1.default.string().valid('asc', 'desc').optional(),
});
router.use((0, rateLimit_1.rateLimit)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: '请求过于频繁，请稍后再试',
}));
router.get('/realtime', (0, validation_1.validateQuery)(timeRangeSchema), sentimentController.getRealTimeData);
router.get('/statistics', (0, validation_1.validateQuery)(timeRangeSchema), sentimentController.getStatistics);
router.get('/hot-topics', (0, validation_1.validateQuery)(limitSchema), sentimentController.getHotTopics);
router.get('/keywords', (0, validation_1.validateQuery)(limitSchema), sentimentController.getKeywords);
router.get('/time-series', (0, validation_1.validateQuery)(timeRangeSchema), sentimentController.getTimeSeries);
router.get('/locations', sentimentController.getLocationData);
router.get('/recent-posts', (0, validation_1.validateQuery)(limitSchema), sentimentController.getRecentPosts);
router.get('/list', (0, validation_1.validateQuery)(sentimentListSchema), sentimentController.getSentimentList);
router.post('/search', (0, validation_1.validateBody)(searchSchema), sentimentController.search);
exports.default = router;
//# sourceMappingURL=sentimentRoutes.js.map