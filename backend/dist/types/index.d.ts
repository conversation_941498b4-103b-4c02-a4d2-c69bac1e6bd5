export interface ApiResponse<T = any> {
    success: boolean;
    data: T;
    message?: string;
    timestamp: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
    };
}
export type SentimentType = 'positive' | 'negative' | 'neutral';
export type TrendType = 'up' | 'down' | 'stable';
export type DataSourceType = 'weibo' | 'zhihu' | 'news' | 'other';
export interface SentimentData {
    id: string;
    content: string;
    sentiment: SentimentType;
    score: number;
    source: DataSourceType;
    author: string;
    platform: string;
    url?: string;
    tags: string[];
    location?: string;
    timestamp: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface StatisticsData {
    total: number;
    positive: number;
    negative: number;
    neutral: number;
    growth: number;
    growthRate: number;
}
export interface HotTopic {
    id: string;
    title: string;
    count: number;
    sentiment: SentimentType;
    keywords: string[];
    trend: TrendType;
    trendValue: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface KeywordData {
    name: string;
    value: number;
    sentiment: SentimentType;
}
export interface TimeSeriesData {
    timestamp: Date;
    value: number;
    positive?: number;
    negative?: number;
    neutral?: number;
}
export interface LocationData {
    name: string;
    value: number;
    sentiment: SentimentType;
    coordinates: [number, number];
}
export interface RealTimeData {
    statistics: StatisticsData;
    hotTopics: HotTopic[];
    keywords: KeywordData[];
    timeSeries: TimeSeriesData[];
    locations: LocationData[];
    recentPosts: SentimentData[];
}
export interface WebSocketMessage {
    type: 'update' | 'alert' | 'heartbeat';
    data: any;
    timestamp: Date;
}
export interface SystemStatus {
    isOnline: boolean;
    lastUpdate: Date;
    dataSource: {
        weibo: boolean;
        zhihu: boolean;
        news: boolean;
    };
    performance: {
        cpu: number;
        memory: number;
        network: number;
    };
}
export interface SystemPerformance {
    cpu: {
        usage: number;
        cores: number;
        model: string;
    };
    memory: {
        total: number;
        used: number;
        free: number;
        usage: number;
    };
    disk: {
        total: number;
        used: number;
        free: number;
        usage: number;
    };
    network: {
        rx: number;
        tx: number;
    };
    uptime: number;
}
export interface SentimentQuery extends PaginationQuery {
    timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
    sentiment?: SentimentType;
    source?: DataSourceType;
    keyword?: string;
    startDate?: Date;
    endDate?: Date;
}
export interface SearchQuery {
    query: string;
    filters?: {
        sentiment?: SentimentType;
        source?: DataSourceType;
        dateRange?: {
            start: Date;
            end: Date;
        };
        location?: string;
    };
}
export interface AppError extends Error {
    statusCode?: number;
    code?: string;
    details?: any;
}
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                username: string;
                role: string;
            };
        }
    }
}
//# sourceMappingURL=index.d.ts.map