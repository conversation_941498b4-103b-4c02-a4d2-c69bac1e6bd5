"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.createError = exports.CustomError = void 0;
const logger_1 = require("../config/logger");
const config_1 = __importDefault(require("../config"));
class CustomError extends Error {
    constructor(message, statusCode = 500, code, details) {
        super(message);
        this.name = 'CustomError';
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
exports.createError = {
    badRequest: (message, details) => new CustomError(message, 400, 'BAD_REQUEST', details),
    unauthorized: (message = '未授权访问', details) => new CustomError(message, 401, 'UNAUTHORIZED', details),
    forbidden: (message = '禁止访问', details) => new CustomError(message, 403, 'FORBIDDEN', details),
    notFound: (message = '资源不存在', details) => new CustomError(message, 404, 'NOT_FOUND', details),
    conflict: (message, details) => new CustomError(message, 409, 'CONFLICT', details),
    unprocessableEntity: (message, details) => new CustomError(message, 422, 'UNPROCESSABLE_ENTITY', details),
    tooManyRequests: (message = '请求过于频繁', details) => new CustomError(message, 429, 'TOO_MANY_REQUESTS', details),
    internalServerError: (message = '服务器内部错误', details) => new CustomError(message, 500, 'INTERNAL_SERVER_ERROR', details),
    serviceUnavailable: (message = '服务不可用', details) => new CustomError(message, 503, 'SERVICE_UNAVAILABLE', details),
};
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = '服务器内部错误';
    let code = 'INTERNAL_SERVER_ERROR';
    let details = undefined;
    if (error instanceof CustomError) {
        statusCode = error.statusCode;
        message = error.message;
        code = error.code || 'CUSTOM_ERROR';
        details = error.details;
    }
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = '请求参数验证失败';
        code = 'VALIDATION_ERROR';
        details = error.message;
    }
    else if (error.name === 'MongoError' || error.name === 'MongooseError') {
        statusCode = 500;
        message = '数据库操作失败';
        code = 'DATABASE_ERROR';
        details = config_1.default.server.env === 'development' ? error.message : undefined;
    }
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = '无效的访问令牌';
        code = 'INVALID_TOKEN';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = '访问令牌已过期';
        code = 'TOKEN_EXPIRED';
    }
    else if (error.message) {
        message = error.message;
    }
    const errorLog = {
        message: error.message,
        stack: error.stack,
        statusCode,
        code,
        path: req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        query: req.query,
        params: req.params,
        timestamp: new Date().toISOString(),
    };
    if (statusCode >= 500) {
        logger_1.log.error('服务器错误', errorLog);
    }
    else {
        logger_1.log.warn('客户端错误', errorLog);
    }
    const response = {
        success: false,
        message,
        code,
        timestamp: new Date().toISOString(),
    };
    if (config_1.default.server.env === 'development') {
        response.stack = error.stack;
        if (details) {
            response.details = details;
        }
    }
    res.status(statusCode).json(response);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res, next) => {
    const error = exports.createError.notFound(`路由 ${req.method} ${req.path} 不存在`);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map