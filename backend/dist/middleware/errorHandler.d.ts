import { Request, Response, NextFunction } from 'express';
import { AppError } from '../types';
export declare class CustomError extends Error implements AppError {
    statusCode: number;
    code?: string;
    details?: any;
    constructor(message: string, statusCode?: number, code?: string, details?: any);
}
export declare const createError: {
    badRequest: (message: string, details?: any) => CustomError;
    unauthorized: (message?: string, details?: any) => CustomError;
    forbidden: (message?: string, details?: any) => CustomError;
    notFound: (message?: string, details?: any) => CustomError;
    conflict: (message: string, details?: any) => CustomError;
    unprocessableEntity: (message: string, details?: any) => CustomError;
    tooManyRequests: (message?: string, details?: any) => CustomError;
    internalServerError: (message?: string, details?: any) => CustomError;
    serviceUnavailable: (message?: string, details?: any) => CustomError;
};
export declare const errorHandler: (error: Error | AppError, req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=errorHandler.d.ts.map