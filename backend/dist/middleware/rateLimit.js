"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRateLimit = exports.rateLimit = void 0;
const logger_1 = require("../config/logger");
const store = {};
setInterval(() => {
    const now = Date.now();
    Object.keys(store).forEach(key => {
        if (store[key].resetTime < now) {
            delete store[key];
        }
    });
}, 60000);
const rateLimit = (options) => {
    const { windowMs, max, message = '请求过于频繁，请稍后再试', skipSuccessfulRequests = false, skipFailedRequests = false, keyGenerator = (req) => req.ip || 'unknown', } = options;
    return (req, res, next) => {
        const key = keyGenerator(req);
        const now = Date.now();
        const resetTime = now + windowMs;
        if (!store[key] || store[key].resetTime < now) {
            store[key] = {
                count: 0,
                resetTime,
            };
        }
        const record = store[key];
        if (record.count >= max) {
            logger_1.log.warn('速率限制触发', {
                ip: req.ip,
                path: req.path,
                method: req.method,
                count: record.count,
                max,
                resetTime: new Date(record.resetTime).toISOString(),
            });
            res.status(429).json({
                success: false,
                message,
                retryAfter: Math.ceil((record.resetTime - now) / 1000),
                timestamp: new Date().toISOString(),
            });
            return;
        }
        record.count++;
        res.set({
            'X-RateLimit-Limit': max.toString(),
            'X-RateLimit-Remaining': Math.max(0, max - record.count).toString(),
            'X-RateLimit-Reset': new Date(record.resetTime).toISOString(),
        });
        const originalSend = res.send;
        res.send = function (body) {
            const statusCode = res.statusCode;
            if ((skipSuccessfulRequests && statusCode < 400) ||
                (skipFailedRequests && statusCode >= 400)) {
                record.count--;
            }
            return originalSend.call(this, body);
        };
        next();
    };
};
exports.rateLimit = rateLimit;
exports.createRateLimit = {
    strict: () => (0, exports.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 50,
        message: '请求过于频繁，请稍后再试',
    }),
    moderate: () => (0, exports.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 100,
        message: '请求过于频繁，请稍后再试',
    }),
    lenient: () => (0, exports.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 200,
        message: '请求过于频繁，请稍后再试',
    }),
    api: () => (0, exports.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 1000,
        message: 'API 请求过于频繁，请稍后再试',
        skipSuccessfulRequests: true,
    }),
};
//# sourceMappingURL=rateLimit.js.map