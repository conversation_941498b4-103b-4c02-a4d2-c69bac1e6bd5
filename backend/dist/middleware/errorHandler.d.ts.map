{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAC1D,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAKpC,qBAAa,WAAY,SAAQ,KAAM,YAAW,QAAQ;IACjD,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,GAAG,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAUpF;AAGD,eAAO,MAAM,WAAW;0BACA,MAAM,YAAY,GAAG;6BAGnB,MAAM,YAAsB,GAAG;0BAGlC,MAAM,YAAqB,GAAG;yBAG/B,MAAM,YAAsB,GAAG;wBAG/B,MAAM,YAAY,GAAG;mCAGV,MAAM,YAAY,GAAG;gCAGzB,MAAM,YAAuB,GAAG;oCAG5B,MAAM,YAAwB,GAAG;mCAGlC,MAAM,YAAsB,GAAG;CAE9D,CAAC;AAGF,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,GAAG,QAAQ,EACvB,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IAkFF,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAGjF,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC"}