"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateParams = exports.validateQuery = exports.validateBody = void 0;
const logger_1 = require("../config/logger");
const validateBody = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true,
        });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            logger_1.log.warn('请求体验证失败', {
                path: req.path,
                method: req.method,
                error: errorMessage,
                body: req.body
            });
            res.status(400).json({
                success: false,
                message: '请求参数验证失败',
                errors: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                })),
                timestamp: new Date().toISOString(),
            });
            return;
        }
        req.body = value;
        next();
    };
};
exports.validateBody = validateBody;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.query, {
            abortEarly: false,
            stripUnknown: true,
        });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            logger_1.log.warn('查询参数验证失败', {
                path: req.path,
                method: req.method,
                error: errorMessage,
                query: req.query
            });
            res.status(400).json({
                success: false,
                message: '查询参数验证失败',
                errors: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                })),
                timestamp: new Date().toISOString(),
            });
            return;
        }
        req.query = value;
        next();
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.params, {
            abortEarly: false,
            stripUnknown: true,
        });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            logger_1.log.warn('路径参数验证失败', {
                path: req.path,
                method: req.method,
                error: errorMessage,
                params: req.params
            });
            res.status(400).json({
                success: false,
                message: '路径参数验证失败',
                errors: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                })),
                timestamp: new Date().toISOString(),
            });
            return;
        }
        req.params = value;
        next();
    };
};
exports.validateParams = validateParams;
//# sourceMappingURL=validation.js.map