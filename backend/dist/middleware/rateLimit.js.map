{"version": 3, "file": "rateLimit.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimit.ts"], "names": [], "mappings": ";;;AACA,6CAAuC;AAmBvC,MAAM,KAAK,GAAmB,EAAE,CAAC;AAGjC,WAAW,CAAC,GAAG,EAAE;IACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,EAAE,KAAK,CAAC,CAAC;AAEH,MAAM,SAAS,GAAG,CAAC,OAAyB,EAAE,EAAE;IACrD,MAAM,EACJ,QAAQ,EACR,GAAG,EACH,OAAO,GAAG,cAAc,EACxB,sBAAsB,GAAG,KAAK,EAC9B,kBAAkB,GAAG,KAAK,EAC1B,YAAY,GAAG,CAAC,GAAY,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,SAAS,GACrD,GAAG,OAAO,CAAC;IAEZ,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAC;QAGjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YAC9C,KAAK,CAAC,GAAG,CAAC,GAAG;gBACX,KAAK,EAAE,CAAC;gBACR,SAAS;aACV,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAG1B,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC;YACxB,YAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACjB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,GAAG;gBACH,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;aACpD,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;gBACtD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,CAAC,KAAK,EAAE,CAAC;QAGf,GAAG,CAAC,GAAG,CAAC;YACN,mBAAmB,EAAE,GAAG,CAAC,QAAQ,EAAE;YACnC,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;YACnE,mBAAmB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;SAC9D,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;QAC9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAI;YACtB,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YAGlC,IACE,CAAC,sBAAsB,IAAI,UAAU,GAAG,GAAG,CAAC;gBAC5C,CAAC,kBAAkB,IAAI,UAAU,IAAI,GAAG,CAAC,EACzC,CAAC;gBACD,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;YAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzEW,QAAA,SAAS,aAyEpB;AAGW,QAAA,eAAe,GAAG;IAE7B,MAAM,EAAE,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC;QACtB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxB,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,cAAc;KACxB,CAAC;IAGF,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC;QACxB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxB,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,cAAc;KACxB,CAAC;IAGF,OAAO,EAAE,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC;QACvB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxB,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,cAAc;KACxB,CAAC;IAGF,GAAG,EAAE,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC;QACnB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxB,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,kBAAkB;QAC3B,sBAAsB,EAAE,IAAI;KAC7B,CAAC;CACH,CAAC"}