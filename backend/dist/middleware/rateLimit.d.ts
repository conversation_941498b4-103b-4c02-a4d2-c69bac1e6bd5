import { Request, Response, NextFunction } from 'express';
interface RateLimitOptions {
    windowMs: number;
    max: number;
    message?: string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: Request) => string;
}
export declare const rateLimit: (options: RateLimitOptions) => (req: Request, res: Response, next: NextFunction) => void;
export declare const createRateLimit: {
    strict: () => (req: Request, res: Response, next: NextFunction) => void;
    moderate: () => (req: Request, res: Response, next: NextFunction) => void;
    lenient: () => (req: Request, res: Response, next: NextFunction) => void;
    api: () => (req: Request, res: Response, next: NextFunction) => void;
};
export {};
//# sourceMappingURL=rateLimit.d.ts.map