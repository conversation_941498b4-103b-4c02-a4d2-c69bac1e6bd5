interface Config {
    server: {
        port: number;
        host: string;
        env: string;
    };
    database: {
        mongodb: {
            uri: string;
        };
        redis: {
            url: string;
        };
    };
    jwt: {
        secret: string;
        expiresIn: string;
    };
    cors: {
        origin: string;
    };
    logging: {
        level: string;
        dir: string;
    };
    websocket: {
        corsOrigin: string;
    };
    dataRefresh: {
        interval: number;
    };
    systemMonitor: {
        interval: number;
    };
    mockData: {
        enabled: boolean;
        interval: number;
    };
}
declare const config: Config;
export default config;
//# sourceMappingURL=index.d.ts.map