"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.database = exports.DatabaseConnection = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const redis_1 = require("redis");
const logger_1 = require("./logger");
const index_1 = __importDefault(require("./index"));
class DatabaseConnection {
    constructor() {
        this.mongoConnection = null;
        this.redisClient = null;
    }
    static getInstance() {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection();
        }
        return DatabaseConnection.instance;
    }
    async connectMongoDB() {
        try {
            if (this.mongoConnection) {
                logger_1.log.info('MongoDB 已连接');
                return;
            }
            logger_1.log.info('正在连接 MongoDB...', { uri: index_1.default.database.mongodb.uri });
            const options = {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                bufferMaxEntries: 0,
                bufferCommands: false,
            };
            this.mongoConnection = await mongoose_1.default.connect(index_1.default.database.mongodb.uri, options);
            mongoose_1.default.connection.on('connected', () => {
                logger_1.log.info('MongoDB 连接成功');
            });
            mongoose_1.default.connection.on('error', (error) => {
                logger_1.log.error('MongoDB 连接错误', { error });
            });
            mongoose_1.default.connection.on('disconnected', () => {
                logger_1.log.warn('MongoDB 连接断开');
            });
            process.on('SIGINT', async () => {
                await this.disconnectMongoDB();
                process.exit(0);
            });
        }
        catch (error) {
            logger_1.log.error('MongoDB 连接失败', { error });
            throw error;
        }
    }
    async disconnectMongoDB() {
        try {
            if (this.mongoConnection) {
                await mongoose_1.default.disconnect();
                this.mongoConnection = null;
                logger_1.log.info('MongoDB 连接已断开');
            }
        }
        catch (error) {
            logger_1.log.error('MongoDB 断开连接失败', { error });
            throw error;
        }
    }
    async connectRedis() {
        try {
            if (this.redisClient && this.redisClient.isOpen) {
                logger_1.log.info('Redis 已连接');
                return;
            }
            logger_1.log.info('正在连接 Redis...', { url: index_1.default.database.redis.url });
            this.redisClient = (0, redis_1.createClient)({
                url: index_1.default.database.redis.url,
                socket: {
                    reconnectStrategy: (retries) => Math.min(retries * 50, 500),
                },
            });
            this.redisClient.on('connect', () => {
                logger_1.log.info('Redis 连接成功');
            });
            this.redisClient.on('error', (error) => {
                logger_1.log.error('Redis 连接错误', { error });
            });
            this.redisClient.on('end', () => {
                logger_1.log.warn('Redis 连接断开');
            });
            await this.redisClient.connect();
        }
        catch (error) {
            logger_1.log.error('Redis 连接失败', { error });
            logger_1.log.warn('应用将在没有 Redis 的情况下运行');
        }
    }
    async disconnectRedis() {
        try {
            if (this.redisClient && this.redisClient.isOpen) {
                await this.redisClient.quit();
                this.redisClient = null;
                logger_1.log.info('Redis 连接已断开');
            }
        }
        catch (error) {
            logger_1.log.error('Redis 断开连接失败', { error });
        }
    }
    getMongoConnection() {
        return this.mongoConnection;
    }
    getRedisClient() {
        return this.redisClient;
    }
    isMongoConnected() {
        return mongoose_1.default.connection.readyState === 1;
    }
    isRedisConnected() {
        return this.redisClient && this.redisClient.isOpen;
    }
    async connectAll() {
        await Promise.all([
            this.connectMongoDB(),
            this.connectRedis(),
        ]);
    }
    async disconnectAll() {
        await Promise.all([
            this.disconnectMongoDB(),
            this.disconnectRedis(),
        ]);
    }
}
exports.DatabaseConnection = DatabaseConnection;
exports.database = DatabaseConnection.getInstance();
//# sourceMappingURL=database.js.map