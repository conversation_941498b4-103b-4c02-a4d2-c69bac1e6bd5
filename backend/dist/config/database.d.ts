import mongoose from 'mongoose';
export declare class DatabaseConnection {
    private static instance;
    private mongoConnection;
    private redisClient;
    private constructor();
    static getInstance(): DatabaseConnection;
    connectMongoDB(): Promise<void>;
    disconnectMongoDB(): Promise<void>;
    connectRedis(): Promise<void>;
    disconnectRedis(): Promise<void>;
    getMongoConnection(): typeof mongoose | null;
    getRedisClient(): any;
    isMongoConnected(): boolean;
    isRedisConnected(): boolean;
    connectAll(): Promise<void>;
    disconnectAll(): Promise<void>;
}
export declare const database: DatabaseConnection;
//# sourceMappingURL=database.d.ts.map