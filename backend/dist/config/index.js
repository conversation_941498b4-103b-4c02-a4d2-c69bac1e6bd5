"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const envFile = process.env.NODE_ENV === 'production'
    ? '.env.production'
    : process.env.NODE_ENV === 'test'
        ? '.env.test'
        : '.env.development';
dotenv_1.default.config({ path: path_1.default.resolve(process.cwd(), envFile) });
const config = {
    server: {
        port: parseInt(process.env.PORT || '8080', 10),
        host: process.env.HOST || '0.0.0.0',
        env: process.env.NODE_ENV || 'development',
    },
    database: {
        mongodb: {
            uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/weibo_sentiment',
        },
        redis: {
            url: process.env.REDIS_URL || 'redis://localhost:6379',
        },
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'default-secret-key',
        expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    },
    cors: {
        origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        dir: process.env.LOG_DIR || 'logs',
    },
    websocket: {
        corsOrigin: process.env.WS_CORS_ORIGIN || 'http://localhost:3001',
    },
    dataRefresh: {
        interval: parseInt(process.env.DATA_REFRESH_INTERVAL || '30000', 10),
    },
    systemMonitor: {
        interval: parseInt(process.env.SYSTEM_MONITOR_INTERVAL || '5000', 10),
    },
    mockData: {
        enabled: process.env.ENABLE_MOCK_DATA === 'true',
        interval: parseInt(process.env.MOCK_DATA_INTERVAL || '10000', 10),
    },
};
const validateConfig = () => {
    const requiredFields = [
        'server.port',
        'database.mongodb.uri',
        'jwt.secret',
    ];
    for (const field of requiredFields) {
        const keys = field.split('.');
        let value = config;
        for (const key of keys) {
            value = value[key];
        }
        if (!value) {
            throw new Error(`Missing required configuration: ${field}`);
        }
    }
    if (config.server.port < 1 || config.server.port > 65535) {
        throw new Error('Port must be between 1 and 65535');
    }
    if (config.jwt.secret.length < 8) {
        throw new Error('JWT secret must be at least 8 characters long');
    }
};
validateConfig();
exports.default = config;
//# sourceMappingURL=index.js.map