import express from 'express';
import http from 'http';
import { WebSocketService } from './services/websocketService';
import { DataGeneratorService } from './services/dataGeneratorService';
declare class Server {
    private app;
    private httpServer;
    private websocketService;
    private dataGeneratorService;
    constructor();
    private initializeMiddleware;
    private initializeRoutes;
    private initializeDatabase;
    start(): Promise<void>;
    private setupGracefulShutdown;
    getApp(): express.Application;
    getHttpServer(): http.Server;
    getWebSocketService(): WebSocketService;
    getDataGeneratorService(): DataGeneratorService;
}
declare const server: Server;
export default server;
//# sourceMappingURL=server.d.ts.map