"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentimentModel = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const SentimentSchema = new mongoose_1.Schema({
    content: {
        type: String,
        required: true,
        maxlength: 2000,
        index: 'text',
    },
    sentiment: {
        type: String,
        enum: ['positive', 'negative', 'neutral'],
        required: true,
        index: true,
    },
    score: {
        type: Number,
        required: true,
        min: 0,
        max: 1,
    },
    source: {
        type: String,
        enum: ['weibo', 'zhihu', 'news', 'other'],
        required: true,
        index: true,
    },
    author: {
        type: String,
        required: true,
        maxlength: 100,
    },
    platform: {
        type: String,
        required: true,
        maxlength: 50,
    },
    url: {
        type: String,
        maxlength: 500,
    },
    tags: [{
            type: String,
            maxlength: 50,
        }],
    location: {
        type: String,
        maxlength: 100,
        index: true,
    },
    timestamp: {
        type: Date,
        required: true,
        index: true,
    },
}, {
    timestamps: true,
    collection: 'sentiments',
});
SentimentSchema.index({ timestamp: -1, sentiment: 1 });
SentimentSchema.index({ source: 1, timestamp: -1 });
SentimentSchema.index({ location: 1, sentiment: 1 });
SentimentSchema.index({ tags: 1, sentiment: 1 });
SentimentSchema.virtual('id').get(function () {
    return this._id.toHexString();
});
SentimentSchema.set('toJSON', {
    virtuals: true,
    transform: function (_doc, ret) {
        delete ret._id;
        delete ret.__v;
        return ret;
    },
});
exports.SentimentModel = mongoose_1.default.model('Sentiment', SentimentSchema);
//# sourceMappingURL=SentimentModel.js.map