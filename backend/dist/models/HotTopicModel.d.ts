import mongoose, { Document } from 'mongoose';
import { SentimentType, TrendType } from '../types';
export interface IHotTopicDocument extends Document {
    title: string;
    count: number;
    sentiment: SentimentType;
    keywords: string[];
    trend: TrendType;
    trendValue: number;
}
export declare const HotTopicModel: mongoose.Model<IHotTopicDocument, {}, {}, {}, mongoose.Document<unknown, {}, IHotTopicDocument, {}, {}> & IHotTopicDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=HotTopicModel.d.ts.map