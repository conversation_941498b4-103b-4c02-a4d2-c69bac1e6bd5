import mongoose, { Document } from 'mongoose';
import { SentimentType, DataSourceType } from '../types';
export interface ISentimentDocument extends Document {
    content: string;
    sentiment: SentimentType;
    score: number;
    source: DataSourceType;
    author: string;
    platform: string;
    url?: string;
    tags: string[];
    location?: string;
    timestamp: Date;
}
export declare const SentimentModel: mongoose.Model<ISentimentDocument, {}, {}, {}, mongoose.Document<unknown, {}, ISentimentDocument, {}, {}> & ISentimentDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=SentimentModel.d.ts.map