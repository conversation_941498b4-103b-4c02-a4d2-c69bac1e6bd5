"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotTopicModel = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const HotTopicSchema = new mongoose_1.Schema({
    title: {
        type: String,
        required: true,
        maxlength: 200,
        index: 'text',
    },
    count: {
        type: Number,
        required: true,
        min: 0,
        index: true,
    },
    sentiment: {
        type: String,
        enum: ['positive', 'negative', 'neutral'],
        required: true,
        index: true,
    },
    keywords: [{
            type: String,
            maxlength: 50,
        }],
    trend: {
        type: String,
        enum: ['up', 'down', 'stable'],
        required: true,
        default: 'stable',
    },
    trendValue: {
        type: Number,
        required: true,
        default: 0,
    },
}, {
    timestamps: true,
    collection: 'hot_topics',
});
HotTopicSchema.index({ count: -1, createdAt: -1 });
HotTopicSchema.index({ sentiment: 1, count: -1 });
HotTopicSchema.index({ trend: 1, trendValue: -1 });
HotTopicSchema.virtual('id').get(function () {
    return this._id.toHexString();
});
HotTopicSchema.set('toJSON', {
    virtuals: true,
    transform: function (_doc, ret) {
        delete ret._id;
        delete ret.__v;
        return ret;
    },
});
exports.HotTopicModel = mongoose_1.default.model('HotTopic', HotTopicSchema);
//# sourceMappingURL=HotTopicModel.js.map