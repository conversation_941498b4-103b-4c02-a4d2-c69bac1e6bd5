import { WebSocketService } from './websocketService';
export declare class DataGeneratorService {
    private sentimentService;
    private systemService;
    private websocketService;
    private isRunning;
    private cronJobs;
    constructor();
    setWebSocketService(websocketService: WebSocketService): void;
    start(): void;
    stop(): void;
    private startMockDataGeneration;
    private startSystemMonitoring;
    private startHeartbeat;
    private generateAndPushData;
    private updateStatistics;
    private updateHotTopics;
    private updateKeywords;
    private updateTimeSeries;
    private updateLocations;
    private generateNewPost;
    private collectSystemMetrics;
    private sendAlert;
    triggerDataUpdate(): Promise<void>;
    getStatus(): {
        isRunning: boolean;
        cronJobsCount: number;
    };
}
//# sourceMappingURL=dataGeneratorService.d.ts.map