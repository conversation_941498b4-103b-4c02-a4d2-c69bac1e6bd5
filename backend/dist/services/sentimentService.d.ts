import { StatisticsData, HotTopic, KeywordData, TimeSeriesData, LocationData, SentimentData, RealTimeData, SentimentQuery, SearchQuery, PaginatedResponse } from '../types';
export declare class SentimentService {
    private random;
    private randomSentiment;
    private randomTrend;
    private randomSource;
    private generateId;
    getRealTimeData(timeRange: string): Promise<RealTimeData>;
    getStatistics(timeRange: string): Promise<StatisticsData>;
    getHotTopics(limit: number): Promise<HotTopic[]>;
    getKeywords(limit: number): Promise<KeywordData[]>;
    getTimeSeries(timeRange: string): Promise<TimeSeriesData[]>;
    getLocationData(): Promise<LocationData[]>;
    getRecentPosts(limit: number): Promise<SentimentData[]>;
    search(query: SearchQuery): Promise<SentimentData[]>;
    getSentimentList(query: SentimentQuery): Promise<PaginatedResponse<SentimentData>>;
    private getHoursFromRange;
}
//# sourceMappingURL=sentimentService.d.ts.map