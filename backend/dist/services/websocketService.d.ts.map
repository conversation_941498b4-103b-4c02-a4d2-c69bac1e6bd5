{"version": 3, "file": "websocketService.d.ts", "sourceRoot": "", "sources": ["../../src/services/websocketService.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,MAAM,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAI1D,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,EAAE,CAAiB;IAC3B,OAAO,CAAC,gBAAgB,CAAkC;gBAE9C,UAAU,EAAE,UAAU;IAclC,OAAO,CAAC,kBAAkB;IAkEnB,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAa1C,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAe/D,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAU/D,sBAAsB,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI;IAahD,oBAAoB,CAAC,UAAU,EAAE,GAAG,GAAG,IAAI;IAa3C,mBAAmB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI;IAa3C,kBAAkB,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI;IAazC,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,IAAI;IAa7C,sBAAsB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI;IAa9C,uBAAuB,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;IAaxC,SAAS,CAAC,SAAS,EAAE,GAAG,GAAG,IAAI;IAU/B,aAAa,IAAI,IAAI;IAarB,wBAAwB,IAAI,MAAM;IAKlC,qBAAqB,IAAI,MAAM,EAAE;IAKjC,KAAK,IAAI,IAAI;CAKrB"}