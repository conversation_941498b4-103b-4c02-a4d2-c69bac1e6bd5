"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = void 0;
const socket_io_1 = require("socket.io");
const logger_1 = require("../config/logger");
const config_1 = __importDefault(require("../config"));
class WebSocketService {
    constructor(httpServer) {
        this.connectedClients = new Map();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: config_1.default.websocket.corsOrigin,
                methods: ['GET', 'POST'],
                credentials: true,
            },
            transports: ['websocket', 'polling'],
        });
        this.setupEventHandlers();
        logger_1.log.info('WebSocket 服务初始化完成');
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            const clientId = socket.id;
            this.connectedClients.set(clientId, socket);
            logger_1.log.info('客户端连接', { clientId, totalClients: this.connectedClients.size });
            socket.emit('connected', {
                message: '连接成功',
                clientId,
                timestamp: new Date().toISOString(),
            });
            socket.on('disconnect', (reason) => {
                this.connectedClients.delete(clientId);
                logger_1.log.info('客户端断开连接', {
                    clientId,
                    reason,
                    totalClients: this.connectedClients.size
                });
            });
            socket.on('ping', () => {
                socket.emit('pong', {
                    timestamp: new Date().toISOString(),
                });
            });
            socket.on('subscribe', (data) => {
                const { channels } = data;
                if (Array.isArray(channels)) {
                    channels.forEach((channel) => {
                        socket.join(channel);
                        logger_1.log.debug('客户端订阅频道', { clientId, channel });
                    });
                }
            });
            socket.on('unsubscribe', (data) => {
                const { channels } = data;
                if (Array.isArray(channels)) {
                    channels.forEach((channel) => {
                        socket.leave(channel);
                        logger_1.log.debug('客户端取消订阅频道', { clientId, channel });
                    });
                }
            });
            socket.on('error', (error) => {
                logger_1.log.error('WebSocket 客户端错误', { clientId, error });
            });
        });
        this.io.on('error', (error) => {
            logger_1.log.error('WebSocket 服务器错误', { error });
        });
    }
    broadcast(message) {
        try {
            this.io.emit('data:update', message);
            logger_1.log.debug('广播消息', {
                type: message.type,
                clients: this.connectedClients.size
            });
        }
        catch (error) {
            logger_1.log.error('广播消息失败', { error });
        }
    }
    sendToClient(clientId, message) {
        try {
            const socket = this.connectedClients.get(clientId);
            if (socket) {
                socket.emit('data:update', message);
                logger_1.log.debug('发送消息给客户端', { clientId, type: message.type });
            }
            else {
                logger_1.log.warn('客户端不存在', { clientId });
            }
        }
        catch (error) {
            logger_1.log.error('发送消息给客户端失败', { clientId, error });
        }
    }
    sendToChannel(channel, message) {
        try {
            this.io.to(channel).emit('data:update', message);
            logger_1.log.debug('发送消息给频道', { channel, type: message.type });
        }
        catch (error) {
            logger_1.log.error('发送消息给频道失败', { channel, error });
        }
    }
    sendRealTimeDataUpdate(data) {
        const message = {
            type: 'update',
            data: {
                type: 'realtime',
                payload: data,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendStatisticsUpdate(statistics) {
        const message = {
            type: 'update',
            data: {
                type: 'statistics',
                payload: statistics,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendHotTopicsUpdate(hotTopics) {
        const message = {
            type: 'update',
            data: {
                type: 'hotTopics',
                payload: hotTopics,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendKeywordsUpdate(keywords) {
        const message = {
            type: 'update',
            data: {
                type: 'keywords',
                payload: keywords,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendTimeSeriesUpdate(timeSeries) {
        const message = {
            type: 'update',
            data: {
                type: 'timeSeries',
                payload: timeSeries,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendLocationDataUpdate(locations) {
        const message = {
            type: 'update',
            data: {
                type: 'locations',
                payload: locations,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendNewPostNotification(post) {
        const message = {
            type: 'update',
            data: {
                type: 'newPost',
                payload: post,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendAlert(alertData) {
        const message = {
            type: 'alert',
            data: alertData,
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    sendHeartbeat() {
        const message = {
            type: 'heartbeat',
            data: {
                timestamp: new Date().toISOString(),
                connectedClients: this.connectedClients.size,
            },
            timestamp: new Date(),
        };
        this.broadcast(message);
    }
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    getConnectedClientIds() {
        return Array.from(this.connectedClients.keys());
    }
    close() {
        this.io.close();
        this.connectedClients.clear();
        logger_1.log.info('WebSocket 服务已关闭');
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=websocketService.js.map