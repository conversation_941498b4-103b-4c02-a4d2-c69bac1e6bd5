"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentimentService = void 0;
const logger_1 = require("../config/logger");
class SentimentService {
    random(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    randomSentiment() {
        const sentiments = ['positive', 'negative', 'neutral'];
        return sentiments[this.random(0, 2)];
    }
    randomTrend() {
        const trends = ['up', 'down', 'stable'];
        return trends[this.random(0, 2)];
    }
    randomSource() {
        const sources = ['weibo', 'zhihu', 'news', 'other'];
        return sources[this.random(0, 3)];
    }
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
    async getRealTimeData(timeRange) {
        try {
            logger_1.log.info('获取实时数据', { timeRange });
            const [statistics, hotTopics, keywords, timeSeries, locations, recentPosts] = await Promise.all([
                this.getStatistics(timeRange),
                this.getHotTopics(10),
                this.getKeywords(50),
                this.getTimeSeries(timeRange),
                this.getLocationData(),
                this.getRecentPosts(20),
            ]);
            return {
                statistics,
                hotTopics,
                keywords,
                timeSeries,
                locations,
                recentPosts,
            };
        }
        catch (error) {
            logger_1.log.error('获取实时数据失败', { error });
            throw error;
        }
    }
    async getStatistics(timeRange) {
        try {
            logger_1.log.debug('获取统计数据', { timeRange });
            const total = this.random(10000, 50000);
            const positive = this.random(Math.floor(total * 0.2), Math.floor(total * 0.5));
            const negative = this.random(Math.floor(total * 0.1), Math.floor(total * 0.3));
            const neutral = total - positive - negative;
            return {
                total,
                positive,
                negative,
                neutral,
                growth: this.random(-1000, 2000),
                growthRate: this.random(-10, 25),
            };
        }
        catch (error) {
            logger_1.log.error('获取统计数据失败', { error });
            throw error;
        }
    }
    async getHotTopics(limit) {
        try {
            logger_1.log.debug('获取热点话题', { limit });
            const topics = [
                '人工智能发展趋势',
                '新能源汽车市场',
                '疫情防控政策',
                '教育改革方案',
                '房地产市场调控',
                '科技创新发展',
                '环保政策实施',
                '医疗健康服务',
                '数字经济建设',
                '文化产业发展',
                '体育赛事举办',
                '旅游业复苏',
                '金融市场波动',
                '就业形势分析',
                '社会保障制度',
            ];
            return Array.from({ length: Math.min(limit, topics.length) }, (_, index) => ({
                id: this.generateId(),
                title: topics[this.random(0, topics.length - 1)],
                count: this.random(100, 5000),
                sentiment: this.randomSentiment(),
                keywords: ['关键词1', '关键词2', '关键词3'],
                trend: this.randomTrend(),
                trendValue: this.random(-50, 100),
                createdAt: new Date(),
                updatedAt: new Date(),
            }));
        }
        catch (error) {
            logger_1.log.error('获取热点话题失败', { error });
            throw error;
        }
    }
    async getKeywords(limit) {
        try {
            logger_1.log.debug('获取关键词', { limit });
            const keywords = [
                '人工智能', '机器学习', '深度学习', '大数据', '云计算',
                '区块链', '物联网', '5G', '新能源', '电动汽车',
                '疫情', '防控', '疫苗', '健康', '医疗',
                '教育', '改革', '学校', '学生', '老师',
                '房价', '楼市', '调控', '政策', '投资',
                '创新', '科技', '研发', '技术', '专利',
                '环保', '绿色', '节能', '减排', '生态',
                '数字化', '智能化', '自动化', '信息化', '网络',
                '文化', '艺术', '娱乐', '影视', '音乐',
                '体育', '运动', '健身', '比赛', '奥运',
            ];
            return Array.from({ length: Math.min(limit, keywords.length) }, (_, index) => ({
                name: keywords[this.random(0, keywords.length - 1)],
                value: this.random(10, 1000),
                sentiment: this.randomSentiment(),
            }));
        }
        catch (error) {
            logger_1.log.error('获取关键词失败', { error });
            throw error;
        }
    }
    async getTimeSeries(timeRange) {
        try {
            logger_1.log.debug('获取时间序列数据', { timeRange });
            const hours = this.getHoursFromRange(timeRange);
            const now = new Date();
            return Array.from({ length: hours }, (_, index) => {
                const timestamp = new Date(now.getTime() - (hours - index - 1) * 60 * 60 * 1000);
                const total = this.random(100, 1000);
                const positive = this.random(Math.floor(total * 0.2), Math.floor(total * 0.5));
                const negative = this.random(Math.floor(total * 0.1), Math.floor(total * 0.3));
                const neutral = total - positive - negative;
                return {
                    timestamp,
                    value: total,
                    positive,
                    negative,
                    neutral,
                };
            });
        }
        catch (error) {
            logger_1.log.error('获取时间序列数据失败', { error });
            throw error;
        }
    }
    async getLocationData() {
        try {
            logger_1.log.debug('获取地理位置数据');
            const locations = [
                { name: '北京', coordinates: [116.4074, 39.9042] },
                { name: '上海', coordinates: [121.4737, 31.2304] },
                { name: '广州', coordinates: [113.2644, 23.1291] },
                { name: '深圳', coordinates: [114.0579, 22.5431] },
                { name: '杭州', coordinates: [120.1551, 30.2741] },
                { name: '南京', coordinates: [118.7969, 32.0603] },
                { name: '武汉', coordinates: [114.3054, 30.5931] },
                { name: '成都', coordinates: [104.0665, 30.5723] },
                { name: '西安', coordinates: [108.9402, 34.3416] },
                { name: '重庆', coordinates: [106.5516, 29.5630] },
            ];
            return locations.map(location => ({
                name: location.name,
                value: this.random(50, 500),
                sentiment: this.randomSentiment(),
                coordinates: location.coordinates,
            }));
        }
        catch (error) {
            logger_1.log.error('获取地理位置数据失败', { error });
            throw error;
        }
    }
    async getRecentPosts(limit) {
        try {
            logger_1.log.debug('获取最新帖子', { limit });
            const contents = [
                '今天的天气真不错，心情也很好！',
                '新的政策出台了，希望能带来积极的变化。',
                '这个产品的质量有待提高，用户体验不太好。',
                '科技发展真是日新月异，令人惊叹。',
                '教育改革需要更多的时间和耐心。',
                '环保意识越来越重要了。',
                '医疗服务质量有了明显提升。',
                '数字化转型是大势所趋。',
                '文化产业发展前景广阔。',
                '体育运动对健康很重要。',
            ];
            const authors = ['用户A', '用户B', '用户C', '用户D', '用户E'];
            return Array.from({ length: limit }, () => ({
                id: this.generateId(),
                content: contents[this.random(0, contents.length - 1)],
                sentiment: this.randomSentiment(),
                score: this.random(0, 100) / 100,
                source: this.randomSource(),
                author: authors[this.random(0, authors.length - 1)],
                platform: '微博',
                tags: ['标签1', '标签2'],
                timestamp: new Date(Date.now() - this.random(0, 24 * 60 * 60 * 1000)),
                createdAt: new Date(),
                updatedAt: new Date(),
            }));
        }
        catch (error) {
            logger_1.log.error('获取最新帖子失败', { error });
            throw error;
        }
    }
    async search(query) {
        try {
            logger_1.log.debug('搜索内容', { query: query.query });
            const results = await this.getRecentPosts(10);
            return results.filter(post => post.content.includes(query.query) ||
                post.tags.some(tag => tag.includes(query.query)));
        }
        catch (error) {
            logger_1.log.error('搜索内容失败', { error });
            throw error;
        }
    }
    async getSentimentList(query) {
        try {
            logger_1.log.debug('获取舆情数据列表', { query });
            const page = query.page || 1;
            const limit = query.limit || 20;
            const total = this.random(1000, 5000);
            const pages = Math.ceil(total / limit);
            const data = await this.getRecentPosts(limit);
            return {
                data,
                pagination: {
                    page,
                    limit,
                    total,
                    pages,
                },
            };
        }
        catch (error) {
            logger_1.log.error('获取舆情数据列表失败', { error });
            throw error;
        }
    }
    getHoursFromRange(timeRange) {
        switch (timeRange) {
            case '1h':
                return 1;
            case '6h':
                return 6;
            case '24h':
                return 24;
            case '7d':
                return 168;
            case '30d':
                return 720;
            default:
                return 24;
        }
    }
}
exports.SentimentService = SentimentService;
//# sourceMappingURL=sentimentService.js.map