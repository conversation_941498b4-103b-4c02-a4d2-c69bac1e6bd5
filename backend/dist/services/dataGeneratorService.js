"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataGeneratorService = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const sentimentService_1 = require("./sentimentService");
const systemService_1 = require("./systemService");
const logger_1 = require("../config/logger");
const config_1 = __importDefault(require("../config"));
class DataGeneratorService {
    constructor() {
        this.websocketService = null;
        this.isRunning = false;
        this.cronJobs = [];
        this.sentimentService = new sentimentService_1.SentimentService();
        this.systemService = new systemService_1.SystemService();
    }
    setWebSocketService(websocketService) {
        this.websocketService = websocketService;
    }
    start() {
        if (this.isRunning) {
            logger_1.log.warn('数据生成器已在运行');
            return;
        }
        this.isRunning = true;
        logger_1.log.info('启动数据生成器');
        if (config_1.default.mockData.enabled) {
            this.startMockDataGeneration();
        }
        this.startSystemMonitoring();
        this.startHeartbeat();
    }
    stop() {
        if (!this.isRunning) {
            logger_1.log.warn('数据生成器未在运行');
            return;
        }
        this.isRunning = false;
        logger_1.log.info('停止数据生成器');
        this.cronJobs.forEach(job => job.stop());
        this.cronJobs = [];
    }
    startMockDataGeneration() {
        logger_1.log.info('启动模拟数据生成', { interval: config_1.default.mockData.interval });
        const job = node_cron_1.default.schedule(`*/${Math.floor(config_1.default.mockData.interval / 1000)} * * * * *`, async () => {
            try {
                await this.generateAndPushData();
            }
            catch (error) {
                logger_1.log.error('生成模拟数据失败', { error });
            }
        });
        this.cronJobs.push(job);
    }
    startSystemMonitoring() {
        logger_1.log.info('启动系统监控', { interval: config_1.default.systemMonitor.interval });
        const job = node_cron_1.default.schedule(`*/${Math.floor(config_1.default.systemMonitor.interval / 1000)} * * * * *`, async () => {
            try {
                await this.collectSystemMetrics();
            }
            catch (error) {
                logger_1.log.error('收集系统指标失败', { error });
            }
        });
        this.cronJobs.push(job);
    }
    startHeartbeat() {
        logger_1.log.info('启动心跳服务');
        const job = node_cron_1.default.schedule('*/30 * * * * *', () => {
            try {
                if (this.websocketService) {
                    this.websocketService.sendHeartbeat();
                }
            }
            catch (error) {
                logger_1.log.error('发送心跳失败', { error });
            }
        });
        this.cronJobs.push(job);
    }
    async generateAndPushData() {
        try {
            const dataTypes = ['statistics', 'hotTopics', 'keywords', 'timeSeries', 'locations', 'newPost'];
            const randomType = dataTypes[Math.floor(Math.random() * dataTypes.length)];
            switch (randomType) {
                case 'statistics':
                    await this.updateStatistics();
                    break;
                case 'hotTopics':
                    await this.updateHotTopics();
                    break;
                case 'keywords':
                    await this.updateKeywords();
                    break;
                case 'timeSeries':
                    await this.updateTimeSeries();
                    break;
                case 'locations':
                    await this.updateLocations();
                    break;
                case 'newPost':
                    await this.generateNewPost();
                    break;
            }
            logger_1.log.debug('数据更新完成', { type: randomType });
        }
        catch (error) {
            logger_1.log.error('生成并推送数据失败', { error });
        }
    }
    async updateStatistics() {
        const statistics = await this.sentimentService.getStatistics('24h');
        if (this.websocketService) {
            this.websocketService.sendStatisticsUpdate(statistics);
        }
    }
    async updateHotTopics() {
        const hotTopics = await this.sentimentService.getHotTopics(10);
        if (this.websocketService) {
            this.websocketService.sendHotTopicsUpdate(hotTopics);
        }
    }
    async updateKeywords() {
        const keywords = await this.sentimentService.getKeywords(50);
        if (this.websocketService) {
            this.websocketService.sendKeywordsUpdate(keywords);
        }
    }
    async updateTimeSeries() {
        const timeSeries = await this.sentimentService.getTimeSeries('24h');
        if (this.websocketService) {
            this.websocketService.sendTimeSeriesUpdate(timeSeries);
        }
    }
    async updateLocations() {
        const locations = await this.sentimentService.getLocationData();
        if (this.websocketService) {
            this.websocketService.sendLocationDataUpdate(locations);
        }
    }
    async generateNewPost() {
        const recentPosts = await this.sentimentService.getRecentPosts(1);
        if (recentPosts.length > 0 && this.websocketService) {
            this.websocketService.sendNewPostNotification(recentPosts[0]);
        }
    }
    async collectSystemMetrics() {
        try {
            const systemStatus = await this.systemService.getSystemStatus();
            logger_1.log.debug('系统指标', {
                cpu: systemStatus.performance.cpu,
                memory: systemStatus.performance.memory,
                network: systemStatus.performance.network,
                connectedClients: this.websocketService?.getConnectedClientsCount() || 0,
            });
            if (systemStatus.performance.cpu > 80) {
                this.sendAlert('CPU 使用率过高', {
                    cpu: systemStatus.performance.cpu,
                    threshold: 80,
                });
            }
            if (systemStatus.performance.memory > 80) {
                this.sendAlert('内存使用率过高', {
                    memory: systemStatus.performance.memory,
                    threshold: 80,
                });
            }
        }
        catch (error) {
            logger_1.log.error('收集系统指标失败', { error });
        }
    }
    sendAlert(message, data) {
        try {
            if (this.websocketService) {
                this.websocketService.sendAlert({
                    message,
                    data,
                    level: 'warning',
                    timestamp: new Date().toISOString(),
                });
            }
            logger_1.log.warn('系统警告', { message, data });
        }
        catch (error) {
            logger_1.log.error('发送警告失败', { error });
        }
    }
    async triggerDataUpdate() {
        try {
            logger_1.log.info('手动触发数据更新');
            const realTimeData = await this.sentimentService.getRealTimeData('24h');
            if (this.websocketService) {
                this.websocketService.sendRealTimeDataUpdate(realTimeData);
            }
            logger_1.log.info('数据更新完成');
        }
        catch (error) {
            logger_1.log.error('手动触发数据更新失败', { error });
            throw error;
        }
    }
    getStatus() {
        return {
            isRunning: this.isRunning,
            cronJobsCount: this.cronJobs.length,
        };
    }
}
exports.DataGeneratorService = DataGeneratorService;
//# sourceMappingURL=dataGeneratorService.js.map