import { SystemStatus, SystemPerformance } from '../types';
export declare class SystemService {
    getSystemStatus(): Promise<SystemStatus>;
    getPerformanceMetrics(): Promise<SystemPerformance>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        details: any;
    }>;
    getSystemInfo(): Promise<any>;
    private getCpuInfo;
    private getMemoryInfo;
    private getDiskInfo;
    private getNetworkInfo;
    private getUptime;
    private getBasicPerformance;
    private checkDatabase;
    private checkRedis;
    private checkDiskSpace;
    private checkMemoryUsage;
    private getMockPerformanceMetrics;
}
//# sourceMappingURL=systemService.d.ts.map