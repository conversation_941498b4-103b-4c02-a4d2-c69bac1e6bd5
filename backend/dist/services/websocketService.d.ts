import { Server as HttpServer } from 'http';
import { WebSocketMessage, RealTimeData } from '../types';
export declare class WebSocketService {
    private io;
    private connectedClients;
    constructor(httpServer: HttpServer);
    private setupEventHandlers;
    broadcast(message: WebSocketMessage): void;
    sendToClient(clientId: string, message: WebSocketMessage): void;
    sendToChannel(channel: string, message: WebSocketMessage): void;
    sendRealTimeDataUpdate(data: RealTimeData): void;
    sendStatisticsUpdate(statistics: any): void;
    sendHotTopicsUpdate(hotTopics: any[]): void;
    sendKeywordsUpdate(keywords: any[]): void;
    sendTimeSeriesUpdate(timeSeries: any[]): void;
    sendLocationDataUpdate(locations: any[]): void;
    sendNewPostNotification(post: any): void;
    sendAlert(alertData: any): void;
    sendHeartbeat(): void;
    getConnectedClientsCount(): number;
    getConnectedClientIds(): string[];
    close(): void;
}
//# sourceMappingURL=websocketService.d.ts.map