"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const config_1 = __importDefault(require("./config"));
const logger_1 = require("./config/logger");
const database_1 = require("./config/database");
const websocketService_1 = require("./services/websocketService");
const dataGeneratorService_1 = require("./services/dataGeneratorService");
const errorHandler_1 = require("./middleware/errorHandler");
const routes_1 = __importDefault(require("./routes"));
class Server {
    constructor() {
        this.app = (0, express_1.default)();
        this.httpServer = http_1.default.createServer(this.app);
        this.websocketService = new websocketService_1.WebSocketService(this.httpServer);
        this.dataGeneratorService = new dataGeneratorService_1.DataGeneratorService();
        this.dataGeneratorService.setWebSocketService(this.websocketService);
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)({
            crossOriginEmbedderPolicy: false,
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: config_1.default.cors.origin,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization'],
        }));
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        if (config_1.default.server.env === 'development') {
            this.app.use((0, morgan_1.default)('dev'));
        }
        else {
            this.app.use((0, morgan_1.default)('combined', {
                stream: {
                    write: (message) => {
                        logger_1.log.info(message.trim());
                    },
                },
            }));
        }
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: config_1.default.server.env,
                version: process.env.npm_package_version || '1.0.0',
            });
        });
    }
    initializeRoutes() {
        this.app.use('/api', routes_1.default);
        this.app.use(errorHandler_1.notFoundHandler);
        this.app.use(errorHandler_1.errorHandler);
    }
    async initializeDatabase() {
        try {
            await database_1.database.connectAll();
            logger_1.log.info('数据库连接成功');
        }
        catch (error) {
            logger_1.log.error('数据库连接失败', { error });
            logger_1.log.warn('服务器将使用模拟数据运行');
        }
    }
    async start() {
        try {
            await this.initializeDatabase();
            this.initializeMiddleware();
            this.initializeRoutes();
            this.dataGeneratorService.start();
            this.httpServer.listen(config_1.default.server.port, config_1.default.server.host, () => {
                logger_1.log.info('服务器启动成功', {
                    host: config_1.default.server.host,
                    port: config_1.default.server.port,
                    env: config_1.default.server.env,
                    websocket: '已启用',
                    mockData: config_1.default.mockData.enabled ? '已启用' : '已禁用',
                });
                console.log(`
🚀 舆情监控大屏幕系统后端服务已启动

📡 HTTP 服务器: http://${config_1.default.server.host}:${config_1.default.server.port}
🔌 WebSocket 服务: ws://${config_1.default.server.host}:${config_1.default.server.port}
📊 API 文档: http://${config_1.default.server.host}:${config_1.default.server.port}/api
🏥 健康检查: http://${config_1.default.server.host}:${config_1.default.server.port}/health

🌍 环境: ${config_1.default.server.env}
📦 模拟数据: ${config_1.default.mockData.enabled ? '启用' : '禁用'}
🔄 数据刷新间隔: ${config_1.default.dataRefresh.interval}ms
📈 系统监控间隔: ${config_1.default.systemMonitor.interval}ms

按 Ctrl+C 停止服务器
        `);
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger_1.log.error('服务器启动失败', { error });
            process.exit(1);
        }
    }
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            logger_1.log.info(`收到 ${signal} 信号，开始优雅关闭...`);
            try {
                this.dataGeneratorService.stop();
                this.websocketService.close();
                this.httpServer.close(() => {
                    logger_1.log.info('HTTP 服务器已关闭');
                });
                await database_1.database.disconnectAll();
                logger_1.log.info('服务器优雅关闭完成');
                process.exit(0);
            }
            catch (error) {
                logger_1.log.error('优雅关闭失败', { error });
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            logger_1.log.error('未捕获的异常', { error });
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.log.error('未处理的 Promise 拒绝', { reason, promise });
            shutdown('unhandledRejection');
        });
    }
    getApp() {
        return this.app;
    }
    getHttpServer() {
        return this.httpServer;
    }
    getWebSocketService() {
        return this.websocketService;
    }
    getDataGeneratorService() {
        return this.dataGeneratorService;
    }
}
const server = new Server();
server.start().catch((error) => {
    logger_1.log.error('服务器启动失败', { error });
    process.exit(1);
});
exports.default = server;
//# sourceMappingURL=server.js.map