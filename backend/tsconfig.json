{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/controllers/*": ["./controllers/*"], "@/services/*": ["./services/*"], "@/models/*": ["./models/*"], "@/middleware/*": ["./middleware/*"], "@/utils/*": ["./utils/*"], "@/config/*": ["./config/*"], "@/routes/*": ["./routes/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}