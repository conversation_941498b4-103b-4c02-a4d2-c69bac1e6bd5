# 舆情监控大屏幕系统 - 后端服务

基于 Node.js + Express + Socket.IO + TypeScript 构建的舆情监控后端服务系统。

## 🚀 特性

- **RESTful API**: 完整的舆情数据 API 接口
- **WebSocket 实时推送**: 基于 Socket.IO 的实时数据推送
- **TypeScript**: 完整的类型安全支持
- **数据库支持**: MongoDB + Redis 数据存储
- **系统监控**: 实时系统性能监控
- **日志系统**: 基于 Winston 的完整日志记录
- **错误处理**: 完善的错误处理和异常捕获
- **速率限制**: API 请求频率限制
- **数据验证**: 基于 Joi 的请求参数验证
- **模拟数据**: 开发环境模拟数据支持

## 📋 API 接口

### 舆情数据接口
- `GET /api/sentiment/realtime` - 获取实时数据
- `GET /api/sentiment/statistics` - 获取统计数据
- `GET /api/sentiment/hot-topics` - 获取热点话题
- `GET /api/sentiment/keywords` - 获取关键词
- `GET /api/sentiment/time-series` - 获取时间序列数据
- `GET /api/sentiment/locations` - 获取地理位置数据
- `GET /api/sentiment/recent-posts` - 获取最新帖子
- `GET /api/sentiment/list` - 获取舆情数据列表
- `POST /api/sentiment/search` - 搜索舆情内容

### 系统接口
- `GET /api/system/status` - 获取系统状态
- `GET /api/system/performance` - 获取性能指标
- `GET /api/system/health` - 健康检查
- `GET /api/system/info` - 获取系统信息

### WebSocket 事件
- `connected` - 连接成功
- `data:update` - 数据更新
- `data:alert` - 系统警告
- `data:heartbeat` - 心跳消息

## 🛠️ 技术栈

- **运行时**: Node.js >= 16
- **框架**: Express.js
- **语言**: TypeScript
- **WebSocket**: Socket.IO
- **数据库**: MongoDB + Redis
- **日志**: Winston
- **验证**: Joi
- **定时任务**: node-cron
- **系统监控**: systeminformation

## 📦 安装和运行

### 环境要求
- Node.js >= 16
- MongoDB (可选，有模拟数据)
- Redis (可选)

### 安装依赖
```bash
cd backend
npm install
```

### 环境配置
复制并修改环境配置文件：
```bash
cp .env.development .env
```

### 开发环境运行
```bash
npm run dev
# 或使用启动脚本
./start.sh
```

### 构建生产版本
```bash
npm run build
```

### 生产环境运行
```bash
npm start
```

## 📁 项目结构

```
backend/
├── src/
│   ├── config/             # 配置文件
│   │   ├── index.ts        # 主配置
│   │   ├── logger.ts       # 日志配置
│   │   └── database.ts     # 数据库配置
│   ├── controllers/        # 控制器
│   │   ├── sentimentController.ts
│   │   └── systemController.ts
│   ├── services/           # 服务层
│   │   ├── sentimentService.ts
│   │   ├── systemService.ts
│   │   ├── websocketService.ts
│   │   └── dataGeneratorService.ts
│   ├── models/             # 数据模型
│   │   ├── SentimentModel.ts
│   │   └── HotTopicModel.ts
│   ├── middleware/         # 中间件
│   │   ├── validation.ts
│   │   ├── rateLimit.ts
│   │   └── errorHandler.ts
│   ├── routes/             # 路由
│   │   ├── sentimentRoutes.ts
│   │   ├── systemRoutes.ts
│   │   └── index.ts
│   ├── types/              # 类型定义
│   │   └── index.ts
│   └── server.ts           # 服务器入口
├── logs/                   # 日志文件
├── .env                    # 环境变量
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript 配置
└── README.md               # 说明文档
```

## ⚙️ 配置说明

### 环境变量
```bash
# 服务器配置
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/weibo_sentiment
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:3001

# 日志配置
LOG_LEVEL=info
LOG_DIR=logs

# WebSocket 配置
WS_CORS_ORIGIN=http://localhost:3001

# 数据刷新间隔
DATA_REFRESH_INTERVAL=30000
SYSTEM_MONITOR_INTERVAL=5000

# 模拟数据配置
ENABLE_MOCK_DATA=true
MOCK_DATA_INTERVAL=10000
```

## 🔧 开发特性

### 模拟数据
开发环境自动启用模拟数据生成，无需真实数据库即可运行。

### 实时数据推送
通过 WebSocket 实时推送以下数据：
- 统计数据更新
- 热点话题变化
- 关键词统计
- 时间序列数据
- 地理位置分布
- 新帖子通知
- 系统警告

### 系统监控
自动监控系统性能指标：
- CPU 使用率
- 内存使用率
- 磁盘使用率
- 网络流量
- 连接客户端数量

### 日志系统
完整的日志记录：
- 按日期轮转
- 不同级别分离
- 错误堆栈跟踪
- 请求日志记录

## 🚀 部署

### Docker 部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 8080
CMD ["node", "dist/server.js"]
```

### PM2 部署
```bash
npm install -g pm2
npm run build
pm2 start dist/server.js --name "sentiment-backend"
```

## 📊 性能优化

- 请求速率限制
- 数据库连接池
- Redis 缓存
- 响应压缩
- 静态资源优化

## 🔒 安全特性

- Helmet 安全头
- CORS 跨域保护
- 请求参数验证
- 错误信息过滤
- 速率限制保护

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

MIT License
